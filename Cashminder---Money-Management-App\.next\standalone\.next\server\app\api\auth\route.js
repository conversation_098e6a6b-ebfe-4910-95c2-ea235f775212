(()=>{var e={};e.id=188,e.ids=[188],e.modules={2815:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>b,serverHooks:()=>w,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>S});var n={};r.r(n),r.d(n,{POST:()=>E});var s=r(96559),i=r(48088),o=r(37719),a=r(32190),l=r(75745),u=r(17063),c=r(73944),f=r(82598),p=r(43205),d=r.n(p);let h=process.env.JWT_SECRET||"your-secret-key-should-be-in-env-variables";function m(e){return d().sign(e,h,{expiresIn:"7d"})}var y=r(99933);async function g(e){let t=[{name:"Food & Dining",color:"#FF5733",icon:"utensils",type:"expense"},{name:"Transportation",color:"#33A1FF",icon:"car",type:"expense"},{name:"Housing",color:"#33FF57",icon:"home",type:"expense"},{name:"Entertainment",color:"#A133FF",icon:"film",type:"expense"},{name:"Shopping",color:"#FF33A1",icon:"shopping-bag",type:"expense"},{name:"Utilities",color:"#33FFA1",icon:"bolt",type:"expense"},{name:"Healthcare",color:"#FF3357",icon:"medkit",type:"expense"},{name:"Personal Care",color:"#5733FF",icon:"user",type:"expense"},{name:"Salary",color:"#57FF33",icon:"briefcase",type:"income"},{name:"Investments",color:"#FFA133",icon:"chart-line",type:"income"},{name:"Gifts",color:"#A1FF33",icon:"gift",type:"income"},{name:"Other Income",color:"#33FFF5",icon:"dollar-sign",type:"income"}],r=t.map(t=>new c.A({userId:e,name:t.name,color:t.color,icon:t.icon,type:t.type,isDefault:!0}).save());await Promise.all(r),console.log(`Created ${t.length} default categories for user ${e}`)}async function E(e){try{let{email:t,password:r,name:n,mode:s}=await e.json();if(!t)return a.NextResponse.json({error:"Email is required"},{status:400});if(!r)return a.NextResponse.json({error:"Password is required"},{status:400});if(await (0,l.A)(),"signup"===s){if(await u.A.findOne({email:t}))return a.NextResponse.json({error:"User already exists"},{status:400});let e=new u.A({email:t,password:r,name:n||t.split("@")[0],isNewUser:!0,isVerified:!0,role:"user",preferences:{currency:"USD",theme:"light",language:"en",notifications:!0},stats:{totalTransactions:0,totalSavings:0,streakDays:0,lastActive:new Date}});await e.save(),console.log(`New user created: ${e._id} (${t})`),await g(e._id);let s=m({userId:e._id.toString(),email:e.email,name:e.name});return(await (0,y.U)()).set({name:"auth_token",value:s,httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}),a.NextResponse.json({success:!0,user:{id:e._id,email:e.email,name:e.name,isNewUser:e.isNewUser,role:e.role,preferences:e.preferences},token:s})}{let e=await u.A.findOne({email:t});if(!e||!await (0,f.b)(r,e.password))return a.NextResponse.json({error:"Invalid email or password"},{status:401});e.lastLogin=new Date,e.stats.lastActive=new Date,await e.save(),console.log(`User logged in: ${e._id} (${t})`);let n=m({userId:e._id.toString(),email:e.email,name:e.name});return(await (0,y.U)()).set({name:"auth_token",value:n,httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}),a.NextResponse.json({success:!0,user:{id:e._id,email:e.email,name:e.name,isNewUser:e.isNewUser,role:e.role,preferences:e.preferences},token:n})}}catch(e){return console.error("Auth error:",e),a.NextResponse.json({success:!1,error:"Authentication failed",details:e instanceof Error?e.message:String(e)},{status:500})}}r(86280),r(73913);let b=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/route",pathname:"/api/auth",filename:"route",bundlePath:"app/api/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\api\\auth\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:v,workUnitAsyncStorage:S,serverHooks:w}=b;function R(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:S})}},2843:e=>{class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3706:(e,t,r)=>{let n=/\s+/g;class s{constructor(e,t){if(t=o(t),e instanceof s){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;return new s(e.raw,t)}if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(n," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!g(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&E(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&y))+":"+e,r=i.get(t);if(r)return r;let n=this.options.loose,s=n?c[f.HYPHENRANGELOOSE]:c[f.HYPHENRANGE];l("hyphen replace",e=e.replace(s,T(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[f.COMPARATORTRIM],p)),l("tilde trim",e=e.replace(c[f.TILDETRIM],d)),l("caret trim",e=e.replace(c[f.CARETTRIM],h));let o=e.split(" ").map(e=>v(e,this.options)).join(" ").split(/\s+/).map(e=>j(e,this.options));n&&(o=o.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[f.COMPARATORLOOSE])))),l("range list",o);let u=new Map;for(let e of o.map(e=>new a(e,this.options))){if(g(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let E=[...u.values()];return i.set(t,E),E}intersects(e,t){if(!(e instanceof s))throw TypeError("a Range is required");return this.set.some(r=>b(r,t)&&e.set.some(e=>b(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(P(this.set[t],e,this.options))return!0;return!1}}e.exports=s;let i=new(r(2843)),o=r(98300),a=r(14239),l=r(38267),u=r(64487),{safeRe:c,t:f,comparatorTrimReplace:p,tildeTrimReplace:d,caretTrimReplace:h}=r(26515),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:y}=r(32397),g=e=>"<0.0.0-0"===e.value,E=e=>""===e.value,b=(e,t)=>{let r=!0,n=e.slice(),s=n.pop();for(;r&&n.length;)r=n.every(e=>s.intersects(e,t)),s=n.pop();return r},v=(e,t)=>(l("comp",e,t),l("caret",e=$(e,t)),l("tildes",e=w(e,t)),l("xrange",e=A(e,t)),l("stars",e=x(e,t)),e),S=e=>!e||"x"===e.toLowerCase()||"*"===e,w=(e,t)=>e.trim().split(/\s+/).map(e=>R(e,t)).join(" "),R=(e,t)=>{let r=t.loose?c[f.TILDELOOSE]:c[f.TILDE];return e.replace(r,(t,r,n,s,i)=>{let o;return l("tilde",e,t,r,n,s,i),S(r)?o="":S(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:S(s)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:i?(l("replaceTilde pr",i),o=`>=${r}.${n}.${s}-${i} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,l("tilde return",o),o})},$=(e,t)=>e.trim().split(/\s+/).map(e=>O(e,t)).join(" "),O=(e,t)=>{l("caret",e,t);let r=t.loose?c[f.CARETLOOSE]:c[f.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,(t,r,s,i,o)=>{let a;return l("caret",e,t,r,s,i,o),S(r)?a="":S(s)?a=`>=${r}.0.0${n} <${+r+1}.0.0-0`:S(i)?a="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===r?"0"===s?`>=${r}.${s}.${i}-${o} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}-${o} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i}-${o} <${+r+1}.0.0-0`):(l("no pr"),a="0"===r?"0"===s?`>=${r}.${s}.${i}${n} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i} <${+r+1}.0.0-0`),l("caret return",a),a})},A=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>I(e,t)).join(" ")),I=(e,t)=>{e=e.trim();let r=t.loose?c[f.XRANGELOOSE]:c[f.XRANGE];return e.replace(r,(r,n,s,i,o,a)=>{l("xRange",e,r,n,s,i,o,a);let u=S(s),c=u||S(i),f=c||S(o);return"="===n&&f&&(n=""),a=t.includePrerelease?"-0":"",u?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&f?(c&&(i=0),o=0,">"===n?(n=">=",c?(s=+s+1,i=0):i=+i+1,o=0):"<="===n&&(n="<",c?s=+s+1:i=+i+1),"<"===n&&(a="-0"),r=`${n+s}.${i}.${o}${a}`):c?r=`>=${s}.0.0${a} <${+s+1}.0.0-0`:f&&(r=`>=${s}.${i}.0${a} <${s}.${+i+1}.0-0`),l("xRange return",r),r})},x=(e,t)=>(l("replaceStars",e,t),e.trim().replace(c[f.STAR],"")),j=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(c[t.includePrerelease?f.GTE0PRE:f.GTE0],"")),T=e=>(t,r,n,s,i,o,a,l,u,c,f,p)=>(r=S(n)?"":S(s)?`>=${n}.0.0${e?"-0":""}`:S(i)?`>=${n}.${s}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`,l=S(u)?"":S(c)?`<${+u+1}.0.0-0`:S(f)?`<${u}.${+c+1}.0-0`:p?`<=${u}.${c}.${f}-${p}`:e?`<${u}.${c}.${+f+1}-0`:`<=${l}`,`${r} ${l}`.trim()),P=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==a.ANY&&e[r].semver.prerelease.length>0){let n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},4352:(e,t,r)=>{var n=r(45158).Buffer,s=r(89019),i=r(78218),o=r(27910),a=r(9138),l=r(28354),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function c(e){var t=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(n.from(t,"base64").toString("binary"))}function f(e){return e.split(".")[2]}function p(e){return u.test(e)&&!!c(e)}function d(e,t,r){if(!t){var n=Error("Missing algorithm parameter for jws.verify");throw n.code="MISSING_ALGORITHM",n}var s=f(e=a(e)),o=e.split(".",2).join(".");return i(t).verify(o,s,r)}function h(e,t){if(t=t||{},!p(e=a(e)))return null;var r,s,i=c(e);if(!i)return null;var o=(r=r||"utf8",s=e.split(".")[1],n.from(s,"base64").toString(r));return("JWT"===i.typ||t.json)&&(o=JSON.parse(o,t.encoding)),{header:i,payload:o,signature:f(e)}}function m(e){var t=new s((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new s(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(m,o),m.prototype.verify=function(){try{var e=d(this.signature.buffer,this.algorithm,this.key.buffer),t=h(this.signature.buffer,this.encoding);return this.emit("done",e,t),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},m.decode=h,m.isValid=p,m.verify=d,e.exports=m},7110:(e,t,r)=>{let n=r(58361);e.exports=(e,t)=>{let r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},8536:(e,t,r)=>{let n=r(24800);e.exports=(e,t)=>e.sort((e,r)=>n(r,e,t))},9138:(e,t,r)=>{var n=r(79428).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||n.isBuffer(e)?e.toString():JSON.stringify(e)}},9985:(e,t,r)=>{var n=r(45992),s=function(e,t){n.call(this,e),this.name="TokenExpiredError",this.expiredAt=t};s.prototype=Object.create(n.prototype),s.prototype.constructor=s,e.exports=s},10212:(e,t,r)=>{var n=r(71336),s=r(4352);t.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],t.sign=n.sign,t.verify=s.verify,t.decode=s.decode,t.isValid=s.isValid,t.createSign=function(e){return new n(e)},t.createVerify=function(e){return new s(e)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11337:(e,t,r)=>{let n=r(3706),s=r(14239),{ANY:i}=s,o=r(42679),a=r(33877),l=[new s(">=0.0.0-0")],u=[new s(">=0.0.0")],c=(e,t,r)=>{let n,s,c,d,h,m,y;if(e===t)return!0;if(1===e.length&&e[0].semver===i){if(1===t.length&&t[0].semver===i)return!0;e=r.includePrerelease?l:u}if(1===t.length&&t[0].semver===i){if(r.includePrerelease)return!0;t=u}let g=new Set;for(let t of e)">"===t.operator||">="===t.operator?n=f(n,t,r):"<"===t.operator||"<="===t.operator?s=p(s,t,r):g.add(t.semver);if(g.size>1||n&&s&&((c=a(n.semver,s.semver,r))>0||0===c&&(">="!==n.operator||"<="!==s.operator)))return null;for(let e of g){if(n&&!o(e,String(n),r)||s&&!o(e,String(s),r))return null;for(let n of t)if(!o(e,String(n),r))return!1;return!0}let E=!!s&&!r.includePrerelease&&!!s.semver.prerelease.length&&s.semver,b=!!n&&!r.includePrerelease&&!!n.semver.prerelease.length&&n.semver;for(let e of(E&&1===E.prerelease.length&&"<"===s.operator&&0===E.prerelease[0]&&(E=!1),t)){if(y=y||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,n){if(b&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===b.major&&e.semver.minor===b.minor&&e.semver.patch===b.patch&&(b=!1),">"===e.operator||">="===e.operator){if((d=f(n,e,r))===e&&d!==n)return!1}else if(">="===n.operator&&!o(n.semver,String(e),r))return!1}if(s){if(E&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===E.major&&e.semver.minor===E.minor&&e.semver.patch===E.patch&&(E=!1),"<"===e.operator||"<="===e.operator){if((h=p(s,e,r))===e&&h!==s)return!1}else if("<="===s.operator&&!o(s.semver,String(e),r))return!1}if(!e.operator&&(s||n)&&0!==c)return!1}return(!n||!m||!!s||0===c)&&(!s||!y||!!n||0===c)&&!b&&!E&&!0},f=(e,t,r)=>{if(!e)return t;let n=a(e.semver,t.semver,r);return n>0?e:n<0?t:">"===t.operator&&">="===e.operator?t:e},p=(e,t,r)=>{if(!e)return t;let n=a(e.semver,t.semver,r);return n<0?e:n>0?t:"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(let n of e.set){for(let e of t.set){let t=c(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},14239:(e,t,r)=>{let n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=i(t),e instanceof s){if(!!t.loose===e.loose)return e;e=e.value}u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new c(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new f(e.value,t).test(this.value):""===e.operator?""===e.value||new f(this.value,t).test(e.semver):!((t=i(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=s;let i=r(98300),{safeRe:o,t:a}=r(26515),l=r(84450),u=r(38267),c=r(64487),f=r(3706)},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(56037),s=r.n(n),i=r(82598);let o=new n.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,match:[/^\S+@\S+\.\S+$/,"Please enter a valid email address"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters long"]},name:{type:String,trim:!0},avatar:{type:String},isNewUser:{type:Boolean,default:!0},role:{type:String,enum:["user","admin"],default:"user"},isVerified:{type:Boolean,default:!1},verificationToken:String,resetPasswordToken:String,resetPasswordExpires:Date,lastLogin:{type:Date},preferences:{currency:{type:String,default:"USD"},theme:{type:String,default:"light"},language:{type:String,default:"en"},notifications:{type:Boolean,default:!0},dashboardLayout:{type:String}},profile:{bio:String,location:String,website:String,phone:String},stats:{totalTransactions:{type:Number,default:0},totalSavings:{type:Number,default:0},streakDays:{type:Number,default:0},lastActive:{type:Date,default:Date.now}}},{timestamps:!0});o.pre("save",async function(e){if(!this.isModified("password"))return e();try{this.password=await (0,i.E)(this.password),e()}catch(t){e(t)}});let a=s().models.User||s().model("User",o)},17950:(e,t,r)=>{let n=r(33877);e.exports=(e,t)=>n(e,t,!0)},20938:(e,t,r)=>{let n=r(3706);e.exports=(e,t)=>new n(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},22544:e=>{var t,r,n=Object.prototype,s=Function.prototype.toString,i=n.hasOwnProperty,o=s.call(Object),a=n.toString,l=(t=Object.getPrototypeOf,r=Object,function(e){return t(r(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=l(e);if(null===t)return!0;var r=i.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&s.call(r)==o}},22716:e=>{var t=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==t.call(e)}},22893:(e,t,r)=>{let n=r(43528);e.exports=(e,t,r)=>n(e,t,"<",r)},24303:(e,t,r)=>{let n=r(64487),s=r(3706);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!i||1===o.compare(e))&&(o=new n(i=e,r))}),i}},24800:(e,t,r)=>{let n=r(64487);e.exports=(e,t,r)=>{let s=new n(e,r),i=new n(t,r);return s.compare(i)||s.compareBuild(i)}},25388:e=>{"use strict";function t(e){return(e/8|0)+ +(e%8!=0)}var r={ES256:t(256),ES384:t(384),ES512:t(521)};e.exports=function(e){var t=r[e];if(t)return t;throw Error('Unknown algorithm "'+e+'"')}},26515:(e,t,r)=>{let{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:i}=r(32397),o=r(38267),a=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],c=t.safeSrc=[],f=t.t={},p=0,d="[a-zA-Z0-9-]",h=[["\\s",1],["\\d",i],[d,s]],m=e=>{for(let[t,r]of h)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},y=(e,t,r)=>{let n=m(t),s=p++;o(e,s,t),f[e]=s,u[s]=t,c[s]=n,a[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(n,r?"g":void 0)};y("NUMERICIDENTIFIER","0|[1-9]\\d*"),y("NUMERICIDENTIFIERLOOSE","\\d+"),y("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${d}*`),y("MAINVERSION",`(${u[f.NUMERICIDENTIFIER]})\\.(${u[f.NUMERICIDENTIFIER]})\\.(${u[f.NUMERICIDENTIFIER]})`),y("MAINVERSIONLOOSE",`(${u[f.NUMERICIDENTIFIERLOOSE]})\\.(${u[f.NUMERICIDENTIFIERLOOSE]})\\.(${u[f.NUMERICIDENTIFIERLOOSE]})`),y("PRERELEASEIDENTIFIER",`(?:${u[f.NUMERICIDENTIFIER]}|${u[f.NONNUMERICIDENTIFIER]})`),y("PRERELEASEIDENTIFIERLOOSE",`(?:${u[f.NUMERICIDENTIFIERLOOSE]}|${u[f.NONNUMERICIDENTIFIER]})`),y("PRERELEASE",`(?:-(${u[f.PRERELEASEIDENTIFIER]}(?:\\.${u[f.PRERELEASEIDENTIFIER]})*))`),y("PRERELEASELOOSE",`(?:-?(${u[f.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[f.PRERELEASEIDENTIFIERLOOSE]})*))`),y("BUILDIDENTIFIER",`${d}+`),y("BUILD",`(?:\\+(${u[f.BUILDIDENTIFIER]}(?:\\.${u[f.BUILDIDENTIFIER]})*))`),y("FULLPLAIN",`v?${u[f.MAINVERSION]}${u[f.PRERELEASE]}?${u[f.BUILD]}?`),y("FULL",`^${u[f.FULLPLAIN]}$`),y("LOOSEPLAIN",`[v=\\s]*${u[f.MAINVERSIONLOOSE]}${u[f.PRERELEASELOOSE]}?${u[f.BUILD]}?`),y("LOOSE",`^${u[f.LOOSEPLAIN]}$`),y("GTLT","((?:<|>)?=?)"),y("XRANGEIDENTIFIERLOOSE",`${u[f.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),y("XRANGEIDENTIFIER",`${u[f.NUMERICIDENTIFIER]}|x|X|\\*`),y("XRANGEPLAIN",`[v=\\s]*(${u[f.XRANGEIDENTIFIER]})(?:\\.(${u[f.XRANGEIDENTIFIER]})(?:\\.(${u[f.XRANGEIDENTIFIER]})(?:${u[f.PRERELEASE]})?${u[f.BUILD]}?)?)?`),y("XRANGEPLAINLOOSE",`[v=\\s]*(${u[f.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[f.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[f.XRANGEIDENTIFIERLOOSE]})(?:${u[f.PRERELEASELOOSE]})?${u[f.BUILD]}?)?)?`),y("XRANGE",`^${u[f.GTLT]}\\s*${u[f.XRANGEPLAIN]}$`),y("XRANGELOOSE",`^${u[f.GTLT]}\\s*${u[f.XRANGEPLAINLOOSE]}$`),y("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),y("COERCE",`${u[f.COERCEPLAIN]}(?:$|[^\\d])`),y("COERCEFULL",u[f.COERCEPLAIN]+`(?:${u[f.PRERELEASE]})?`+`(?:${u[f.BUILD]})?`+"(?:$|[^\\d])"),y("COERCERTL",u[f.COERCE],!0),y("COERCERTLFULL",u[f.COERCEFULL],!0),y("LONETILDE","(?:~>?)"),y("TILDETRIM",`(\\s*)${u[f.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",y("TILDE",`^${u[f.LONETILDE]}${u[f.XRANGEPLAIN]}$`),y("TILDELOOSE",`^${u[f.LONETILDE]}${u[f.XRANGEPLAINLOOSE]}$`),y("LONECARET","(?:\\^)"),y("CARETTRIM",`(\\s*)${u[f.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",y("CARET",`^${u[f.LONECARET]}${u[f.XRANGEPLAIN]}$`),y("CARETLOOSE",`^${u[f.LONECARET]}${u[f.XRANGEPLAINLOOSE]}$`),y("COMPARATORLOOSE",`^${u[f.GTLT]}\\s*(${u[f.LOOSEPLAIN]})$|^$`),y("COMPARATOR",`^${u[f.GTLT]}\\s*(${u[f.FULLPLAIN]})$|^$`),y("COMPARATORTRIM",`(\\s*)${u[f.GTLT]}\\s*(${u[f.LOOSEPLAIN]}|${u[f.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",y("HYPHENRANGE",`^\\s*(${u[f.XRANGEPLAIN]})\\s+-\\s+(${u[f.XRANGEPLAIN]})\\s*$`),y("HYPHENRANGELOOSE",`^\\s*(${u[f.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[f.XRANGEPLAINLOOSE]})\\s*$`),y("STAR","(<|>)?=?\\s*\\*"),y("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),y("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},27290:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>0!==n(e,t,r)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28584:(e,t,r)=>{let n=r(26515),s=r(32397),i=r(64487),o=r(78668),a=r(58361),l=r(35444),u=r(73051),c=r(90726),f=r(93419),p=r(42467),d=r(40999),h=r(78172),m=r(7110),y=r(33877),g=r(86605),E=r(17950),b=r(24800),v=r(31904),S=r(8536),w=r(42699),R=r(40720),$=r(73438),O=r(27290),A=r(44156),I=r(60301),x=r(84450),j=r(44449),T=r(14239),P=r(3706),N=r(42679),L=r(20938),k=r(43441),C=r(24303),D=r(36686),_=r(31385),M=r(43528),F=r(43900),U=r(22893),B=r(71505);e.exports={parse:a,valid:l,clean:u,inc:c,diff:f,major:p,minor:d,patch:h,prerelease:m,compare:y,rcompare:g,compareLoose:E,compareBuild:b,sort:v,rsort:S,gt:w,lt:R,eq:$,neq:O,gte:A,lte:I,cmp:x,coerce:j,Comparator:T,Range:P,satisfies:N,toComparators:L,maxSatisfying:k,minSatisfying:C,minVersion:D,validRange:_,outside:M,gtr:F,ltr:U,intersects:B,simplifyRange:r(77860),subset:r(11337),SemVer:i,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30937:e=>{var t=1/0,r=0/0,n=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var c,f,p;return"number"==typeof e&&e==(p=(f=(c=e)?(c=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var f=i.test(e);return f||o.test(e)?a(e.slice(2),f?2:8):s.test(e)?r:+e}(c))===t||c===-t?(c<0?-1:1)*17976931348623157e292:c==c?c:0:0===c?c:0)%1,f==f?p?f-p:f:0)}},31385:(e,t,r)=>{let n=r(3706);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},31904:(e,t,r)=>{let n=r(24800);e.exports=(e,t)=>e.sort((e,r)=>n(e,r,t))},32397:e=>{e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},33523:e=>{var t=Object.prototype.toString;e.exports=function(e){var r;return!0===e||!1===e||!!(r=e)&&"object"==typeof r&&"[object Boolean]"==t.call(e)}},33877:(e,t,r)=>{let n=r(64487);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},34072:e=>{function t(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var n,s,i,o,a=typeof e;if("string"===a&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===a&&isFinite(e)){return r.long?(s=Math.abs(n=e))>=864e5?t(n,s,864e5,"day"):s>=36e5?t(n,s,36e5,"hour"):s>=6e4?t(n,s,6e4,"minute"):s>=1e3?t(n,s,1e3,"second"):n+" ms":(o=Math.abs(i=e))>=864e5?Math.round(i/864e5)+"d":o>=36e5?Math.round(i/36e5)+"h":o>=6e4?Math.round(i/6e4)+"m":o>=1e3?Math.round(i/1e3)+"s":i+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},35444:(e,t,r)=>{let n=r(58361);e.exports=(e,t)=>{let r=n(e,t);return r?r.version:null}},35792:(e,t,r)=>{e.exports=r(28584).satisfies(process.version,">=15.7.0")},36686:(e,t,r)=>{let n=r(64487),s=r(3706),i=r(42699);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r)||(r=new n("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let s=e.set[t],o=null;s.forEach(e=>{let t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!o||i(t,o))&&(o=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!r||i(r,o))&&(r=o)}return r&&e.test(r)?r:null}},38267:e=>{e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},38466:(e,t,r)=>{e.exports=r(28584).satisfies(process.version,">=16.9.0")},38792:e=>{var t,r,n=1/0,s=0/0,i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,c=parseInt;function f(e){return e!=e}var p=Object.prototype,d=p.hasOwnProperty,h=p.toString,m=p.propertyIsEnumerable,y=(t=Object.keys,r=Object,function(e){return t(r(e))}),g=Math.max,E=Array.isArray;function b(e){var t,r,n;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=0x1fffffffffffff&&"[object Function]"!=(n=v(r=e)?h.call(r):"")&&"[object GeneratorFunction]"!=n}function v(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function S(e){return!!e&&"object"==typeof e}e.exports=function(e,t,r,w){e=b(e)?e:function(e){return e?function(e,t){for(var r=-1,n=e?e.length:0,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}(b(e)?function(e,t){var r,n,s,i,o=E(e)||S(n=r=e)&&b(n)&&d.call(r,"callee")&&(!m.call(r,"callee")||"[object Arguments]"==h.call(r))?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],a=o.length,l=!!a;for(var c in e){d.call(e,c)&&!(l&&("length"==c||(s=c,(i=null==(i=a)?0x1fffffffffffff:i)&&("number"==typeof s||u.test(s))&&s>-1&&s%1==0&&s<i)))&&o.push(c)}return o}(e):function(e){if(r=(t=e)&&t.constructor,t!==("function"==typeof r&&r.prototype||p))return y(e);var t,r,n=[];for(var s in Object(e))d.call(e,s)&&"constructor"!=s&&n.push(s);return n}(e),function(t){return e[t]}):[]}(e),r=r&&!w?(O=($=(R=r)?(R=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||S(t)&&"[object Symbol]"==h.call(t))return s;if(v(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=v(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var n=a.test(e);return n||l.test(e)?c(e.slice(2),n?2:8):o.test(e)?s:+e}(R))===n||R===-n?(R<0?-1:1)*17976931348623157e292:R==R?R:0:0===R?R:0)%1,$==$?O?$-O:$:0):0;var R,$,O,A,I=e.length;return r<0&&(r=g(I+r,0)),"string"==typeof(A=e)||!E(A)&&S(A)&&"[object String]"==h.call(A)?r<=I&&e.indexOf(t,r)>-1:!!I&&function(e,t,r){if(t!=t)return function(e,t,r,n){for(var s=e.length,i=r+-1;++i<s;)if(t(e[i],i,e))return i;return -1}(e,f,r);for(var n=r-1,s=e.length;++n<s;)if(e[n]===t)return n;return -1}(e,t,r)>-1}},40656:(e,t,r)=>{let n=r(77088),s=r(91236),i=r(96810),o=r(10212),a=r(38792),l=r(33523),u=r(30937),c=r(22716),f=r(22544),p=r(74148),d=r(83488),{KeyObject:h,createSecretKey:m,createPrivateKey:y}=r(55511),g=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];s&&g.splice(3,0,"PS256","PS384","PS512");let E={expiresIn:{isValid:function(e){return u(e)||p(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||p(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return p(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,g),message:'"algorithm" must be a valid string enum value'},header:{isValid:f,message:'"header" must be an object'},encoding:{isValid:p,message:'"encoding" must be a string'},issuer:{isValid:p,message:'"issuer" must be a string'},subject:{isValid:p,message:'"subject" must be a string'},jwtid:{isValid:p,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:p,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},b={iat:{isValid:c,message:'"iat" should be a number of seconds'},exp:{isValid:c,message:'"exp" should be a number of seconds'},nbf:{isValid:c,message:'"nbf" should be a number of seconds'}};function v(e,t,r,n){if(!f(r))throw Error('Expected "'+n+'" to be a plain object.');Object.keys(r).forEach(function(s){let i=e[s];if(!i){if(!t)throw Error('"'+s+'" is not allowed in "'+n+'"');return}if(!i.isValid(r[s]))throw Error(i.message)})}let S={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},w=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,t,r,s){var a,l;"function"==typeof r?(s=r,r={}):r=r||{};let u="object"==typeof e&&!Buffer.isBuffer(e),c=Object.assign({alg:r.algorithm||"HS256",typ:u?"JWT":void 0,kid:r.keyid},r.header);function f(e){if(s)return s(e);throw e}if(!t&&"none"!==r.algorithm)return f(Error("secretOrPrivateKey must have a value"));if(null!=t&&!(t instanceof h))try{t=y(t)}catch(e){try{t=m("string"==typeof t?Buffer.from(t):t)}catch(e){return f(Error("secretOrPrivateKey is not valid key material"))}}if(c.alg.startsWith("HS")&&"secret"!==t.type)return f(Error(`secretOrPrivateKey must be a symmetric key when using ${c.alg}`));if(/^(?:RS|PS|ES)/.test(c.alg)){if("private"!==t.type)return f(Error(`secretOrPrivateKey must be an asymmetric key when using ${c.alg}`));if(!r.allowInsecureKeySizes&&!c.alg.startsWith("ES")&&void 0!==t.asymmetricKeyDetails&&t.asymmetricKeyDetails.modulusLength<2048)return f(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`))}if(void 0===e)return f(Error("payload is required"));if(u){try{a=e,v(b,!0,a,"payload")}catch(e){return f(e)}r.mutatePayload||(e=Object.assign({},e))}else{let t=w.filter(function(e){return void 0!==r[e]});if(t.length>0)return f(Error("invalid "+t.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==r.expiresIn)return f(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==r.notBefore)return f(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=r,v(E,!1,l,"options")}catch(e){return f(e)}if(!r.allowInvalidAsymmetricKeyTypes)try{i(c.alg,t)}catch(e){return f(e)}let p=e.iat||Math.floor(Date.now()/1e3);if(r.noTimestamp?delete e.iat:u&&(e.iat=p),void 0!==r.notBefore){try{e.nbf=n(r.notBefore,p)}catch(e){return f(e)}if(void 0===e.nbf)return f(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==r.expiresIn&&"object"==typeof e){try{e.exp=n(r.expiresIn,p)}catch(e){return f(e)}if(void 0===e.exp)return f(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(S).forEach(function(t){let n=S[t];if(void 0!==r[t]){if(void 0!==e[n])return f(Error('Bad "options.'+t+'" option. The payload already has an "'+n+'" property.'));e[n]=r[t]}});let g=r.encoding||"utf8";if("function"==typeof s)s=s&&d(s),o.createSign({header:c,privateKey:t,payload:e,encoding:g}).once("error",s).once("done",function(e){if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&e.length<256)return s(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`));s(null,e)});else{let n=o.sign({header:c,payload:e,secret:t,encoding:g});if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&n.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`);return n}}},40720:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>0>n(e,t,r)},40917:(e,t,r)=>{var n=r(45992),s=function(e,t){n.call(this,e),this.name="NotBeforeError",this.date=t};s.prototype=Object.create(n.prototype),s.prototype.constructor=s,e.exports=s},40999:(e,t,r)=>{let n=r(64487);e.exports=(e,t)=>new n(e,t).minor},42467:(e,t,r)=>{let n=r(64487);e.exports=(e,t)=>new n(e,t).major},42679:(e,t,r)=>{let n=r(3706);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},42699:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>n(e,t,r)>0},43205:(e,t,r)=>{e.exports={decode:r(48915),verify:r(66092),sign:r(40656),JsonWebTokenError:r(45992),NotBeforeError:r(40917),TokenExpiredError:r(9985)}},43441:(e,t,r)=>{let n=r(64487),s=r(3706);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!i||-1===o.compare(e))&&(o=new n(i=e,r))}),i}},43528:(e,t,r)=>{let n=r(64487),s=r(14239),{ANY:i}=s,o=r(3706),a=r(42679),l=r(42699),u=r(40720),c=r(60301),f=r(44156);e.exports=(e,t,r,p)=>{let d,h,m,y,g;switch(e=new n(e,p),t=new o(t,p),r){case">":d=l,h=c,m=u,y=">",g=">=";break;case"<":d=u,h=f,m=l,y="<",g="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,p))return!1;for(let r=0;r<t.set.length;++r){let n=t.set[r],o=null,a=null;if(n.forEach(e=>{e.semver===i&&(e=new s(">=0.0.0")),o=o||e,a=a||e,d(e.semver,o.semver,p)?o=e:m(e.semver,a.semver,p)&&(a=e)}),o.operator===y||o.operator===g||(!a.operator||a.operator===y)&&h(e,a.semver)||a.operator===g&&m(e,a.semver))return!1}return!0}},43900:(e,t,r)=>{let n=r(43528);e.exports=(e,t,r)=>n(e,t,">",r)},44156:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>n(e,t,r)>=0},44449:(e,t,r)=>{let n=r(64487),s=r(58361),{safeRe:i,t:o}=r(26515);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let n;let s=t.includePrerelease?i[o.COERCERTLFULL]:i[o.COERCERTL];for(;(n=s.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&n.index+n[0].length===r.index+r[0].length||(r=n),s.lastIndex=n.index+n[1].length+n[2].length;s.lastIndex=-1}else r=e.match(t.includePrerelease?i[o.COERCEFULL]:i[o.COERCE]);if(null===r)return null;let a=r[2],l=r[3]||"0",u=r[4]||"0",c=t.includePrerelease&&r[5]?`-${r[5]}`:"",f=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${a}.${l}.${u}${c}${f}`,t)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45158:(e,t,r)=>{var n=r(79428),s=n.Buffer;function i(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return s(e,t,r)}s.from&&s.alloc&&s.allocUnsafe&&s.allocUnsafeSlow?e.exports=n:(i(n,t),t.Buffer=o),o.prototype=Object.create(s.prototype),i(s,o),o.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return s(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var n=s(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return n.SlowBuffer(e)}},45992:e=>{var t=function(e,t){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,t&&(this.inner=t)};t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,e.exports=t},48915:(e,t,r)=>{var n=r(10212);e.exports=function(e,t){t=t||{};var r=n.decode(e,t);if(!r)return null;var s=r.payload;if("string"==typeof s)try{var i=JSON.parse(s);null!==i&&"object"==typeof i&&(s=i)}catch(e){}return!0===t.complete?{header:r.header,payload:s,signature:r.signature}:s}},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},58361:(e,t,r)=>{let n=r(64487);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},60301:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>0>=n(e,t,r)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64487:(e,t,r)=>{let n=r(38267),{MAX_LENGTH:s,MAX_SAFE_INTEGER:i}=r(32397),{safeRe:o,safeSrc:a,t:l}=r(26515),u=r(98300),{compareIdentifiers:c}=r(78668);class f{constructor(e,t){if(t=u(t),e instanceof f){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?o[l.LOOSE]:o[l.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>i||this.major<0)throw TypeError("Invalid major version");if(this.minor>i||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>i||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<i)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof f)){if("string"==typeof e&&e===this.version)return 0;e=new f(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof f||(e=new f(e,this.options)),c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(e instanceof f||(e=new f(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;else if(r===s)continue;else return c(r,s)}while(++t)}compareBuild(e){e instanceof f||(e=new f(e,this.options));let t=0;do{let r=this.build[t],s=e.build[t];if(n("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;else if(r===s)continue;else return c(r,s)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=RegExp(`^${this.options.loose?a[l.PRERELEASELOOSE]:a[l.PRERELEASE]}$`),r=`-${t}`.match(e);if(!r||r[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(r);if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===c(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=f},66092:(e,t,r)=>{let n=r(45992),s=r(40917),i=r(9985),o=r(48915),a=r(77088),l=r(96810),u=r(91236),c=r(10212),{KeyObject:f,createSecretKey:p,createPublicKey:d}=r(55511),h=["RS256","RS384","RS512"],m=["ES256","ES384","ES512"],y=["RS256","RS384","RS512"],g=["HS256","HS384","HS512"];u&&(h.splice(h.length,0,"PS256","PS384","PS512"),y.splice(y.length,0,"PS256","PS384","PS512")),e.exports=function(e,t,r,u){let E,b,v;if("function"!=typeof r||u||(u=r,r={}),r||(r={}),r=Object.assign({},r),E=u||function(e,t){if(e)throw e;return t},r.clockTimestamp&&"number"!=typeof r.clockTimestamp)return E(new n("clockTimestamp must be a number"));if(void 0!==r.nonce&&("string"!=typeof r.nonce||""===r.nonce.trim()))return E(new n("nonce must be a non-empty string"));if(void 0!==r.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof r.allowInvalidAsymmetricKeyTypes)return E(new n("allowInvalidAsymmetricKeyTypes must be a boolean"));let S=r.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return E(new n("jwt must be provided"));if("string"!=typeof e)return E(new n("jwt must be a string"));let w=e.split(".");if(3!==w.length)return E(new n("jwt malformed"));try{b=o(e,{complete:!0})}catch(e){return E(e)}if(!b)return E(new n("invalid token"));let R=b.header;if("function"==typeof t){if(!u)return E(new n("verify must be called asynchronous if secret or public key is provided as a callback"));v=t}else v=function(e,r){return r(null,t)};return v(R,function(t,o){let u;if(t)return E(new n("error in secret or public key callback: "+t.message));let v=""!==w[2].trim();if(!v&&o)return E(new n("jwt signature is required"));if(v&&!o)return E(new n("secret or public key must be provided"));if(!v&&!r.algorithms)return E(new n('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof f))try{o=d(o)}catch(e){try{o=p("string"==typeof o?Buffer.from(o):o)}catch(e){return E(new n("secretOrPublicKey is not valid key material"))}}if(r.algorithms||("secret"===o.type?r.algorithms=g:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?r.algorithms=y:"ec"===o.asymmetricKeyType?r.algorithms=m:r.algorithms=h),-1===r.algorithms.indexOf(b.header.alg))return E(new n("invalid algorithm"));if(R.alg.startsWith("HS")&&"secret"!==o.type)return E(new n(`secretOrPublicKey must be a symmetric key when using ${R.alg}`));if(/^(?:RS|PS|ES)/.test(R.alg)&&"public"!==o.type)return E(new n(`secretOrPublicKey must be an asymmetric key when using ${R.alg}`));if(!r.allowInvalidAsymmetricKeyTypes)try{l(R.alg,o)}catch(e){return E(e)}try{u=c.verify(e,b.header.alg,o)}catch(e){return E(e)}if(!u)return E(new n("invalid signature"));let $=b.payload;if(void 0!==$.nbf&&!r.ignoreNotBefore){if("number"!=typeof $.nbf)return E(new n("invalid nbf value"));if($.nbf>S+(r.clockTolerance||0))return E(new s("jwt not active",new Date(1e3*$.nbf)))}if(void 0!==$.exp&&!r.ignoreExpiration){if("number"!=typeof $.exp)return E(new n("invalid exp value"));if(S>=$.exp+(r.clockTolerance||0))return E(new i("jwt expired",new Date(1e3*$.exp)))}if(r.audience){let e=Array.isArray(r.audience)?r.audience:[r.audience];if(!(Array.isArray($.aud)?$.aud:[$.aud]).some(function(t){return e.some(function(e){return e instanceof RegExp?e.test(t):e===t})}))return E(new n("jwt audience invalid. expected: "+e.join(" or ")))}if(r.issuer&&("string"==typeof r.issuer&&$.iss!==r.issuer||Array.isArray(r.issuer)&&-1===r.issuer.indexOf($.iss)))return E(new n("jwt issuer invalid. expected: "+r.issuer));if(r.subject&&$.sub!==r.subject)return E(new n("jwt subject invalid. expected: "+r.subject));if(r.jwtid&&$.jti!==r.jwtid)return E(new n("jwt jwtid invalid. expected: "+r.jwtid));if(r.nonce&&$.nonce!==r.nonce)return E(new n("jwt nonce invalid. expected: "+r.nonce));if(r.maxAge){if("number"!=typeof $.iat)return E(new n("iat required when maxAge is specified"));let e=a(r.maxAge,$.iat);if(void 0===e)return E(new n('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(S>=e+(r.clockTolerance||0))return E(new i("maxAge exceeded",new Date(1e3*e)))}return!0===r.complete?E(null,{header:R,payload:$,signature:b.signature}):E(null,$)})}},71336:(e,t,r)=>{var n=r(45158).Buffer,s=r(89019),i=r(78218),o=r(27910),a=r(9138),l=r(28354);function u(e,t){return n.from(e,t).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function c(e){var t,r,n,s=e.header,o=e.payload,c=e.secret||e.privateKey,f=e.encoding,p=i(s.alg),d=(t=(t=f)||"utf8",r=u(a(s),"binary"),n=u(a(o),t),l.format("%s.%s",r,n)),h=p.sign(d,c);return l.format("%s.%s",d,h)}function f(e){var t=new s(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new s(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(f,o),f.prototype.sign=function(){try{var e=c({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},f.sign=c,e.exports=f},71505:(e,t,r)=>{let n=r(3706);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},73051:(e,t,r)=>{let n=r(58361);e.exports=(e,t)=>{let r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},73438:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>0===n(e,t,r)},73913:(e,t,r)=>{"use strict";let n=r(63033),s=r(29294),i=r(84971),o=r(76926),a=r(80023),l=r(98479),u=new WeakMap;function c(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class f{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){d("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){d("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function d(e){let t=s.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new l.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},73944:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(56037),s=r.n(n);let i=new n.Schema({userId:{type:n.Schema.Types.ObjectId,ref:"User",required:!0},name:{type:String,required:!0},color:{type:String,required:!0},icon:{type:String},type:{type:String,enum:["income","expense"],required:!0},description:{type:String},isDefault:{type:Boolean,default:!1},budget:{type:Number},parentCategory:{type:n.Schema.Types.ObjectId,ref:"Category"}},{timestamps:!0}),o=s().models.Category||s().model("Category",i)},74148:e=>{var t=Object.prototype.toString,r=Array.isArray;e.exports=function(e){var n;return"string"==typeof e||!r(e)&&!!(n=e)&&"object"==typeof n&&"[object String]"==t.call(e)}},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(56037),s=r.n(n);let i=process.env.MONGODB_URI||"";if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let a=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(i).then(e=>(console.log("MongoDB connected successfully"),e)).catch(e=>{throw console.error("MongoDB connection error:",e),e}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(61120));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let i={current:null},o="function"==typeof n.cache?n.cache:e=>e,a=console.warn;function l(e){return function(...t){a(e(...t))}}o(e=>{try{a(i.current)}finally{i.current=null}})},77088:(e,t,r)=>{var n=r(34072);e.exports=function(e,t){var r=t||Math.floor(Date.now()/1e3);if("string"==typeof e){var s=n(e);if(void 0===s)return;return Math.floor(r+s/1e3)}if("number"==typeof e)return r+e}},77860:(e,t,r)=>{let n=r(42679),s=r(33877);e.exports=(e,t,r)=>{let i=[],o=null,a=null,l=e.sort((e,t)=>s(e,t,r));for(let e of l)n(e,t,r)?(a=e,o||(o=e)):(a&&i.push([o,a]),a=null,o=null);o&&i.push([o,null]);let u=[];for(let[e,t]of i)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),f="string"==typeof t.raw?t.raw:String(t);return c.length<f.length?c:t}},78172:(e,t,r)=>{let n=r(64487);e.exports=(e,t)=>new n(e,t).patch},78218:(e,t,r)=>{var n=r(90876),s=r(45158).Buffer,i=r(55511),o=r(81717),a=r(28354),l="secret must be a string or buffer",u="key must be a string or a buffer",c="function"==typeof i.createPublicKey;function f(e){if(!s.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw m(u)}function p(e){if(!s.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw m("key must be a string, a buffer or an object")}function d(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function h(e){var t=4-(e=e.toString()).length%4;if(4!==t)for(var r=0;r<t;++r)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function m(e){var t=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,t))}function y(e){var t;return t=e,!s.isBuffer(t)&&"string"!=typeof t&&(e=JSON.stringify(e)),e}function g(e){return function(t,r){(function(e){if(!s.isBuffer(e)){if("string"!=typeof e){if(!c||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export)throw m(l)}}})(r),t=y(t);var n=i.createHmac("sha"+e,r);return d((n.update(t),n.digest("base64")))}}function E(e){return function(t,r,i){var o=g(e)(t,i);return n(s.from(r),s.from(o))}}function b(e){return function(t,r){p(r),t=y(t);var n=i.createSign("RSA-SHA"+e);return d((n.update(t),n.sign(r,"base64")))}}function v(e){return function(t,r,n){f(n),t=y(t),r=h(r);var s=i.createVerify("RSA-SHA"+e);return s.update(t),s.verify(n,r,"base64")}}function S(e){return function(t,r){p(r),t=y(t);var n=i.createSign("RSA-SHA"+e);return d((n.update(t),n.sign({key:r,padding:i.constants.RSA_PKCS1_PSS_PADDING,saltLength:i.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function w(e){return function(t,r,n){f(n),t=y(t),r=h(r);var s=i.createVerify("RSA-SHA"+e);return s.update(t),s.verify({key:n,padding:i.constants.RSA_PKCS1_PSS_PADDING,saltLength:i.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function R(e){var t=b(e);return function(){var r=t.apply(null,arguments);return o.derToJose(r,"ES"+e)}}function $(e){var t=v(e);return function(r,n,s){return t(r,n=o.joseToDer(n,"ES"+e).toString("base64"),s)}}function O(){return function(){return""}}function A(){return function(e,t){return""===t}}c&&(u+=" or a KeyObject",l+="or a KeyObject"),e.exports=function(e){var t=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!t)throw m('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var r=(t[1]||t[3]).toLowerCase(),n=t[2];return{sign:({hs:g,rs:b,ps:S,es:R,none:O})[r](n),verify:({hs:E,rs:v,ps:w,es:$,none:A})[r](n)}}},78335:()=>{},78668:e=>{let t=/^[0-9]+$/,r=(e,r)=>{let n=t.test(e),s=t.test(r);return n&&s&&(e*=1,r*=1),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},79428:e=>{"use strict";e.exports=require("buffer")},81717:(e,t,r)=>{"use strict";var n=r(45158).Buffer,s=r(25388);function i(e){if(n.isBuffer(e))return e;if("string"==typeof e)return n.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,t,r){for(var n=0;t+n<r&&0===e[t+n];)++n;return e[t+n]>=128&&--n,n}e.exports={derToJose:function(e,t){e=i(e);var r=s(t),o=r+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var c=e[l++];if(a-l-2<c)throw Error('"r" specified length of "'+c+'", only "'+(a-l-2)+'" available');if(o<c)throw Error('"r" specified length of "'+c+'", max of "'+o+'" is acceptable');var f=l;if(l+=c,2!==e[l++])throw Error('Could not find expected "int" for "s"');var p=e[l++];if(a-l!==p)throw Error('"s" specified length of "'+p+'", expected "'+(a-l)+'"');if(o<p)throw Error('"s" specified length of "'+p+'", max of "'+o+'" is acceptable');var d=l;if((l+=p)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var h=r-c,m=r-p,y=n.allocUnsafe(h+c+m+p);for(l=0;l<h;++l)y[l]=0;e.copy(y,l,f+Math.max(-h,0),f+c),l=r;for(var g=l;l<g+m;++l)y[l]=0;return e.copy(y,l,d+Math.max(-m,0),d+p),y=(y=y.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,t){e=i(e);var r=s(t),a=e.length;if(a!==2*r)throw TypeError('"'+t+'" signatures must be "'+2*r+'" bytes, saw "'+a+'"');var l=o(e,0,r),u=o(e,r,e.length),c=r-l,f=r-u,p=2+c+1+1+f,d=p<128,h=n.allocUnsafe((d?2:3)+p),m=0;return h[m++]=48,d?h[m++]=p:(h[m++]=129,h[m++]=255&p),h[m++]=2,h[m++]=c,l<0?(h[m++]=0,m+=e.copy(h,m,0,r)):m+=e.copy(h,m,l,r),h[m++]=2,h[m++]=f,u<0?(h[m++]=0,e.copy(h,m,r)):e.copy(h,m,r+u),h}}},82598:(e,t,r)=>{"use strict";r.d(t,{E:()=>s,b:()=>i});var n=r(85663);async function s(e){return n.Ay.hash(e,10)}async function i(e,t){return n.Ay.compare(e,t)}},83488:e=>{var t=1/0,r=0/0,n=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){return function(e,c){var f,p,d,h;if("function"!=typeof c)throw TypeError("Expected a function");return h=(d=(p=e)?(p=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var f=i.test(e);return f||o.test(e)?a(e.slice(2),f?2:8):s.test(e)?r:+e}(p))===t||p===-t?(p<0?-1:1)*17976931348623157e292:p==p?p:0:0===p?p:0)%1,e=d==d?h?d-h:d:0,function(){return--e>0&&(f=c.apply(this,arguments)),e<=1&&(c=void 0),f}}(2,e)}},84450:(e,t,r)=>{let n=r(73438),s=r(27290),i=r(42699),o=r(44156),a=r(40720),l=r(60301);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,u);case"!=":return s(e,r,u);case">":return i(e,r,u);case">=":return o(e,r,u);case"<":return a(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},86280:(e,t,r)=>{"use strict";let n=r(92584),s=r(29294),i=r(63033),o=r(84971),a=r(80023),l=r(68388),u=r(76926),c=(r(44523),r(8719)),f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function d(e){return"string"==typeof e?`'${e}'`:"..."}let h=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86605:(e,t,r)=>{let n=r(33877);e.exports=(e,t,r)=>n(t,e,r)},89019:(e,t,r)=>{var n=r(45158).Buffer,s=r(27910);function i(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=n.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=n.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}r(28354).inherits(i,s),i.prototype.write=function(e){this.buffer=n.concat([this.buffer,n.from(e)]),this.emit("data",e)},i.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=i},90726:(e,t,r)=>{let n=r(64487);e.exports=(e,t,r,s,i)=>{"string"==typeof r&&(i=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,i).version}catch(e){return null}}},90876:(e,t,r)=>{"use strict";var n=r(79428).Buffer,s=r(79428).SlowBuffer;function i(e,t){if(!n.isBuffer(e)||!n.isBuffer(t)||e.length!==t.length)return!1;for(var r=0,s=0;s<e.length;s++)r|=e[s]^t[s];return 0===r}e.exports=i,i.install=function(){n.prototype.equal=s.prototype.equal=function(e){return i(this,e)}};var o=n.prototype.equal,a=s.prototype.equal;i.restore=function(){n.prototype.equal=o,s.prototype.equal=a}},91236:(e,t,r)=>{e.exports=r(28584).satisfies(process.version,"^6.12.0 || >=8.0.0")},92584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return s}});let n=r(43763);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,s);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,s)},set(t,r,s,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,s,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,a??r,s,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},93419:(e,t,r)=>{let n=r(58361);e.exports=(e,t)=>{let r=n(e,null,!0),s=n(t,null,!0),i=r.compare(s);if(0===i)return null;let o=i>0,a=o?r:s,l=o?s:r,u=!!a.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return r.major!==s.major?c+"major":r.minor!==s.minor?c+"minor":r.patch!==s.patch?c+"patch":"prerelease"}},94069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return p},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return l},appendMutableCookies:function(){return f},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return c},responseCookiesToRequestCookies:function(){return y},wrapWithMutableAccessCheck:function(){return d}});let n=r(23158),s=r(43763),i=r(29294),o=r(63033);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new a}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function f(e,t){let r=c(t);if(0===r.length)return!1;let s=new n.ResponseCookies(e),i=s.getAll();for(let e of r)s.set(e);for(let e of i)s.set(e);return!0}class p{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,l=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return s.ReflectAdapter.get(e,t,r)}}});return c}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return s.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function m(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new a}function y(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},96487:()=>{},96810:(e,t,r)=>{let n=r(35792),s=r(38466),i={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,t){if(!e||!t)return;let r=t.asymmetricKeyType;if(!r)return;let a=i[r];if(!a)throw Error(`Unknown key type "${r}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${r}" key type must be one of: ${a.join(", ")}.`);if(n)switch(r){case"ec":let l=t.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(s){let r=parseInt(e.slice(-3),10),{hashAlgorithm:n,mgf1HashAlgorithm:s,saltLength:i}=t.asymmetricKeyDetails;if(n!==`sha${r}`||s!==n)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==i&&i>r>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},98300:e=>{let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},99933:(e,t,r)=>{"use strict";Object.defineProperty(t,"U",{enumerable:!0,get:function(){return p}});let n=r(94069),s=r(23158),i=r(29294),o=r(63033),a=r(84971),l=r(80023),u=r(68388),c=r(76926),f=(r(44523),r(8719));function p(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,f.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new s.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new l.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type)return function(e,t){let r=d.get(t);if(r)return r;let n=(0,u.makeHangingPromise)(t.renderSignal,"`cookies()`");return d.set(t,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},size:{get(){let r="`cookies().size`",n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${m(arguments[0])})\``;let n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${m(arguments[0])})\``;let n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${m(arguments[0])})\``;let n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${m(e)}, ...)\``:"`cookies().set(...)`"}let n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${m(arguments[0])})\``:`\`cookies().delete(${m(arguments[0])}, ...)\``;let n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},clear:{value:function(){let r="`cookies().clear()`",n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}},toString:{value:function(){let r="`cookies().toString()`",n=g(e,r);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(e,r,n,t)}}}),n}(t.route,r);"prerender-ppr"===r.type?(0,a.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,a.throwToInterruptStaticGeneration)(e,t,r)}(0,a.trackDynamicDataInDynamicRender)(t,r)}let c=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(c)?c.userspaceMutableCookies:c.cookies)}let d=new WeakMap;function h(e){let t=d.get(e);if(t)return t;let r=Promise.resolve(e);return d.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):E.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function m(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let y=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function E(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580,663],()=>r(2815));module.exports=n})();