1:"$Sreact.fragment"
2:I[7400,["844","static/chunks/ee560e2c-e9c09e02cef8864c.js","493","static/chunks/493-1cf8f8ab3d5926af.js","384","static/chunks/384-c168db35f9f8295a.js","874","static/chunks/874-9f1ca1e6138a33f8.js","177","static/chunks/app/layout-3e61b786b068fce4.js"],"default"]
3:I[3979,["844","static/chunks/ee560e2c-e9c09e02cef8864c.js","493","static/chunks/493-1cf8f8ab3d5926af.js","384","static/chunks/384-c168db35f9f8295a.js","874","static/chunks/874-9f1ca1e6138a33f8.js","177","static/chunks/app/layout-3e61b786b068fce4.js"],"default"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[894,[],"ClientPageRoot"]
7:I[8997,["844","static/chunks/ee560e2c-e9c09e02cef8864c.js","121","static/chunks/121-efaca32120714d9b.js","341","static/chunks/app/goals/page-63c155ce16e3e57a.js"],"default"]
a:I[9665,[],"OutletBoundary"]
d:I[9665,[],"ViewportBoundary"]
f:I[9665,[],"MetadataBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/b412c1caed99ddf9.css","style"]
0:{"P":null,"b":"Ptdb-hYlZSSvVWk1MKald","p":"","c":["","goals"],"i":false,"f":[[["",{"children":["goals",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b412c1caed99ddf9.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","title",null,{"children":"Cashminder - Smart Money Management"}],["$","meta",null,{"name":"description","content":"Take control of your finances with Cashminder's intelligent money management tools"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","link",null,{"rel":"icon","href":"/favicon.ico"}],["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n            // Block Sentry requests to prevent console errors\n            const originalFetch = window.fetch;\n            window.fetch = function(url, options) {\n              if (url && typeof url === 'string' && url.includes('sentry')) {\n                console.log('Blocked Sentry request:', url);\n                return Promise.resolve(new Response('', { status: 200 }));\n              }\n              return originalFetch.apply(this, arguments);\n            };\n          "}}]]}],["$","body",null,{"className":"font-inter antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300","children":["$","$L2",null,{"children":["$","div",null,{"className":"flex flex-col min-h-screen","children":["$","div",null,{"className":"flex-grow","children":[["$","$L3",null,{}],["$","main",null,{"className":"py-10","children":["$","div",null,{"className":"px-4 mx-auto max-w-7xl sm:px-6 lg:px-8","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}]}]}]}]]}]]}],{"children":["goals",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L6",null,{"Component":"$7","searchParams":{},"params":{},"promises":["$@8","$@9"]}],"$undefined",null,["$","$La",null,{"children":["$Lb","$Lc",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","U7ahr37OiHMZJsuGStVVq",{"children":[["$","$Ld",null,{"children":"$Le"}],null]}],["$","$Lf",null,{"children":"$L10"}]]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
8:{}
9:{}
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
c:null
10:[["$","title","0",{"children":"Cashminder - Personal Finance Manager"}],["$","meta","1",{"name":"description","content":"Track your expenses, manage your budget, and achieve your financial goals"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]]
