{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { verifyToken, extractTokenFromHeader } from './lib/jwt';\n\nexport async function middleware(request: NextRequest) {\n  // Temporarily disabled middleware for debugging\n  return NextResponse.next();\n}\n\n// Temporarily disable middleware matcher for debugging\nexport const config = {\n  matcher: [],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACnD,gDAAgD;IAChD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS,EAAE;AACb"}}]}