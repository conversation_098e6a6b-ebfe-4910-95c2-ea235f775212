{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = /* @__PURE__ */ memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,yBAAyB,aAAa,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,IAAM,OAAO,cAAc,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs"], "sourcesContent": ["import { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\n\nclass GroupAnimation {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if (supportsScrollTimeline() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { GroupAnimation };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,YAAY,UAAU,CAAE;QACpB,6DAA6D;QAC7D,IAAI,CAAC,IAAI,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,WAAW,MAAM,CAAC;IACxC;IACA,IAAI,WAAW;QACX,OAAO,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,YAAc,UAAU,QAAQ;IAC5E;IACA;;KAEC,GACD,OAAO,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS;IACvC;IACA,OAAO,QAAQ,EAAE,QAAQ,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;YAC7C,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,GAAG;QACnC;IACJ;IACA,eAAe,QAAQ,EAAE,QAAQ,EAAE;QAC/B,MAAM,gBAAgB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,OAAO,UAAU,cAAc,EAAE;gBACtD,OAAO,UAAU,cAAc,CAAC;YACpC,OACK,IAAI,OAAO,aAAa,YAAY;gBACrC,OAAO,SAAS;YACpB;QACJ;QACA,OAAO;YACH,cAAc,OAAO,CAAC,CAAC,QAAQ;gBAC3B,UAAU;gBACV,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI;YAC3B;QACJ;IACJ;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,KAAK,IAAI,EAAE;QACX,IAAI,CAAC,MAAM,CAAC,QAAQ;IACxB;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,MAAM,KAAK,EAAE;QACb,IAAI,CAAC,MAAM,CAAC,SAAS;IACzB;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;YAC7C,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD;QACA,OAAO;IACX;IACA,OAAO,UAAU,EAAE;QACf,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAa,QAAQ,CAAC,WAAW;IAC9D;IACA,UAAU;QACN,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO;QACH,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,QAAQ;QACJ,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,SAAS;QACL,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,WAAW;QACP,IAAI,CAAC,MAAM,CAAC;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/GroupAnimationWithThen.mjs"], "sourcesContent": ["import { GroupAnimation } from './GroupAnimation.mjs';\n\nclass GroupAnimationWithThen extends GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\nexport { GroupAnimationWithThen };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,+BAA+B,2KAAA,CAAA,iBAAc;IAC/C,KAAK,SAAS,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,KAAQ;IACzD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/render/dom/style.mjs"], "sourcesContent": ["const isCSSVar = (name) => name.startsWith(\"--\");\nconst style = {\n    set: (element, name, value) => {\n        isCSSVar(name)\n            ? element.style.setProperty(name, value)\n            : (element.style[name] = value);\n    },\n    get: (element, name) => {\n        return isCSSVar(name)\n            ? element.style.getPropertyValue(name)\n            : element.style[name];\n    },\n};\n\nexport { style };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,CAAC,OAAS,KAAK,UAAU,CAAC;AAC3C,MAAM,QAAQ;IACV,KAAK,CAAC,SAAS,MAAM;QACjB,SAAS,QACH,QAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,SAC/B,QAAQ,KAAK,CAAC,KAAK,GAAG;IACjC;IACA,KAAK,CAAC,SAAS;QACX,OAAO,SAAS,QACV,QAAQ,KAAK,CAAC,gBAAgB,CAAC,QAC/B,QAAQ,KAAK,CAAC,KAAK;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY,CAAC,QAAU,UAAU;AACvC,SAAS,iBAAiB,SAAS,EAAE,EAAE,MAAM,EAAE,aAAa,MAAM,EAAE,EAAE,aAAa;IAC/E,MAAM,oBAAoB,UAAU,MAAM,CAAC;IAC3C,MAAM,QAAQ,UAAU,eAAe,UAAU,SAAS,MAAM,IAC1D,IACA,kBAAkB,MAAM,GAAG;IACjC,OAAO,CAAC,SAAS,kBAAkB,YAC7B,iBAAiB,CAAC,MAAM,GACxB;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsPartialKeyframes = /*@__PURE__*/ memo(() => {\n    try {\n        document.createElement(\"div\").animate({ opacity: [1] });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n});\n\nexport { supportsPartialKeyframes };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,2BAA2B,WAAW,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE;IAChD,IAAI;QACA,SAAS,aAAa,CAAC,OAAO,OAAO,CAAC;YAAE,SAAS;gBAAC;aAAE;QAAC;IACzD,EACA,OAAO,GAAG;QACN,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs"], "sourcesContent": ["const pxValues = new Set([\n    // Border props\n    \"borderWidth\",\n    \"borderTopWidth\",\n    \"borderRightWidth\",\n    \"borderBottomWidth\",\n    \"borderLeftWidth\",\n    \"borderRadius\",\n    \"radius\",\n    \"borderTopLeftRadius\",\n    \"borderTopRightRadius\",\n    \"borderBottomRightRadius\",\n    \"borderBottomLeftRadius\",\n    // Positioning props\n    \"width\",\n    \"maxWidth\",\n    \"height\",\n    \"maxHeight\",\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\",\n    // Spacing props\n    \"padding\",\n    \"paddingTop\",\n    \"paddingRight\",\n    \"paddingBottom\",\n    \"paddingLeft\",\n    \"margin\",\n    \"marginTop\",\n    \"marginRight\",\n    \"marginBottom\",\n    \"marginLeft\",\n    // Misc\n    \"backgroundPositionX\",\n    \"backgroundPositionY\",\n]);\n\nexport { pxValues };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,IAAI,IAAI;IACrB,eAAe;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO;IACP;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs"], "sourcesContent": ["import { style } from '../../render/dom/style.mjs';\nimport { supportsPartialKeyframes } from '../waapi/supports/partial-keyframes.mjs';\nimport { pxValues } from '../waapi/utils/px-values.mjs';\n\nfunction hydrateKeyframes(element, name, keyframes, pseudoElement) {\n    if (!Array.isArray(keyframes)) {\n        keyframes = [keyframes];\n    }\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] =\n                i === 0 && !pseudoElement\n                    ? style.get(element, name)\n                    : keyframes[i - 1];\n        }\n        if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n            keyframes[i] = keyframes[i] + \"px\";\n        }\n    }\n    if (!pseudoElement && !supportsPartialKeyframes() && keyframes.length < 2) {\n        keyframes.unshift(style.get(element, name));\n    }\n    return keyframes;\n}\n\nexport { hydrateKeyframes };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,iBAAiB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa;IAC7D,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;QAC3B,YAAY;YAAC;SAAU;IAC3B;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;YACvB,SAAS,CAAC,EAAE,GACR,MAAM,KAAK,CAAC,gBACN,sKAAA,CAAA,QAAK,CAAC,GAAG,CAAC,SAAS,QACnB,SAAS,CAAC,IAAI,EAAE;QAC9B;QACA,IAAI,OAAO,SAAS,CAAC,EAAE,KAAK,YAAY,2LAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,OAAO;YACxD,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QAClC;IACJ;IACA,IAAI,CAAC,iBAAiB,CAAC,CAAA,GAAA,sMAAA,CAAA,2BAAwB,AAAD,OAAO,UAAU,MAAM,GAAG,GAAG;QACvE,UAAU,OAAO,CAAC,sKAAA,CAAA,QAAK,CAAC,GAAG,CAAC,SAAS;IACzC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/stats/animation-count.mjs"], "sourcesContent": ["const activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\nexport { activeAnimations };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB;IACrB,QAAQ;IACR,YAAY;IACZ,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/stats/buffer.mjs"], "sourcesContent": ["const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,cAAc;IAChB,OAAO;IACP,sBAAsB;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/supports/flags.mjs"], "sourcesContent": ["/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\nexport { supportsFlags };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,MAAM,gBAAgB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/supports/memo.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => supportsFlags[supportsFlag] ?? memoized();\n}\n\nexport { memoSupports };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,SAAS,aAAa,QAAQ,EAAE,YAAY;IACxC,MAAM,WAAW,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE;IACtB,OAAO,IAAM,0KAAA,CAAA,gBAAa,CAAC,aAAa,IAAI;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs"], "sourcesContent": ["import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB,WAAW,GAAG,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE;IACpD,IAAI;QACA,SACK,aAAa,CAAC,OACd,OAAO,CAAC;YAAE,SAAS;QAAE,GAAG;YAAE,QAAQ;QAAe;IAC1D,EACA,OAAO,GAAG;QACN,OAAO;IACX;IACA,OAAO;AACX,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs"], "sourcesContent": ["const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(i / (numPoints - 1)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n"], "names": [], "mappings": ";;;AAAA,MAAM,uBAAuB,CAAC,QAAQ,UACtC,aAAa,GAAG,kBAAkB;AAAnB;IAEX,IAAI,SAAS;IACb,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW,aAAa;IAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAChC,UAAU,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;IAC5C;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs"], "sourcesContent": ["const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n"], "names": [], "mappings": ";;;AAAA,MAAM,sBAAsB,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAK,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs"], "sourcesContent": ["import { cubicBezierAsString } from './cubic-bezier.mjs';\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\n\nexport { supportedWaapiEasing };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB;IACzB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,QAAQ,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAG;QAAM;QAAM;KAAE;IAC5D,SAAS,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAG;QAAG;KAAK;IAC7D,QAAQ,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAM;QAAM,CAAC;KAAK;IACnE,SAAS,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAM;QAAM;KAAK;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../utils/is-bezier-definition.mjs';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\" && supportsLinearEasing()) {\n        return generateLinearEasing(easing, duration);\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { mapEasingToNativeEasing };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,QAAQ;IAC7C,IAAI,CAAC,QAAQ;QACT,OAAO;IACX,OACK,IAAI,OAAO,WAAW,cAAc,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,KAAK;QAC7D,OAAO,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;IACxC,OACK,IAAI,CAAA,GAAA,mLAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QACjC,OAAO,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;IAC/B,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;QAC5B,OAAO,OAAO,GAAG,CAAC,CAAC,gBAAkB,wBAAwB,eAAe,aACxE,yLAAA,CAAA,uBAAoB,CAAC,OAAO;IACpC,OACK;QACD,OAAO,yLAAA,CAAA,uBAAoB,CAAC,OAAO;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs"], "sourcesContent": ["import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeInOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const animation = element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n        pseudoElement,\n    });\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,oBAAoB,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,EAAE,SAAS,CAAC,EAAE,aAAa,MAAM,EAAE,OAAO,WAAW,EAAE,KAAK,EAAG,GAAG,CAAC,CAAC,EAAE,gBAAgB,SAAS;IAClL,MAAM,kBAAkB;QACpB,CAAC,UAAU,EAAE;IACjB;IACA,IAAI,OACA,gBAAgB,MAAM,GAAG;IAC7B,MAAM,SAAS,CAAA,GAAA,6LAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM;IAC7C;;KAEC,GACD,IAAI,MAAM,OAAO,CAAC,SACd,gBAAgB,MAAM,GAAG;IAC7B,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB,2KAAA,CAAA,mBAAgB,CAAC,KAAK;IAC1B;IACA,MAAM,YAAY,QAAQ,OAAO,CAAC,iBAAiB;QAC/C;QACA;QACA,QAAQ,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAC1C,MAAM;QACN,YAAY,SAAS;QACrB,WAAW,eAAe,YAAY,cAAc;QACpD;IACJ;IACA,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB,UAAU,QAAQ,CAAC,OAAO,CAAC;YACvB,2KAAA,CAAA,mBAAgB,CAAC,KAAK;QAC1B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs"], "sourcesContent": ["function isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\nexport { isGenerator };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,IAAI;IACrB,OAAO,OAAO,SAAS,cAAc,oBAAoB;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs"], "sourcesContent": ["import { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type)) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,sBAAsB,EAAE,IAAI,EAAE,GAAG,SAAS;IAC/C,IAAI,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnB,OAAO,KAAK,cAAc,CAAC;IAC/B,OACK;QACD,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG;QAC3C,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,SAAS;IAC7C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs"], "sourcesContent": ["import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { style } from '../render/dom/style.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { hydrateKeyframes } from './keyframes/hydrate.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\nconst animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement) => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation {\n    constructor(options) {\n        /**\n         * If we already have an animation, we don't need to instantiate one\n         * and can just use this as a controls interface.\n         */\n        if (\"animation\" in options) {\n            this.animation = options.animation;\n            return;\n        }\n        const { element, name, keyframes: unresolvedKeyframes, pseudoElement, allowFlatten = false, } = options;\n        let { transition } = options;\n        this.allowFlatten = allowFlatten;\n        /**\n         * Stop any existing animations on the element before reading existing keyframes.\n         *\n         * TODO: Check for VisualElement before using animation state. This is a fallback\n         * for mini animate(). Do this when implementing NativeAnimationExtended.\n         */\n        const animationMap = getAnimationMap(element);\n        const key = animationMapKey(name, pseudoElement || \"\");\n        const currentAnimation = animationMap.get(key);\n        currentAnimation && currentAnimation.stop();\n        /**\n         * TODO: If these keyframes aren't correctly hydrated then we want to throw\n         * run an instant animation.\n         */\n        const keyframes = hydrateKeyframes(element, name, unresolvedKeyframes, pseudoElement);\n        invariant(typeof transition.type !== \"string\", `animateMini doesn't support \"type\" as a string. Did you mean to import { spring } from \"motion\"?`);\n        transition = applyGeneratorOptions(transition);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.removeAnimation = () => animationMap.delete(key);\n        this.animation.onfinish = () => {\n            if (!pseudoElement) {\n                style.set(element, name, getFinalKeyframe(keyframes, transition));\n            }\n            else {\n                this.commitStyles();\n            }\n            this.cancel();\n        };\n        /**\n         * TODO: Check for VisualElement before using animation state.\n         */\n        animationMap.set(key, this);\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n        this.removeAnimation();\n    }\n    stop() {\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        this.commitStyles();\n        this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        this.animation.commitStyles?.();\n    }\n    get duration() {\n        console.log(this.animation.effect?.getComputedTiming());\n        const duration = this.animation.effect?.getComputedTiming().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    get finished() {\n        return this.animation.finished;\n    }\n    flatten() {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline(timeline) {\n        this.animation.timeline = timeline;\n        this.animation.onfinish = null;\n        return noop;\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve).catch(onReject);\n    }\n}\n\nexport { NativeAnimation };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,gBAAgB,IAAI;AAC1B,MAAM,kBAAkB,CAAC,MAAM,gBAAkB,GAAG,KAAK,CAAC,EAAE,eAAe;AAC3E,SAAS,gBAAgB,OAAO;IAC5B,MAAM,MAAM,cAAc,GAAG,CAAC,YAAY,IAAI;IAC9C,cAAc,GAAG,CAAC,SAAS;IAC3B,OAAO;AACX;AACA;;CAEC,GACD,MAAM;IACF,YAAY,OAAO,CAAE;QACjB;;;SAGC,GACD,IAAI,eAAe,SAAS;YACxB,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;YAClC;QACJ;QACA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,mBAAmB,EAAE,aAAa,EAAE,eAAe,KAAK,EAAG,GAAG;QAChG,IAAI,EAAE,UAAU,EAAE,GAAG;QACrB,IAAI,CAAC,YAAY,GAAG;QACpB;;;;;SAKC,GACD,MAAM,eAAe,gBAAgB;QACrC,MAAM,MAAM,gBAAgB,MAAM,iBAAiB;QACnD,MAAM,mBAAmB,aAAa,GAAG,CAAC;QAC1C,oBAAoB,iBAAiB,IAAI;QACzC;;;SAGC,GACD,MAAM,YAAY,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,MAAM,qBAAqB;QACvE,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,WAAW,IAAI,KAAK,UAAU,CAAC,gGAAgG,CAAC;QACjJ,aAAa,CAAA,GAAA,iMAAA,CAAA,wBAAqB,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,iMAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,MAAM,WAAW,YAAY;QAC3E,IAAI,WAAW,QAAQ,KAAK,OAAO;YAC/B,IAAI,CAAC,SAAS,CAAC,KAAK;QACxB;QACA,IAAI,CAAC,eAAe,GAAG,IAAM,aAAa,MAAM,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;YACtB,IAAI,CAAC,eAAe;gBAChB,sKAAA,CAAA,QAAK,CAAC,GAAG,CAAC,SAAS,MAAM,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;YACzD,OACK;gBACD,IAAI,CAAC,YAAY;YACrB;YACA,IAAI,CAAC,MAAM;QACf;QACA;;SAEC,GACD,aAAa,GAAG,CAAC,KAAK,IAAI;IAC9B;IACA,OAAO;QACH,IAAI,CAAC,SAAS,CAAC,IAAI;IACvB;IACA,QAAQ;QACJ,IAAI,CAAC,SAAS,CAAC,KAAK;IACxB;IACA,WAAW;QACP,IAAI,CAAC,SAAS,CAAC,MAAM;IACzB;IACA,SAAS;QACL,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,MAAM;QACzB,EACA,OAAO,GAAG,CAAE;QACZ,IAAI,CAAC,eAAe;IACxB;IACA,OAAO;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,IAAI,UAAU,UAAU,UAAU,YAAY;YAC1C;QACJ;QACA,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,MAAM;IACf;IACA;;;;;;;;;;;KAWC,GACD,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,YAAY;IAC/B;IACA,IAAI,WAAW;QACX,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACnC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,oBAAoB,YAAY;QACxE,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;IACxC;IACA,IAAI,OAAO;QACP,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK;IACvE;IACA,IAAI,KAAK,OAAO,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACvD;IACA;;;KAGC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY;IACtC;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG;IAClC;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACnC;IACA,IAAI,YAAY;QACZ,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IAC1C;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;IACA,UAAU;QACN,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa;gBAAE,QAAQ;YAAS;QAC3D;IACJ;IACA;;KAEC,GACD,eAAe,QAAQ,EAAE;QACrB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;QAC1B,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;QAC1B,OAAO,sJAAA,CAAA,OAAI;IACf;IACA;;;;KAIC,GACD,KAAK,SAAS,EAAE,QAAQ,EAAE;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC;IAC/C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs"], "sourcesContent": ["function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mBAAmB,UAAU,EAAE,GAAG;IACvC,OAAQ,YAAY,CAAC,IAAI,IACrB,YAAY,CAAC,UAAU,IACvB;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs"], "sourcesContent": ["/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AACD,MAAM,uBAAuB;AAC7B,SAAS,sBAAsB,SAAS;IACpC,IAAI,WAAW;IACf,MAAM,WAAW;IACjB,IAAI,QAAQ,UAAU,IAAI,CAAC;IAC3B,MAAO,CAAC,MAAM,IAAI,IAAI,WAAW,qBAAsB;QACnD,YAAY;QACZ,QAAQ,UAAU,IAAI,CAAC;IAC3B;IACA,OAAO,YAAY,uBAAuB,WAAW;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,sBAAsB,OAAO,EAAE,QAAQ,GAAG,EAAE,eAAe;IAChE,MAAM,YAAY,gBAAgB;QAAE,GAAG,OAAO;QAAE,WAAW;YAAC;YAAG;SAAM;IAAC;IACtE,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA,GAAA,oMAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,oMAAA,CAAA,uBAAoB;IAChF,OAAO;QACH,MAAM;QACN,MAAM,CAAC;YACH,OAAO,UAAU,IAAI,CAAC,WAAW,UAAU,KAAK,GAAG;QACvD;QACA,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../utils/is-bezier-definition.mjs';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && supportsLinearEasing()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in supportedWaapiEasing || supportsLinearEasing())) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\n\nexport { isWaapiSupportedEasing };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,uBAAuB,MAAM;IAClC,OAAO,QAAQ,AAAC,OAAO,WAAW,cAAc,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,OAC/D,CAAC,UACA,OAAO,WAAW,YACf,CAAC,UAAU,yLAAA,CAAA,uBAAoB,IAAI,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,GAAG,KAC7D,CAAA,GAAA,mLAAA,CAAA,qBAAkB,AAAD,EAAE,WAClB,MAAM,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs"], "sourcesContent": ["function attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\nexport { attachTimeline };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,SAAS,EAAE,QAAQ;IACvC,UAAU,QAAQ,GAAG;IACrB,UAAU,QAAQ,GAAG;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/order.mjs"], "sourcesContent": ["const stepsOrder = [\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACf;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/render-step.mjs"], "sourcesContent": ["import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iBAAiB,YAAY,EAAE,QAAQ;IAC5C;;;KAGC,GACD,IAAI,YAAY,IAAI;IACpB,IAAI,YAAY,IAAI;IACpB;;;KAGC,GACD,IAAI,eAAe;IACnB,IAAI,iBAAiB;IACrB;;KAEC,GACD,MAAM,cAAc,IAAI;IACxB,IAAI,kBAAkB;QAClB,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,IAAI,WAAW;IACf,SAAS,gBAAgB,QAAQ;QAC7B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC3B,KAAK,QAAQ,CAAC;YACd;QACJ;QACA;QACA,SAAS;IACb;IACA,MAAM,OAAO;QACT;;SAEC,GACD,UAAU,CAAC,UAAU,YAAY,KAAK,EAAE,YAAY,KAAK;YACrD,MAAM,oBAAoB,aAAa;YACvC,MAAM,QAAQ,oBAAoB,YAAY;YAC9C,IAAI,WACA,YAAY,GAAG,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,CAAC,WACX,MAAM,GAAG,CAAC;YACd,OAAO;QACX;QACA;;SAEC,GACD,QAAQ,CAAC;YACL,UAAU,MAAM,CAAC;YACjB,YAAY,MAAM,CAAC;QACvB;QACA;;SAEC,GACD,SAAS,CAAC;YACN,kBAAkB;YAClB;;;;aAIC,GACD,IAAI,cAAc;gBACd,iBAAiB;gBACjB;YACJ;YACA,eAAe;YACf,CAAC,WAAW,UAAU,GAAG;gBAAC;gBAAW;aAAU;YAC/C,qBAAqB;YACrB,UAAU,OAAO,CAAC;YAClB;;aAEC,GACD,IAAI,YAAY,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;gBAC/B,+JAAA,CAAA,cAAW,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC/C;YACA,WAAW;YACX,2DAA2D;YAC3D,4DAA4D;YAC5D,UAAU,KAAK;YACf,eAAe;YACf,IAAI,gBAAgB;gBAChB,iBAAiB;gBACjB,KAAK,OAAO,CAAC;YACjB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { read, resolveKeyframes, update, preRender, render, postRender } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        read.process(state);\n        resolveKeyframes.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa;AACnB,SAAS,oBAAoB,iBAAiB,EAAE,cAAc;IAC1D,IAAI,eAAe;IACnB,IAAI,oBAAoB;IACxB,MAAM,QAAQ;QACV,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,MAAM,mBAAmB,IAAO,eAAe;IAC/C,MAAM,QAAQ,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QAClC,GAAG,CAAC,IAAI,GAAG,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,iBAAiB,MAAM;QACrE,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;IAC1E,MAAM,eAAe;QACjB,MAAM,YAAY,kKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC9C,MAAM,SAAS,GACf,YAAY,GAAG;QACrB,eAAe;QACf,IAAI,CAAC,kKAAA,CAAA,qBAAkB,CAAC,eAAe,EAAE;YACrC,MAAM,KAAK,GAAG,oBACR,OAAO,KACP,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,MAAM,SAAS,EAAE,aAAa;QACtE;QACA,MAAM,SAAS,GAAG;QAClB,MAAM,YAAY,GAAG;QACrB,wDAAwD;QACxD,KAAK,OAAO,CAAC;QACb,iBAAiB,OAAO,CAAC;QACzB,OAAO,OAAO,CAAC;QACf,UAAU,OAAO,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,WAAW,OAAO,CAAC;QACnB,MAAM,YAAY,GAAG;QACrB,IAAI,gBAAgB,gBAAgB;YAChC,oBAAoB;YACpB,kBAAkB;QACtB;IACJ;IACA,MAAM,OAAO;QACT,eAAe;QACf,oBAAoB;QACpB,IAAI,CAAC,MAAM,YAAY,EAAE;YACrB,kBAAkB;QACtB;IACJ;IACA,MAAM,WAAW,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QACrC,MAAM,OAAO,KAAK,CAAC,IAAI;QACvB,GAAG,CAAC,IAAI,GAAG,CAAC,SAAS,YAAY,KAAK,EAAE,YAAY,KAAK;YACrD,IAAI,CAAC,cACD;YACJ,OAAO,KAAK,QAAQ,CAAC,SAAS,WAAW;QAC7C;QACA,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,SAAS,CAAC;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,kKAAA,CAAA,aAAU,CAAC,MAAM,EAAE,IAAK;YACxC,KAAK,CAAC,kKAAA,CAAA,aAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;QAChC;IACJ;IACA,OAAO;QAAE;QAAU;QAAQ;QAAO;IAAM;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/frame.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA,MAAM,EAAE,UAAU,KAAK,EAAE,QAAQ,WAAW,EAAE,OAAO,SAAS,EAAE,OAAO,UAAU,EAAG,GAAG,aAAa,GAAG,CAAA,GAAA,oKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,0BAA0B,cAAc,wBAAwB,sJAAA,CAAA,OAAI,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/microtask.mjs"], "sourcesContent": ["import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,EAAE,UAAU,SAAS,EAAE,QAAQ,eAAe,EAAE,GACtD,aAAa,GAAG,CAAA,GAAA,oKAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,IAAI;AACJ,SAAS;IACL,MAAM;AACV;AACA;;;;;;;CAOC,GACD,MAAM,OAAO;IACT,KAAK;QACD,IAAI,QAAQ,WAAW;YACnB,KAAK,GAAG,CAAC,kKAAA,CAAA,YAAS,CAAC,YAAY,IAAI,kKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC/D,kKAAA,CAAA,YAAS,CAAC,SAAS,GACnB,YAAY,GAAG;QACzB;QACA,OAAO;IACX;IACA,KAAK,CAAC;QACF,MAAM;QACN,eAAe;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs"], "sourcesContent": ["const isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\nexport { isDragActive, isDragging };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,aAAa;IACf,GAAG;IACH,GAAG;AACP;AACA,SAAS;IACL,OAAO,WAAW,CAAC,IAAI,WAAW,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs"], "sourcesContent": ["import { isDragging } from './is-active.mjs';\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (isDragging[axis]) {\n            return null;\n        }\n        else {\n            isDragging[axis] = true;\n            return () => {\n                isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (isDragging.x || isDragging.y) {\n            return null;\n        }\n        else {\n            isDragging.x = isDragging.y = true;\n            return () => {\n                isDragging.x = isDragging.y = false;\n            };\n        }\n    }\n}\n\nexport { setDragLock };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,YAAY,IAAI;IACrB,IAAI,SAAS,OAAO,SAAS,KAAK;QAC9B,IAAI,yLAAA,CAAA,aAAU,CAAC,KAAK,EAAE;YAClB,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACnB,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACvB;QACJ;IACJ,OACK;QACD,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,EAAE;YAC9B,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAC9B,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAClC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs"], "sourcesContent": ["function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,aAAa;IAC5D,IAAI,6BAA6B,aAAa;QAC1C,OAAO;YAAC;SAAkB;IAC9B,OACK,IAAI,OAAO,sBAAsB,UAAU;QAC5C,IAAI,OAAO;QACX,IAAI,OAAO;YACP,OAAO,MAAM,OAAO;QACxB;QACA,MAAM,WAAW,eAAe,CAAC,kBAAkB,IAC/C,KAAK,gBAAgB,CAAC;QAC1B,OAAO,WAAW,MAAM,IAAI,CAAC,YAAY,EAAE;IAC/C;IACA,OAAO,MAAM,IAAI,CAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,iBAAiB,EAAE,OAAO;IAC5C,MAAM,WAAW,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,yBAAyB,IAAI;IACnC,MAAM,eAAe;QACjB,SAAS;QACT,GAAG,OAAO;QACV,QAAQ,uBAAuB,MAAM;IACzC;IACA,MAAM,SAAS,IAAM,uBAAuB,KAAK;IACjD,OAAO;QAAC;QAAU;QAAc;KAAO;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\nexport { hover };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,aAAa,KAAK;IACvB,OAAO,CAAC,CAAC,MAAM,WAAW,KAAK,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,GAAG;AAC5D;AACA;;;;;;CAMC,GACD,SAAS,MAAM,iBAAiB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,CAAC,UAAU,cAAc,OAAO,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;IACzE,MAAM,iBAAiB,CAAC;QACpB,IAAI,CAAC,aAAa,aACd;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,aAAa,aAAa,QAAQ;QACxC,IAAI,OAAO,eAAe,cAAc,CAAC,QACrC;QACJ,MAAM,iBAAiB,CAAC;YACpB,IAAI,CAAC,aAAa,aACd;YACJ,WAAW;YACX,OAAO,mBAAmB,CAAC,gBAAgB;QAC/C;QACA,OAAO,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC5D;IACA,SAAS,OAAO,CAAC,CAAC;QACd,QAAQ,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC7D;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs"], "sourcesContent": ["/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\nexport { isNodeOrChild };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,gBAAgB,CAAC,QAAQ;IAC3B,IAAI,CAAC,OAAO;QACR,OAAO;IACX,OACK,IAAI,WAAW,OAAO;QACvB,OAAO;IACX,OACK;QACD,OAAO,cAAc,QAAQ,MAAM,aAAa;IACpD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs"], "sourcesContent": ["const isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\nexport { isPrimaryPointer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB,CAAC;IACtB,IAAI,MAAM,WAAW,KAAK,SAAS;QAC/B,OAAO,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,IAAI;IAC/D,OACK;QACD;;;;;;;SAOC,GACD,OAAO,MAAM,SAAS,KAAK;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs"], "sourcesContent": ["const focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\nexport { isElementKeyboardAccessible };\n"], "names": [], "mappings": ";;;AAAA,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,4BAA4B,OAAO;IACxC,OAAQ,kBAAkB,GAAG,CAAC,QAAQ,OAAO,KACzC,QAAQ,QAAQ,KAAK,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs"], "sourcesContent": ["const isPressing = new WeakSet();\n\nexport { isPressing };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs"], "sourcesContent": ["import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\nexport { enableKeyboardPress };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;CAEC,GACD,SAAS,aAAa,QAAQ;IAC1B,OAAO,CAAC;QACJ,IAAI,MAAM,GAAG,KAAK,SACd;QACJ,SAAS;IACb;AACJ;AACA,SAAS,iBAAiB,MAAM,EAAE,IAAI;IAClC,OAAO,aAAa,CAAC,IAAI,aAAa,YAAY,MAAM;QAAE,WAAW;QAAM,SAAS;IAAK;AAC7F;AACA,MAAM,sBAAsB,CAAC,YAAY;IACrC,MAAM,UAAU,WAAW,aAAa;IACxC,IAAI,CAAC,SACD;IACJ,MAAM,gBAAgB,aAAa;QAC/B,IAAI,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UACf;QACJ,iBAAiB,SAAS;QAC1B,MAAM,cAAc,aAAa;YAC7B,iBAAiB,SAAS;QAC9B;QACA,MAAM,aAAa,IAAM,iBAAiB,SAAS;QACnD,QAAQ,gBAAgB,CAAC,SAAS,aAAa;QAC/C,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;IACjD;IACA,QAAQ,gBAAgB,CAAC,WAAW,eAAe;IACnD;;KAEC,GACD,QAAQ,gBAAgB,CAAC,QAAQ,IAAM,QAAQ,mBAAmB,CAAC,WAAW,gBAAgB;AAClG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || isPressing.has(target))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !isPressing.has(target)) {\n                return;\n            }\n            isPressing.delete(target);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (target instanceof HTMLElement) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC5B,OAAO,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;AAClD;AACA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,MAAM,gBAAgB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,CAAC,SAAS,cAAc,aAAa,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB;IAC7E,MAAM,aAAa,CAAC;QAChB,MAAM,SAAS,WAAW,aAAa;QACvC,IAAI,CAAC,kBAAkB,eAAe,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SACjD;QACJ,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC;QACf,MAAM,aAAa,aAAa,QAAQ;QACxC,MAAM,eAAe,CAAC,UAAU;YAC5B,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,iBAAiB;YAC5C,IAAI,CAAC,kBAAkB,aAAa,CAAC,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS;gBACzD;YACJ;YACA,mLAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YAClB,IAAI,OAAO,eAAe,YAAY;gBAClC,WAAW,UAAU;oBAAE;gBAAQ;YACnC;QACJ;QACA,MAAM,cAAc,CAAC;YACjB,aAAa,SAAS,WAAW,UAC7B,WAAW,YACX,QAAQ,eAAe,IACvB,CAAA,GAAA,8LAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,QAAQ,MAAM;QAC5C;QACA,MAAM,kBAAkB,CAAC;YACrB,aAAa,aAAa;QAC9B;QACA,OAAO,gBAAgB,CAAC,aAAa,aAAa;QAClD,OAAO,gBAAgB,CAAC,iBAAiB,iBAAiB;IAC9D;IACA,QAAQ,OAAO,CAAC,CAAC;QACb,MAAM,oBAAoB,QAAQ,eAAe,GAAG,SAAS;QAC7D,kBAAkB,gBAAgB,CAAC,eAAe,YAAY;QAC9D,IAAI,kBAAkB,aAAa;YAC/B,OAAO,gBAAgB,CAAC,SAAS,CAAC,QAAU,CAAA,GAAA,sLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YACvE,IAAI,CAAC,CAAA,GAAA,0MAAA,CAAA,8BAA2B,AAAD,EAAE,WAC7B,CAAC,OAAO,YAAY,CAAC,aAAa;gBAClC,OAAO,QAAQ,GAAG;YACtB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/stats/index.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\nimport { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\n\nfunction record() {\n    const { value } = statsBuffer;\n    if (value === null) {\n        cancelFrame(record);\n        return;\n    }\n    value.frameloop.rate.push(frameData.delta);\n    value.animations.mainThread.push(activeAnimations.mainThread);\n    value.animations.waapi.push(activeAnimations.waapi);\n    value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n    return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n    if (values.length === 0) {\n        return {\n            min: 0,\n            max: 0,\n            avg: 0,\n        };\n    }\n    return {\n        min: Math.min(...values),\n        max: Math.max(...values),\n        avg: calcAverage(values),\n    };\n}\nconst msToFps = (ms) => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n    statsBuffer.value = null;\n    statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n    const { value } = statsBuffer;\n    if (!value) {\n        throw new Error(\"Stats are not being measured\");\n    }\n    clearStatsBuffer();\n    cancelFrame(record);\n    const summary = {\n        frameloop: {\n            rate: summarise(value.frameloop.rate),\n            read: summarise(value.frameloop.read),\n            resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n            update: summarise(value.frameloop.update),\n            preRender: summarise(value.frameloop.preRender),\n            render: summarise(value.frameloop.render),\n            postRender: summarise(value.frameloop.postRender),\n        },\n        animations: {\n            mainThread: summarise(value.animations.mainThread),\n            waapi: summarise(value.animations.waapi),\n            layout: summarise(value.animations.layout),\n        },\n        layoutProjection: {\n            nodes: summarise(value.layoutProjection.nodes),\n            calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n            calculatedProjections: summarise(value.layoutProjection.calculatedProjections),\n        },\n    };\n    /**\n     * Convert the rate to FPS\n     */\n    const { rate } = summary.frameloop;\n    rate.min = msToFps(rate.min);\n    rate.max = msToFps(rate.max);\n    rate.avg = msToFps(rate.avg);\n    [rate.min, rate.max] = [rate.max, rate.min];\n    return summary;\n}\nfunction recordStats() {\n    if (statsBuffer.value) {\n        clearStatsBuffer();\n        throw new Error(\"Stats are already being measured\");\n    }\n    const newStatsBuffer = statsBuffer;\n    newStatsBuffer.value = {\n        frameloop: {\n            rate: [],\n            read: [],\n            resolveKeyframes: [],\n            update: [],\n            preRender: [],\n            render: [],\n            postRender: [],\n        },\n        animations: {\n            mainThread: [],\n            waapi: [],\n            layout: [],\n        },\n        layoutProjection: {\n            nodes: [],\n            calculatedTargetDeltas: [],\n            calculatedProjections: [],\n        },\n    };\n    newStatsBuffer.addProjectionMetrics = (metrics) => {\n        const { layoutProjection } = newStatsBuffer.value;\n        layoutProjection.nodes.push(metrics.nodes);\n        layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n        layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n    };\n    frame.postRender(record, true);\n    return reportStats;\n}\n\nexport { recordStats };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS;IACL,MAAM,EAAE,KAAK,EAAE,GAAG,+JAAA,CAAA,cAAW;IAC7B,IAAI,UAAU,MAAM;QAChB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;QACZ;IACJ;IACA,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,kKAAA,CAAA,YAAS,CAAC,KAAK;IACzC,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,2KAAA,CAAA,mBAAgB,CAAC,UAAU;IAC5D,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2KAAA,CAAA,mBAAgB,CAAC,KAAK;IAClD,MAAM,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,2KAAA,CAAA,mBAAgB,CAAC,MAAM;AACxD;AACA,SAAS,KAAK,MAAM;IAChB,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK,OAAO,MAAM;AACxE;AACA,SAAS,UAAU,MAAM,EAAE,cAAc,IAAI;IACzC,IAAI,OAAO,MAAM,KAAK,GAAG;QACrB,OAAO;YACH,KAAK;YACL,KAAK;YACL,KAAK;QACT;IACJ;IACA,OAAO;QACH,KAAK,KAAK,GAAG,IAAI;QACjB,KAAK,KAAK,GAAG,IAAI;QACjB,KAAK,YAAY;IACrB;AACJ;AACA,MAAM,UAAU,CAAC,KAAO,KAAK,KAAK,CAAC,OAAO;AAC1C,SAAS;IACL,+JAAA,CAAA,cAAW,CAAC,KAAK,GAAG;IACpB,+JAAA,CAAA,cAAW,CAAC,oBAAoB,GAAG;AACvC;AACA,SAAS;IACL,MAAM,EAAE,KAAK,EAAE,GAAG,+JAAA,CAAA,cAAW;IAC7B,IAAI,CAAC,OAAO;QACR,MAAM,IAAI,MAAM;IACpB;IACA;IACA,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;IACZ,MAAM,UAAU;QACZ,WAAW;YACP,MAAM,UAAU,MAAM,SAAS,CAAC,IAAI;YACpC,MAAM,UAAU,MAAM,SAAS,CAAC,IAAI;YACpC,kBAAkB,UAAU,MAAM,SAAS,CAAC,gBAAgB;YAC5D,QAAQ,UAAU,MAAM,SAAS,CAAC,MAAM;YACxC,WAAW,UAAU,MAAM,SAAS,CAAC,SAAS;YAC9C,QAAQ,UAAU,MAAM,SAAS,CAAC,MAAM;YACxC,YAAY,UAAU,MAAM,SAAS,CAAC,UAAU;QACpD;QACA,YAAY;YACR,YAAY,UAAU,MAAM,UAAU,CAAC,UAAU;YACjD,OAAO,UAAU,MAAM,UAAU,CAAC,KAAK;YACvC,QAAQ,UAAU,MAAM,UAAU,CAAC,MAAM;QAC7C;QACA,kBAAkB;YACd,OAAO,UAAU,MAAM,gBAAgB,CAAC,KAAK;YAC7C,wBAAwB,UAAU,MAAM,gBAAgB,CAAC,sBAAsB;YAC/E,uBAAuB,UAAU,MAAM,gBAAgB,CAAC,qBAAqB;QACjF;IACJ;IACA;;KAEC,GACD,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,SAAS;IAClC,KAAK,GAAG,GAAG,QAAQ,KAAK,GAAG;IAC3B,KAAK,GAAG,GAAG,QAAQ,KAAK,GAAG;IAC3B,KAAK,GAAG,GAAG,QAAQ,KAAK,GAAG;IAC3B,CAAC,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG;QAAC,KAAK,GAAG;QAAE,KAAK,GAAG;KAAC;IAC3C,OAAO;AACX;AACA,SAAS;IACL,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB;QACA,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,iBAAiB,+JAAA,CAAA,cAAW;IAClC,eAAe,KAAK,GAAG;QACnB,WAAW;YACP,MAAM,EAAE;YACR,MAAM,EAAE;YACR,kBAAkB,EAAE;YACpB,QAAQ,EAAE;YACV,WAAW,EAAE;YACb,QAAQ,EAAE;YACV,YAAY,EAAE;QAClB;QACA,YAAY;YACR,YAAY,EAAE;YACd,OAAO,EAAE;YACT,QAAQ,EAAE;QACd;QACA,kBAAkB;YACd,OAAO,EAAE;YACT,wBAAwB,EAAE;YAC1B,uBAAuB,EAAE;QAC7B;IACJ;IACA,eAAe,oBAAoB,GAAG,CAAC;QACnC,MAAM,EAAE,gBAAgB,EAAE,GAAG,eAAe,KAAK;QACjD,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,KAAK;QACzC,iBAAiB,sBAAsB,CAAC,IAAI,CAAC,QAAQ,sBAAsB;QAC3E,iBAAiB,qBAAqB,CAAC,IAAI,CAAC,QAAQ,qBAAqB;IAC7E;IACA,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,QAAQ;IACzB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/value/index.mjs"], "sourcesContent": ["import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { frame } from '../frameloop/frame.mjs';\nimport { time } from '../frameloop/sync-time.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"12.6.3\";\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;AAEA;;;CAGC,GACD,MAAM,qBAAqB;AAC3B,MAAM,UAAU,CAAC;IACb,OAAO,CAAC,MAAM,WAAW;AAC7B;AACA,MAAM,sBAAsB;IACxB,SAAS;AACb;AACA;;;;CAIC,GACD,MAAM;IACF;;;;;KAKC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC5B;;;SAGC,GACD,IAAI,CAAC,OAAO,GAAG;QACf;;;;;;SAMC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,SAAS,IAAI;YACpC,MAAM,cAAc,yKAAA,CAAA,OAAI,CAAC,GAAG;YAC5B;;;;aAIC,GACD,IAAI,IAAI,CAAC,SAAS,KAAK,aAAa;gBAChC,IAAI,CAAC,iBAAiB;YAC1B;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO;YACxB,IAAI,CAAC,UAAU,CAAC;YAChB,4BAA4B;YAC5B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;YAC1C;YACA,4BAA4B;YAC5B,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;YACjD;QACJ;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;IAC9B;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,yKAAA,CAAA,OAAI,CAAC,GAAG;QACzB,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,YAAY,WAAW;YACzD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,IAAI,CAAC,OAAO;QAChD;IACJ;IACA,kBAAkB,iBAAiB,IAAI,CAAC,OAAO,EAAE;QAC7C,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS;IACvC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCC,GACD,SAAS,YAAY,EAAE;QACnB,wCAA2C;YACvC,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,+EAA+E,CAAC;QACrG;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU;IAC7B;IACA,GAAG,SAAS,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,yKAAA,CAAA,sBAAmB;QACpD;QACA,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/C,IAAI,cAAc,UAAU;YACxB,OAAO;gBACH;gBACA;;;iBAGC,GACD,kKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACP,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI;wBAC/B,IAAI,CAAC,IAAI;oBACb;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB;QACb,IAAK,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAE;YACrC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK;QACpC;IACJ;IACA;;KAEC,GACD,OAAO,aAAa,EAAE,iBAAiB,EAAE;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA;;;;;;;;;;;;;;KAcC,GACD,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,GAAG;QAC5B,OACK;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,eAAe;QAC9C;IACJ;IACA,gBAAgB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;QAClC,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG;IAC1C;IACA;;;KAGC,GACD,KAAK,CAAC,EAAE,eAAe,IAAI,EAAE;QACzB,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG;QAC3C,gBAAgB,IAAI,CAAC,IAAI;QACzB,IAAI,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,iBAAiB;IAC9B;IACA;;;;;;KAMC,GACD,MAAM;QACF,IAAI,oBAAoB,OAAO,EAAE;YAC7B,oBAAoB,OAAO,CAAC,IAAI,CAAC,IAAI;QACzC;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;KAEC,GACD,cAAc;QACV,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;;;;;KAMC,GACD,cAAc;QACV,MAAM,cAAc,yKAAA,CAAA,OAAI,CAAC,GAAG;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IACtB,IAAI,CAAC,cAAc,KAAK,aACxB,cAAc,IAAI,CAAC,SAAS,GAAG,oBAAoB;YACnD,OAAO;QACX;QACA,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;QAC5D,4CAA4C;QAC5C,OAAO,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,IAAI,CAAC,OAAO,IAC5C,WAAW,IAAI,CAAC,cAAc,GAAG;IACzC;IACA;;;;;;;;;KASC,GACD,MAAM,cAAc,EAAE;QAClB,IAAI,CAAC,IAAI;QACT,OAAO,IAAI,QAAQ,CAAC;YAChB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,SAAS,GAAG,eAAe;YAChC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM;YACrC;QACJ,GAAG,IAAI,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM;YACxC;YACA,IAAI,CAAC,cAAc;QACvB;IACJ;IACA;;;;KAIC,GACD,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,IAAI;YACnB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM;YACtC;QACJ;QACA,IAAI,CAAC,cAAc;IACvB;IACA;;;;KAIC,GACD,cAAc;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;IAC3B;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;;;;;;;;KAQC,GACD,UAAU;QACN,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,IAAI;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,OAAO;IAC9B,OAAO,IAAI,YAAY,MAAM;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs"], "sourcesContent": ["function chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\nexport { chooseLayerType };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,SAAS;IAC9B,IAAI,cAAc,UACd,OAAO;IACX,IAAI,cAAc,WAAW,cAAc,OACvC,OAAO;IACX,IAAI,cAAc,UAAU,cAAc,OACtC,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/utils/css.mjs"], "sourcesContent": ["let pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\nexport { css };\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe,CAAC;AACpB,IAAI,QAAQ;AACZ,MAAM,MAAM;IACR,KAAK,CAAC,UAAU;QACZ,YAAY,CAAC,SAAS,GAAG;IAC7B;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO;YACR,QAAQ,SAAS,aAAa,CAAC;YAC/B,MAAM,EAAE,GAAG;QACf;QACA,IAAI,UAAU;QACd,IAAK,MAAM,YAAY,aAAc;YACjC,MAAM,OAAO,YAAY,CAAC,SAAS;YACnC,WAAW,GAAG,SAAS,IAAI,CAAC;YAC5B,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;gBAClD,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;YAC3C;YACA,WAAW;QACf;QACA,MAAM,WAAW,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,eAAe,CAAC;IACpB;IACA,QAAQ;QACJ,IAAI,SAAS,MAAM,aAAa,EAAE;YAC9B,MAAM,aAAa,CAAC,WAAW,CAAC;QACpC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs"], "sourcesContent": ["function getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\nexport { getLayerName };\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,aAAa;IAC/B,MAAM,QAAQ,cAAc,KAAK,CAAC;IAClC,IAAI,CAAC,OACD,OAAO;IACX,OAAO;QAAE,OAAO,KAAK,CAAC,EAAE;QAAE,MAAM,KAAK,CAAC,EAAE;IAAC;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs"], "sourcesContent": ["function filterViewAnimations(animation) {\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        effect.pseudoElement?.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\nexport { getViewAnimations };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,SAAS;IACnC,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,IAAI,CAAC,QACD,OAAO;IACX,OAAQ,OAAO,MAAM,KAAK,SAAS,eAAe,IAC9C,OAAO,aAAa,EAAE,WAAW;AACzC;AACA,SAAS;IACL,OAAO,SAAS,aAAa,GAAG,MAAM,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/utils/has-target.mjs"], "sourcesContent": ["function hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\nexport { hasTarget };\n"], "names": [], "mappings": ";;;AAAA,SAAS,UAAU,MAAM,EAAE,OAAO;IAC9B,OAAO,QAAQ,GAAG,CAAC,WAAW,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,MAAM,GAAG;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/start.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { GroupAnimation } from '../animation/GroupAnimation.mjs';\nimport { NativeAnimation } from '../animation/NativeAnimation.mjs';\nimport { getValueTransition } from '../animation/utils/get-value-transition.mjs';\nimport { mapEasingToNativeEasing } from '../animation/waapi/easing/map-easing.mjs';\nimport { applyGeneratorOptions } from '../animation/waapi/utils/apply-generator.mjs';\nimport { chooseLayerType } from './utils/choose-layer-type.mjs';\nimport { css } from './utils/css.mjs';\nimport { getLayerName } from './utils/get-layer-name.mjs';\nimport { getViewAnimations } from './utils/get-view-animations.mjs';\nimport { hasTarget } from './utils/has-target.mjs';\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(builder) {\n    const { update, targets, options: defaultOptions } = builder;\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new GroupAnimation([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!hasTarget(\"root\", targets)) {\n        css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            const generatedViewAnimations = getViewAnimations();\n            const animations = [];\n            /**\n             * Create animations for each of our explicitly-defined subjects.\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...getValueTransition(defaultOptions, valueName),\n                            ...getValueTransition(options, valueName),\n                        };\n                        const type = chooseLayerType(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        const animation = new NativeAnimation({\n                            element: document.documentElement,\n                            name: valueName,\n                            pseudoElement: `::view-transition-${type}(${target})`,\n                            keyframes: valueKeyframes,\n                            transition: valueOptions,\n                        });\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = getLayerName(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    let animationTransition = {\n                        ...getValueTransition(defaultOptions, transitionName),\n                    };\n                    animationTransition.duration && (animationTransition.duration = secondsToMilliseconds(animationTransition.duration));\n                    animationTransition =\n                        applyGeneratorOptions(animationTransition);\n                    const easing = mapEasingToNativeEasing(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: secondsToMilliseconds(animationTransition.delay ?? 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new NativeAnimation({ animation }));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new NativeAnimation({ animation }));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new GroupAnimation(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    return target?.[key]?.keyframes.opacity;\n}\n\nexport { startViewAnimation };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,kBAAkB;IAAC;IAAU;IAAS;IAAQ;IAAO;CAAM;AACjE,SAAS,mBAAmB,OAAO;IAC/B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,cAAc,EAAE,GAAG;IACrD,IAAI,CAAC,SAAS,mBAAmB,EAAE;QAC/B,OAAO,IAAI,QAAQ,OAAO;YACtB,MAAM;YACN,QAAQ,IAAI,2KAAA,CAAA,iBAAc,CAAC,EAAE;QACjC;IACJ;IACA,8DAA8D;IAC9D;;;KAGC,GACD,IAAI,CAAC,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,UAAU;QAC7B,oKAAA,CAAA,MAAG,CAAC,GAAG,CAAC,SAAS;YACb,wBAAwB;QAC5B;IACJ;IACA;;;;;;KAMC,GACD,oKAAA,CAAA,MAAG,CAAC,GAAG,CAAC,kFAAkF;QAAE,6BAA6B;IAAoB;IAC7I,oKAAA,CAAA,MAAG,CAAC,MAAM,IAAI,QAAQ;IACtB,MAAM,aAAa,SAAS,mBAAmB,CAAC;QAC5C,MAAM;IACN,yDAAyD;IAC7D;IACA,WAAW,QAAQ,CAAC,OAAO,CAAC;QACxB,oKAAA,CAAA,MAAG,CAAC,MAAM,IAAI,QAAQ;IAC1B;IACA,OAAO,IAAI,QAAQ,CAAC;QAChB,WAAW,KAAK,CAAC,IAAI,CAAC;YAClB,MAAM,0BAA0B,CAAA,GAAA,0LAAA,CAAA,oBAAiB,AAAD;YAChD,MAAM,aAAa,EAAE;YACrB;;aAEC,GACD,QAAQ,OAAO,CAAC,CAAC,YAAY;gBACzB,kDAAkD;gBAClD,wBAAwB;gBACxB,KAAK,MAAM,OAAO,gBAAiB;oBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,EAChB;oBACJ,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,IAAI;oBAC9C,KAAK,IAAI,CAAC,WAAW,eAAe,IAAI,OAAO,OAAO,CAAC,WAAY;wBAC/D,IAAI,CAAC,gBACD;wBACJ,MAAM,eAAe;4BACjB,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,UAAU;4BAChD,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,UAAU;wBAC7C;wBACA,MAAM,OAAO,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;wBAC7B;;;yBAGC,GACD,IAAI,cAAc,aACd,CAAC,MAAM,OAAO,CAAC,iBAAiB;4BAChC,MAAM,eAAe,SAAS,QAAQ,IAAI;4BAC1C,iBAAiB;gCAAC;gCAAc;6BAAe;wBACnD;wBACA;;yBAEC,GACD,IAAI,OAAO,aAAa,KAAK,KAAK,YAAY;4BAC1C,aAAa,KAAK,GAAG,aAAa,KAAK,CAAC,GAAG;wBAC/C;wBACA,MAAM,YAAY,IAAI,4KAAA,CAAA,kBAAe,CAAC;4BAClC,SAAS,SAAS,eAAe;4BACjC,MAAM;4BACN,eAAe,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;4BACrD,WAAW;4BACX,YAAY;wBAChB;wBACA,WAAW,IAAI,CAAC;oBACpB;gBACJ;YACJ;YACA;;aAEC,GACD,KAAK,MAAM,aAAa,wBAAyB;gBAC7C,IAAI,UAAU,SAAS,KAAK,YACxB;gBACJ,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,IAAI,CAAC,UAAU,CAAC,CAAC,kBAAkB,cAAc,GAC7C;gBACJ,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,IAAI,CAAC,eACD;gBACJ,MAAM,OAAO,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;gBAC1B,IAAI,CAAC,MACD;gBACJ,MAAM,mBAAmB,QAAQ,GAAG,CAAC,KAAK,KAAK;gBAC/C,IAAI,CAAC,kBAAkB;oBACnB;;;;qBAIC,GACD,MAAM,iBAAiB,KAAK,IAAI,KAAK,UAAU,WAAW;oBAC1D,IAAI,sBAAsB;wBACtB,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,eAAe;oBACzD;oBACA,oBAAoB,QAAQ,IAAI,CAAC,oBAAoB,QAAQ,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,oBAAoB,QAAQ,CAAC;oBACnH,sBACI,CAAA,GAAA,iMAAA,CAAA,wBAAqB,AAAD,EAAE;oBAC1B,MAAM,SAAS,CAAA,GAAA,6LAAA,CAAA,0BAAuB,AAAD,EAAE,oBAAoB,IAAI,EAAE,oBAAoB,QAAQ;oBAC7F,OAAO,YAAY,CAAC;wBAChB,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,oBAAoB,KAAK,IAAI;wBAC1D,UAAU,oBAAoB,QAAQ;wBACtC;oBACJ;oBACA,WAAW,IAAI,CAAC,IAAI,4KAAA,CAAA,kBAAe,CAAC;wBAAE;oBAAU;gBACpD,OACK,IAAI,WAAW,kBAAkB,YAClC,WAAW,kBAAkB,WAC7B,OACK,YAAY,GACZ,IAAI,CAAC,CAAC,WAAa,SAAS,YAAY,GAAG;oBAChD,WAAW,IAAI,CAAC,IAAI,4KAAA,CAAA,kBAAe,CAAC;wBAAE;oBAAU;gBACpD,OACK;oBACD,UAAU,MAAM;gBACpB;YACJ;YACA,QAAQ,IAAI,2KAAA,CAAA,iBAAc,CAAC;QAC/B;IACJ;AACJ;AACA,SAAS,WAAW,MAAM,EAAE,GAAG;IAC3B,OAAO,QAAQ,CAAC,IAAI,EAAE,UAAU;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/queue.mjs"], "sourcesContent": ["import { removeItem } from 'motion-utils';\nimport { microtask } from '../frameloop/microtask.mjs';\nimport { startViewAnimation } from './start.mjs';\n\nlet builders = [];\nlet current = null;\nfunction next() {\n    current = null;\n    const [nextBuilder] = builders;\n    if (nextBuilder)\n        start(nextBuilder);\n}\nfunction start(builder) {\n    removeItem(builders, builder);\n    current = builder;\n    startViewAnimation(builder).then((animation) => {\n        builder.notifyReady(animation);\n        animation.finished.finally(next);\n    });\n}\nfunction processQueue() {\n    /**\n     * Iterate backwards over the builders array. We can ignore the\n     * \"wait\" animations. If we have an interrupting animation in the\n     * queue then we need to batch all preceeding animations into it.\n     * Currently this only batches the update functions but will also\n     * need to batch the targets.\n     */\n    for (let i = builders.length - 1; i >= 0; i--) {\n        const builder = builders[i];\n        const { interrupt } = builder.options;\n        if (interrupt === \"immediate\") {\n            const batchedUpdates = builders.slice(0, i + 1).map((b) => b.update);\n            const remaining = builders.slice(i + 1);\n            builder.update = () => {\n                batchedUpdates.forEach((update) => update());\n            };\n            // Put the current builder at the front, followed by any \"wait\" builders\n            builders = [builder, ...remaining];\n            break;\n        }\n    }\n    if (!current || builders[0]?.options.interrupt === \"immediate\") {\n        next();\n    }\n}\nfunction addToQueue(builder) {\n    builders.push(builder);\n    microtask.render(processQueue);\n}\n\nexport { addToQueue };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEA,IAAI,WAAW,EAAE;AACjB,IAAI,UAAU;AACd,SAAS;IACL,UAAU;IACV,MAAM,CAAC,YAAY,GAAG;IACtB,IAAI,aACA,MAAM;AACd;AACA,SAAS,MAAM,OAAO;IAClB,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE,UAAU;IACrB,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,IAAI,CAAC,CAAC;QAC9B,QAAQ,WAAW,CAAC;QACpB,UAAU,QAAQ,CAAC,OAAO,CAAC;IAC/B;AACJ;AACA,SAAS;IACL;;;;;;KAMC,GACD,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC3C,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,OAAO;QACrC,IAAI,cAAc,aAAa;YAC3B,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;YACnE,MAAM,YAAY,SAAS,KAAK,CAAC,IAAI;YACrC,QAAQ,MAAM,GAAG;gBACb,eAAe,OAAO,CAAC,CAAC,SAAW;YACvC;YACA,wEAAwE;YACxE,WAAW;gBAAC;mBAAY;aAAU;YAClC;QACJ;IACJ;IACA,IAAI,CAAC,WAAW,QAAQ,CAAC,EAAE,EAAE,QAAQ,cAAc,aAAa;QAC5D;IACJ;AACJ;AACA,SAAS,WAAW,OAAO;IACvB,SAAS,IAAI,CAAC;IACd,sKAAA,CAAA,YAAS,CAAC,MAAM,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/node_modules/motion-dom/dist/es/view/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\n\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        this.update = update;\n        this.options = {\n            interrupt: \"wait\",\n            ...options,\n        };\n        addToQueue(this);\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction animateView(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\nexport { ViewTransitionBuilder, animateView };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;IACF,YAAY,MAAM,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,WAAW,GAAG,sJAAA,CAAA,OAAI;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;YACX,WAAW;YACX,GAAG,OAAO;QACd;QACA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,IAAI;IACnB;IACA,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,aAAa,GAAG;QACrB,OAAO,IAAI;IACf;IACA,OAAO,SAAS,EAAE,OAAO,EAAE;QACvB,IAAI,CAAC,YAAY,CAAC,UAAU,WAAW;QACvC,OAAO,IAAI;IACf;IACA,IAAI,SAAS,EAAE,OAAO,EAAE;QACpB,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW;QACpC,OAAO,IAAI;IACf;IACA,IAAI,SAAS,EAAE,OAAO,EAAE;QACpB,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW;QACpC,OAAO,IAAI;IACf;IACA,MAAM,SAAS,EAAE,OAAO,EAAE;QACtB,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;QACtC,OAAO,IAAI;IACf;IACA,KAAK,SAAS,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,WAAW;QACrC,OAAO,IAAI;IACf;IACA,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,YAAY,CAAC,SAAS;YAAE,SAAS;QAAE,GAAG;QAC3C,IAAI,CAAC,YAAY,CAAC,QAAQ;YAAE,SAAS;QAAE,GAAG;QAC1C,OAAO,IAAI;IACf;IACA,aAAa,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;QAC1C,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,IAAI;QACvC,IAAI,CAAC,QAAQ,GAAG,CAAC,gBAAgB;YAC7B,QAAQ,GAAG,CAAC,eAAe,CAAC;QAChC;QACA,MAAM,aAAa,QAAQ,GAAG,CAAC;QAC/B,UAAU,CAAC,OAAO,GAAG;YAAE;YAAW;QAAQ;IAC9C;IACA,KAAK,OAAO,EAAE,MAAM,EAAE;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;IAC3C;AACJ;AACA,SAAS,YAAY,MAAM,EAAE,iBAAiB,CAAC,CAAC;IAC5C,OAAO,IAAI,sBAAsB,QAAQ;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}