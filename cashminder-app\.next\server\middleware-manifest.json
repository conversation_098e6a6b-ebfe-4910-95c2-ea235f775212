{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CPHUbL1TqxLllFElih6XeM4ipXSJg4EBis5ANgWuZ+U=", "__NEXT_PREVIEW_MODE_ID": "2cc4467d87cd8bd7c46d9411ea037b06", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "be0379496d79224092c52ba29c9573308618d9edc73aab6e5be899ebeba7b9fc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "23405fd8b04cdc27589fb731bc5ebe88b1563cb6e254c3819f4debdf72b45c34"}}}, "sortedMiddleware": ["/"], "functions": {}}