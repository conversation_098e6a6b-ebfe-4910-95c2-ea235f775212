{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Combine Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\n// Format date\nexport function formatDate(dateString: string, formatStr: string = 'MMM d, yyyy'): string {\n  try {\n    const date = parseISO(dateString);\n    return format(date, formatStr);\n  } catch (error) {\n    return dateString;\n  }\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Generate random color\nexport function generateRandomColor(): string {\n  const colors = [\n    '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA5A5', '#A5FFD6',\n    '#FFC145', '#FF6B8B', '#845EC2', '#D65DB1', '#FF9671',\n  ];\n  return colors[Math.floor(Math.random() * colors.length)];\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number = 25): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,UAAkB,EAAE,YAAoB,aAAa;IAC9E,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS;IACd,MAAM,SAAS;QACb;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,EAAE;IAC/D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/transactions/TransactionList.tsx"], "sourcesContent": ["'use client';\n\nimport { Category, Transaction } from '@/lib/types';\nimport { formatCurrency, formatDate, truncateText } from '@/lib/utils';\nimport { FiEdit2, FiTrash2 } from 'react-icons/fi';\n\ninterface TransactionListProps {\n  transactions: Transaction[];\n  categories: Category[];\n  onEdit: (transaction: Transaction) => void;\n  onDelete: (transactionId: string) => void;\n}\n\nexport default function TransactionList({\n  transactions,\n  categories,\n  onEdit,\n  onDelete,\n}: TransactionListProps) {\n  const getCategoryName = (categoryId: string) => {\n    const category = categories.find((c) => c.id === categoryId);\n    return category ? category.name : 'Uncategorized';\n  };\n\n  return (\n    <div className=\"overflow-hidden rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm\">\n      {transactions.length > 0 ? (\n        <ul className=\"divide-y divide-light-border dark:divide-dark-border\">\n          {transactions.map((transaction) => (\n            <li key={transaction.id}>\n              <div className=\"block hover:bg-light-accent dark:hover:bg-dark-accent transition-colors\">\n                <div className=\"flex items-center px-4 py-4 sm:px-6\">\n                  <div className=\"flex-1 min-w-0 sm:flex sm:items-center sm:justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-primary truncate\">\n                        {truncateText(transaction.description, 40)}\n                      </p>\n                      <div className=\"mt-2 flex\">\n                        <div className=\"flex items-center text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n                          <p>\n                            {getCategoryName(transaction.category_id)} • {formatDate(transaction.date)}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"mt-4 flex-shrink-0 sm:mt-0\">\n                      <p\n                        className={`text-sm font-medium ${\n                          transaction.is_income ? 'text-success-light dark:text-success-dark' : 'text-error-light dark:text-error-dark'\n                        }`}\n                      >\n                        {transaction.is_income ? '+' : '-'}\n                        {formatCurrency(Math.abs(transaction.amount))}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 flex-shrink-0 flex space-x-2\">\n                    <button\n                      onClick={() => onEdit(transaction)}\n                      className=\"p-1 rounded-full text-light-text-muted dark:text-dark-text-muted hover:text-primary dark:hover:text-primary transition-colors\"\n                    >\n                      <FiEdit2 className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      onClick={() => onDelete(transaction.id)}\n                      className=\"p-1 rounded-full text-light-text-muted dark:text-dark-text-muted hover:text-error-light dark:hover:text-error-dark transition-colors\"\n                    >\n                      <FiTrash2 className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </li>\n          ))}\n        </ul>\n      ) : (\n        <div className=\"py-12 text-center text-light-text-secondary dark:text-dark-text-secondary bg-light-accent dark:bg-dark-accent\">\n          <svg className=\"w-16 h-16 mx-auto mb-4 text-light-text-muted dark:text-dark-text-muted\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n          </svg>\n          <p className=\"font-medium text-light-text-primary dark:text-dark-text-primary\">No transactions found</p>\n          <p className=\"text-sm mt-1\">Add some transactions to see them here</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAae,SAAS,gBAAgB,EACtC,YAAY,EACZ,UAAU,EACV,MAAM,EACN,QAAQ,EACa;IACrB,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QACjD,OAAO,WAAW,SAAS,IAAI,GAAG;IACpC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC;YAAG,WAAU;sBACX,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;8BACC,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,WAAW,EAAE;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;;gEACE,gBAAgB,YAAY,WAAW;gEAAE;gEAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAKjF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,oBAAoB,EAC9B,YAAY,SAAS,GAAG,8CAA8C,yCACtE;;oDAED,YAAY,SAAS,GAAG,MAAM;oDAC9B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,CAAC,YAAY,MAAM;;;;;;;;;;;;;;;;;;8CAIjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO;4CACtB,WAAU;sDAEV,cAAA,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CACC,SAAS,IAAM,SAAS,YAAY,EAAE;4CACtC,WAAU;sDAEV,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAtCrB,YAAY,EAAE;;;;;;;;;iCA+C3B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;oBAAyE,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC7H,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAK,GAAE;;;;;;;;;;;8BAEzE,6LAAC;oBAAE,WAAU;8BAAkE;;;;;;8BAC/E,6LAAC;oBAAE,WAAU;8BAAe;;;;;;;;;;;;;;;;;AAKtC;KAzEwB", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/transactions/TransactionForm.tsx"], "sourcesContent": ["'use client';\n\nimport { Category, Transaction } from '@/lib/types';\nimport { useState } from 'react';\n\ninterface TransactionFormProps {\n  categories: Category[];\n  onSubmit: (transaction: Omit<Transaction, 'id' | 'user_id' | 'created_at'>) => boolean;\n  onCancel: () => void;\n  initialData?: Partial<Transaction>;\n}\n\nexport default function TransactionForm({\n  categories,\n  onSubmit,\n  onCancel,\n  initialData,\n}: TransactionFormProps) {\n  const [amount, setAmount] = useState(initialData?.amount?.toString() || '');\n  const [description, setDescription] = useState(initialData?.description || '');\n  const [categoryId, setCategoryId] = useState(initialData?.category_id || '');\n  const [date, setDate] = useState(\n    initialData?.date\n      ? new Date(initialData.date).toISOString().split('T')[0]\n      : new Date().toISOString().split('T')[0]\n  );\n  const [isIncome, setIsIncome] = useState(initialData?.is_income || false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Check amount - must be a valid positive number\n    if (!amount || amount.trim() === '') {\n      newErrors.amount = 'Amount is required';\n    } else if (isNaN(Number(amount))) {\n      newErrors.amount = 'Amount must be a valid number';\n    } else if (Number(amount) <= 0) {\n      newErrors.amount = 'Amount must be greater than 0';\n    }\n\n    // Check description - must not be empty\n    if (!description || description.trim() === '') {\n      newErrors.description = 'Description is required';\n    }\n\n    // Check category - must be selected\n    if (!categoryId) {\n      newErrors.categoryId = 'Please select a category';\n    }\n\n    // Check date - must be valid\n    if (!date) {\n      newErrors.date = 'Date is required';\n    }\n\n    // Set errors in state so they show up in the UI\n    setErrors(newErrors);\n\n    // Log validation results for debugging\n    const isValid = Object.keys(newErrors).length === 0;\n    console.log('Form validation:', { isValid, errors: newErrors, formData: { amount, description, categoryId, date } });\n\n    return isValid;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('Form submission started');\n\n    // Clear previous errors\n    setErrors({});\n\n    // Validate the form\n    if (!validateForm()) {\n      console.log('Form validation failed, stopping submission');\n      return; // Stop if validation fails\n    }\n\n    try {\n      // Set submitting state to prevent multiple submissions\n      setIsSubmitting(true);\n      console.log('Form is valid, submitting data:', {\n        amount: Number(amount),\n        description,\n        category_id: categoryId,\n        date: new Date(date).toISOString(),\n        is_income: isIncome,\n      });\n\n      // Submit the transaction data and get success status\n      const success = onSubmit({\n        amount: Number(amount),\n        description,\n        category_id: categoryId,\n        date: new Date(date).toISOString(),\n        is_income: isIncome,\n      });\n\n      console.log('Submission result:', success);\n\n      // If submission failed, show error and stay on form\n      if (!success) {\n        console.log('Submission returned false, showing error');\n        setErrors({ form: 'Failed to save transaction. Please try again.' });\n        setIsSubmitting(false);\n      }\n\n    } catch (error) {\n      console.error('Error submitting transaction:', error);\n      setErrors({ form: 'Failed to save transaction. Please try again.' });\n      setIsSubmitting(false); // Reset submitting state on error\n    }\n  };\n\n  const filteredCategories = categories.filter(\n    (category) => category.is_income === isIncome\n  );\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <label className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">Transaction Type</label>\n            <div className=\"flex items-center space-x-4 mt-2\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"expense\"\n                  name=\"transaction-type\"\n                  type=\"radio\"\n                  checked={!isIncome}\n                  onChange={() => {\n                    setIsIncome(false);\n                    setCategoryId('');\n                  }}\n                  className=\"w-4 h-4 text-error-light dark:text-error-dark border-light-border dark:border-dark-border focus:ring-error-light dark:focus:ring-error-dark\"\n                />\n                <label htmlFor=\"expense\" className=\"ml-2 block text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n                  Expense\n                </label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  id=\"income\"\n                  name=\"transaction-type\"\n                  type=\"radio\"\n                  checked={isIncome}\n                  onChange={() => {\n                    setIsIncome(true);\n                    setCategoryId('');\n                  }}\n                  className=\"w-4 h-4 text-success-light dark:text-success-dark border-light-border dark:border-dark-border focus:ring-success-light dark:focus:ring-success-dark\"\n                />\n                <label htmlFor=\"income\" className=\"ml-2 block text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n                  Income\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div>\n        <label htmlFor=\"amount\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Amount\n        </label>\n        <div className=\"relative mt-1 rounded-md shadow-sm\">\n          <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n            <span className=\"text-light-text-secondary dark:text-dark-text-secondary sm:text-sm\">$</span>\n          </div>\n          <input\n            type=\"number\"\n            name=\"amount\"\n            id=\"amount\"\n            step=\"0.01\"\n            min=\"0\"\n            value={amount}\n            onChange={(e) => setAmount(e.target.value)}\n            className=\"block w-full pl-7 pr-12 bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md focus:ring-primary focus:border-primary sm:text-sm\"\n            placeholder=\"0.00\"\n          />\n        </div>\n        {errors.amount && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.amount}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"description\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Description\n        </label>\n        <div className=\"mt-1\">\n          <input\n            type=\"text\"\n            name=\"description\"\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            className=\"block w-full bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\n          />\n        </div>\n        {errors.description && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.description}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"category\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Category\n        </label>\n        <div className=\"mt-1\">\n          <select\n            id=\"category\"\n            name=\"category\"\n            value={categoryId}\n            onChange={(e) => setCategoryId(e.target.value)}\n            className=\"block w-full bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\n          >\n            <option value=\"\">Select a category</option>\n            {filteredCategories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n        </div>\n        {errors.categoryId && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.categoryId}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"date\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Date\n        </label>\n        <div className=\"mt-1\">\n          <input\n            type=\"date\"\n            name=\"date\"\n            id=\"date\"\n            value={date}\n            onChange={(e) => setDate(e.target.value)}\n            className=\"block w-full bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\n          />\n        </div>\n        {errors.date && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.date}</p>}\n      </div>\n\n      {/* General form error message */}\n      {errors.form && (\n        <div className=\"p-3 rounded-md bg-error-light/10 dark:bg-error-dark/10 border border-error-light/20 dark:border-error-dark/20\">\n          <p className=\"text-sm text-error-light dark:text-error-dark\">{errors.form}</p>\n        </div>\n      )}\n\n      <div className=\"flex justify-end space-x-3\">\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          disabled={isSubmitting}\n          className=\"px-4 py-2 text-sm font-medium text-light-text-primary dark:text-dark-text-primary bg-light-accent dark:bg-dark-accent border border-light-border dark:border-dark-border rounded-lg shadow-sm hover:bg-light-border dark:hover:bg-dark-border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Cancel\n        </button>\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className=\"px-4 py-2 text-sm font-medium text-dark-bg bg-primary border border-transparent rounded-lg shadow-sm hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center\"\n        >\n          {isSubmitting ? (\n            <>\n              <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              Saving...\n            </>\n          ) : (\n            'Save'\n          )}\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAYe,SAAS,gBAAgB,EACtC,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,WAAW,EACU;;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,QAAQ,cAAc;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,eAAe;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,eAAe;IACzE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7B,aAAa,OACT,IAAI,KAAK,YAAY,IAAI,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GACtD,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAE5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,aAAa;IACnE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,iDAAiD;QACjD,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;YACnC,UAAU,MAAM,GAAG;QACrB,OAAO,IAAI,MAAM,OAAO,UAAU;YAChC,UAAU,MAAM,GAAG;QACrB,OAAO,IAAI,OAAO,WAAW,GAAG;YAC9B,UAAU,MAAM,GAAG;QACrB;QAEA,wCAAwC;QACxC,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,UAAU,WAAW,GAAG;QAC1B;QAEA,oCAAoC;QACpC,IAAI,CAAC,YAAY;YACf,UAAU,UAAU,GAAG;QACzB;QAEA,6BAA6B;QAC7B,IAAI,CAAC,MAAM;YACT,UAAU,IAAI,GAAG;QACnB;QAEA,gDAAgD;QAChD,UAAU;QAEV,uCAAuC;QACvC,MAAM,UAAU,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;QAClD,QAAQ,GAAG,CAAC,oBAAoB;YAAE;YAAS,QAAQ;YAAW,UAAU;gBAAE;gBAAQ;gBAAa;gBAAY;YAAK;QAAE;QAElH,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC;QAEZ,wBAAwB;QACxB,UAAU,CAAC;QAEX,oBAAoB;QACpB,IAAI,CAAC,gBAAgB;YACnB,QAAQ,GAAG,CAAC;YACZ,QAAQ,2BAA2B;QACrC;QAEA,IAAI;YACF,uDAAuD;YACvD,gBAAgB;YAChB,QAAQ,GAAG,CAAC,mCAAmC;gBAC7C,QAAQ,OAAO;gBACf;gBACA,aAAa;gBACb,MAAM,IAAI,KAAK,MAAM,WAAW;gBAChC,WAAW;YACb;YAEA,qDAAqD;YACrD,MAAM,UAAU,SAAS;gBACvB,QAAQ,OAAO;gBACf;gBACA,aAAa;gBACb,MAAM,IAAI,KAAK,MAAM,WAAW;gBAChC,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAElC,oDAAoD;YACpD,IAAI,CAAC,SAAS;gBACZ,QAAQ,GAAG,CAAC;gBACZ,UAAU;oBAAE,MAAM;gBAAgD;gBAClE,gBAAgB;YAClB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,UAAU;gBAAE,MAAM;YAAgD;YAClE,gBAAgB,QAAQ,kCAAkC;QAC5D;IACF;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAC1C,CAAC,WAAa,SAAS,SAAS,KAAK;IAGvC,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAgF;;;;;;0CACjG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,SAAS,CAAC;gDACV,UAAU;oDACR,YAAY;oDACZ,cAAc;gDAChB;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAA6E;;;;;;;;;;;;kDAIlH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU;oDACR,YAAY;oDACZ,cAAc;gDAChB;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzH,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAS,WAAU;kCAAgF;;;;;;kCAGlH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqE;;;;;;;;;;;0CAEvF,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;gCACV,aAAY;;;;;;;;;;;;oBAGf,OAAO,MAAM,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,MAAM;;;;;;;;;;;;0BAGpG,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAAgF;;;;;;kCAGvH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;oBAGb,OAAO,WAAW,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,WAAW;;;;;;;;;;;;0BAG9G,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAW,WAAU;kCAAgF;;;;;;kCAGpH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;gCAChB,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;wCAAyB,OAAO,SAAS,EAAE;kDACzC,SAAS,IAAI;uCADH,SAAS,EAAE;;;;;;;;;;;;;;;;oBAM7B,OAAO,UAAU,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,UAAU;;;;;;;;;;;;0BAG5G,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAO,WAAU;kCAAgF;;;;;;kCAGhH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4BACvC,WAAU;;;;;;;;;;;oBAGb,OAAO,IAAI,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,IAAI;;;;;;;;;;;;YAI/F,OAAO,IAAI,kBACV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAiD,OAAO,IAAI;;;;;;;;;;;0BAI7E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,6BACC;;8CACE,6LAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,6LAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,6LAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR;;;;;;;;;;;;;;;;;;AAMZ;GA5QwB;KAAA", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/eventBus.ts"], "sourcesContent": ["'use client';\n\n/**\n * Simple event bus for cross-component communication\n * This allows different parts of the application to communicate\n * without direct dependencies\n */\n\nexport type EventType = \n  | 'transaction_created'\n  | 'transaction_updated'\n  | 'transaction_deleted'\n  | 'transactions_changed';\n\nexport interface EventData {\n  userId?: string;\n  transactionId?: string;\n  [key: string]: any;\n}\n\n// Custom event name for our application\nconst EVENT_NAME = 'cashminder_event';\n\n/**\n * Emit an event to notify other components about a change\n */\nexport function emitEvent(type: EventType, data: EventData = {}) {\n  // Create a custom event with our data\n  const event = new CustomEvent(EVENT_NAME, {\n    detail: { type, data, timestamp: new Date().toISOString() }\n  });\n  \n  // Dispatch the event on the window object\n  window.dispatchEvent(event);\n  \n  console.log(`Event emitted: ${type}`, data);\n}\n\n/**\n * Listen for events of a specific type\n */\nexport function listenEvent(type: EventType, callback: (data: EventData) => void) {\n  const handler = (event: Event) => {\n    const customEvent = event as CustomEvent;\n    if (customEvent.detail && customEvent.detail.type === type) {\n      callback(customEvent.detail.data);\n    }\n  };\n  \n  // Add event listener\n  window.addEventListener(EVENT_NAME, handler);\n  \n  // Return a function to remove the listener\n  return () => {\n    window.removeEventListener(EVENT_NAME, handler);\n  };\n}\n\n/**\n * Helper function to refresh transactions for a user\n * This will emit an event that all transaction-dependent components should listen for\n */\nexport function refreshTransactions(userId: string) {\n  emitEvent('transactions_changed', { userId });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAoBA,wCAAwC;AACxC,MAAM,aAAa;AAKZ,SAAS,UAAU,IAAe,EAAE,OAAkB,CAAC,CAAC;IAC7D,sCAAsC;IACtC,MAAM,QAAQ,IAAI,YAAY,YAAY;QACxC,QAAQ;YAAE;YAAM;YAAM,WAAW,IAAI,OAAO,WAAW;QAAG;IAC5D;IAEA,0CAA0C;IAC1C,OAAO,aAAa,CAAC;IAErB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,EAAE;AACxC;AAKO,SAAS,YAAY,IAAe,EAAE,QAAmC;IAC9E,MAAM,UAAU,CAAC;QACf,MAAM,cAAc;QACpB,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,IAAI,KAAK,MAAM;YAC1D,SAAS,YAAY,MAAM,CAAC,IAAI;QAClC;IACF;IAEA,qBAAqB;IACrB,OAAO,gBAAgB,CAAC,YAAY;IAEpC,2CAA2C;IAC3C,OAAO;QACL,OAAO,mBAAmB,CAAC,YAAY;IACzC;AACF;AAMO,SAAS,oBAAoB,MAAc;IAChD,UAAU,wBAAwB;QAAE;IAAO;AAC7C", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/app/transactions/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Category, Transaction } from '@/lib/types';\nimport { useRouter } from 'next/navigation';\nimport TransactionList from '@/components/transactions/TransactionList';\nimport TransactionForm from '@/components/transactions/TransactionForm';\nimport { FiPlus, FiFilter } from 'react-icons/fi';\nimport { emitEvent, listenEvent, refreshTransactions } from '@/lib/eventBus';\n\n// Mock data (same as in dashboard)\nconst mockCategories: Category[] = [\n  { id: '1', name: 'Housing', color: '#4F46E5', is_income: false, is_default: true },\n  { id: '2', name: 'Food', color: '#10B981', is_income: false, is_default: true },\n  { id: '3', name: 'Transportation', color: '#F59E0B', is_income: false, is_default: true },\n  { id: '4', name: 'Entertainment', color: '#EC4899', is_income: false, is_default: true },\n  { id: '5', name: 'Utilities', color: '#6366F1', is_income: false, is_default: true },\n  { id: '6', name: 'Sal<PERSON>', color: '#34D399', is_income: true, is_default: true },\n];\n\nconst mockTransactions: Transaction[] = [\n  {\n    id: '1',\n    user_id: '1',\n    amount: 2000,\n    description: 'Salary',\n    category_id: '6',\n    date: new Date().toISOString(),\n    is_income: true,\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    user_id: '1',\n    amount: 800,\n    description: 'Rent',\n    category_id: '1',\n    date: new Date().toISOString(),\n    is_income: false,\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    user_id: '1',\n    amount: 150,\n    description: 'Groceries',\n    category_id: '2',\n    date: new Date().toISOString(),\n    is_income: false,\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '4',\n    user_id: '1',\n    amount: 50,\n    description: 'Gas',\n    category_id: '3',\n    date: new Date().toISOString(),\n    is_income: false,\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '5',\n    user_id: '1',\n    amount: 30,\n    description: 'Movie tickets',\n    category_id: '4',\n    date: new Date().toISOString(),\n    is_income: false,\n    created_at: new Date().toISOString(),\n  },\n];\n\nexport default function TransactionsPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);\n  const [categories, setCategories] = useState<Category[]>(mockCategories);\n  const [showForm, setShowForm] = useState(false);\n  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);\n  const [filter, setFilter] = useState<'all' | 'income' | 'expense'>('all');\n\n  // Load transactions from localStorage\n  const loadTransactions = () => {\n    // Check if user is logged in using localStorage\n    const userJson = localStorage.getItem('cashminder_user');\n\n    if (!userJson) {\n      router.push('/auth/login');\n      return;\n    }\n\n    try {\n      // Parse user data\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n      const isNewUser = userData.isNewUser === true;\n\n      // If this is a new user, show empty data\n      if (isNewUser) {\n        // For new users, initialize with empty data\n        setTransactions([]);\n      } else {\n        // For returning users, try to load their data from localStorage\n        const storedTransactions = localStorage.getItem(`cashminder_transactions_${userId}`);\n\n        if (storedTransactions) {\n          setTransactions(JSON.parse(storedTransactions));\n        }\n        // If no stored transactions, keep the empty array (don't use mock data)\n      }\n    } catch (error) {\n      console.error('Error loading user data:', error);\n      // Don't use mock data for transactions, start with empty\n      setTransactions([]);\n    }\n\n    setIsLoading(false);\n  };\n\n  // Initial load\n  useEffect(() => {\n    loadTransactions();\n  }, [router]);\n\n  // Listen for transaction changes\n  useEffect(() => {\n    // Set up event listener for transaction changes\n    const removeListener = listenEvent('transactions_changed', (data) => {\n      // Reload transactions when they change\n      loadTransactions();\n    });\n\n    // Clean up event listener on unmount\n    return () => {\n      removeListener();\n    };\n  }, []);\n\n  const handleAddTransaction = () => {\n    setEditingTransaction(null);\n    setShowForm(true);\n  };\n\n  const handleEditTransaction = (transaction: Transaction) => {\n    setEditingTransaction(transaction);\n    setShowForm(true);\n  };\n\n  const handleDeleteTransaction = (transactionId: string) => {\n    if (window.confirm('Are you sure you want to delete this transaction?')) {\n      try {\n        // Get the current user\n        const userJson = localStorage.getItem('cashminder_user');\n        if (!userJson) return;\n\n        const userData = JSON.parse(userJson);\n        const userId = userData.id || 'default';\n\n        // Find the transaction before removing it\n        const transactionToDelete = transactions.find(t => t.id === transactionId);\n\n        // Update transactions in state\n        const updatedTransactions = transactions.filter((t) => t.id !== transactionId);\n        setTransactions(updatedTransactions);\n\n        // Save to localStorage\n        localStorage.setItem(`cashminder_transactions_${userId}`, JSON.stringify(updatedTransactions));\n\n        // Emit event to notify other components\n        if (transactionToDelete) {\n          emitEvent('transaction_deleted', {\n            userId,\n            transactionId,\n            transaction: transactionToDelete\n          });\n        }\n\n        // Also emit a general transactions_changed event\n        refreshTransactions(userId);\n      } catch (error) {\n        console.error('Error deleting transaction:', error);\n        alert('Failed to delete transaction. Please try again.');\n      }\n    }\n  };\n\n  const handleSubmitTransaction = (transactionData: Omit<Transaction, 'id' | 'user_id' | 'created_at'>): boolean => {\n    console.log('Parent component received transaction data:', transactionData);\n\n    try {\n      // Get the current user\n      const userJson = localStorage.getItem('cashminder_user');\n      if (!userJson) {\n        console.error('No user found in localStorage');\n        return false;\n      }\n\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n      console.log('User ID:', userId);\n\n      let updatedTransactions: Transaction[];\n      let eventType: 'transaction_created' | 'transaction_updated';\n      let affectedTransaction: Transaction;\n\n      if (editingTransaction) {\n        // Update existing transaction\n        const updatedTransaction = {\n          ...editingTransaction,\n          ...transactionData\n        };\n\n        updatedTransactions = transactions.map((t) =>\n          t.id === editingTransaction.id ? updatedTransaction : t\n        );\n\n        eventType = 'transaction_updated';\n        affectedTransaction = updatedTransaction;\n      } else {\n        // Add new transaction\n        const newTransaction: Transaction = {\n          id: `transaction_${Date.now()}`,\n          user_id: userId,\n          created_at: new Date().toISOString(),\n          ...transactionData,\n        };\n        updatedTransactions = [newTransaction, ...transactions];\n\n        eventType = 'transaction_created';\n        affectedTransaction = newTransaction;\n      }\n\n      // Update state\n      setTransactions(updatedTransactions);\n\n      // Save to localStorage\n      localStorage.setItem(`cashminder_transactions_${userId}`, JSON.stringify(updatedTransactions));\n\n      // Emit event to notify other components\n      emitEvent(eventType, {\n        userId,\n        transactionId: affectedTransaction.id,\n        transaction: affectedTransaction\n      });\n\n      // Also emit a general transactions_changed event\n      refreshTransactions(userId);\n\n      // Hide the form only after successful submission\n      console.log('Transaction saved successfully, hiding form');\n      setShowForm(false);\n      setEditingTransaction(null);\n\n      // Return success\n      return true;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      // Return failure - don't hide the form\n      console.log('Transaction save failed, keeping form visible');\n      return false;\n    }\n  };\n\n  const filteredTransactions = transactions.filter((t) => {\n    if (filter === 'all') return true;\n    if (filter === 'income') return t.is_income;\n    if (filter === 'expense') return !t.is_income;\n    return true;\n  });\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-xl text-light-text-secondary dark:text-dark-text-secondary\">\n          <svg className=\"animate-spin -ml-1 mr-3 h-8 w-8 text-primary inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading transactions...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8 max-w-7xl\">\n      <div className=\"pb-5 border-b border-light-border dark:border-dark-border sm:flex sm:items-center sm:justify-between mb-8\">\n        <h1 className=\"text-3xl font-bold leading-tight text-light-text-primary dark:text-dark-text-primary\">Transactions</h1>\n        <div className=\"mt-3 flex sm:mt-0 sm:ml-4\">\n          <div className=\"mr-2\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <FiFilter className=\"text-light-text-muted dark:text-dark-text-muted\" />\n              </div>\n              <select\n                value={filter}\n                onChange={(e) => setFilter(e.target.value as 'all' | 'income' | 'expense')}\n                className=\"block w-full pl-10 pr-10 py-2 text-base bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-secondary dark:text-dark-text-secondary focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-lg\"\n              >\n                <option value=\"all\">All Transactions</option>\n                <option value=\"income\">Income Only</option>\n                <option value=\"expense\">Expenses Only</option>\n              </select>\n            </div>\n          </div>\n          <button\n            type=\"button\"\n            onClick={handleAddTransaction}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-dark-bg bg-primary hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\n          >\n            <FiPlus className=\"-ml-1 mr-2 h-5 w-5\" />\n            Add Transaction\n          </button>\n        </div>\n      </div>\n\n      {showForm ? (\n        <div className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm\">\n          <h2 className=\"text-lg font-medium text-light-text-primary dark:text-dark-text-primary mb-4\">\n            {editingTransaction ? 'Edit Transaction' : 'Add Transaction'}\n          </h2>\n          <TransactionForm\n            categories={categories}\n            onSubmit={handleSubmitTransaction}\n            onCancel={() => {\n              setShowForm(false);\n              setEditingTransaction(null);\n            }}\n            initialData={editingTransaction || undefined}\n          />\n        </div>\n      ) : (\n        <div>\n          <TransactionList\n            transactions={filteredTransactions}\n            categories={categories}\n            onEdit={handleEditTransaction}\n            onDelete={handleDeleteTransaction}\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAUA,mCAAmC;AACnC,MAAM,iBAA6B;IACjC;QAAE,IAAI;QAAK,MAAM;QAAW,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACjF;QAAE,IAAI;QAAK,MAAM;QAAQ,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IAC9E;QAAE,IAAI;QAAK,MAAM;QAAkB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACxF;QAAE,IAAI;QAAK,MAAM;QAAiB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACvF;QAAE,IAAI;QAAK,MAAM;QAAa,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACnF;QAAE,IAAI;QAAK,MAAM;QAAU,OAAO;QAAW,WAAW;QAAM,YAAY;IAAK;CAChF;AAED,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,aAAa;QACb,MAAM,IAAI,OAAO,WAAW;QAC5B,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,aAAa;QACb,MAAM,IAAI,OAAO,WAAW;QAC5B,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,aAAa;QACb,MAAM,IAAI,OAAO,WAAW;QAC5B,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,aAAa;QACb,MAAM,IAAI,OAAO,WAAW;QAC5B,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,aAAa;QACb,aAAa;QACb,MAAM,IAAI,OAAO,WAAW;QAC5B,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;IACpC;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACjF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAEnE,sCAAsC;IACtC,MAAM,mBAAmB;QACvB,gDAAgD;QAChD,MAAM,WAAW,aAAa,OAAO,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAC9B,MAAM,YAAY,SAAS,SAAS,KAAK;YAEzC,yCAAyC;YACzC,IAAI,WAAW;gBACb,4CAA4C;gBAC5C,gBAAgB,EAAE;YACpB,OAAO;gBACL,gEAAgE;gBAChE,MAAM,qBAAqB,aAAa,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ;gBAEnF,IAAI,oBAAoB;oBACtB,gBAAgB,KAAK,KAAK,CAAC;gBAC7B;YACA,wEAAwE;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,yDAAyD;YACzD,gBAAgB,EAAE;QACpB;QAEA,aAAa;IACf;IAEA,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAO;IAEX,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,gDAAgD;YAChD,MAAM,iBAAiB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;6DAAwB,CAAC;oBAC1D,uCAAuC;oBACvC;gBACF;;YAEA,qCAAqC;YACrC;8CAAO;oBACL;gBACF;;QACF;qCAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,OAAO,OAAO,CAAC,sDAAsD;YACvE,IAAI;gBACF,uBAAuB;gBACvB,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,IAAI,CAAC,UAAU;gBAEf,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;gBAE9B,0CAA0C;gBAC1C,MAAM,sBAAsB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE5D,+BAA+B;gBAC/B,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAChE,gBAAgB;gBAEhB,uBAAuB;gBACvB,aAAa,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;gBAEzE,wCAAwC;gBACxC,IAAI,qBAAqB;oBACvB,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB;wBAC/B;wBACA;wBACA,aAAa;oBACf;gBACF;gBAEA,iDAAiD;gBACjD,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM;YACR;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,QAAQ,GAAG,CAAC,+CAA+C;QAE3D,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACb,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAC9B,QAAQ,GAAG,CAAC,YAAY;YAExB,IAAI;YACJ,IAAI;YACJ,IAAI;YAEJ,IAAI,oBAAoB;gBACtB,8BAA8B;gBAC9B,MAAM,qBAAqB;oBACzB,GAAG,kBAAkB;oBACrB,GAAG,eAAe;gBACpB;gBAEA,sBAAsB,aAAa,GAAG,CAAC,CAAC,IACtC,EAAE,EAAE,KAAK,mBAAmB,EAAE,GAAG,qBAAqB;gBAGxD,YAAY;gBACZ,sBAAsB;YACxB,OAAO;gBACL,sBAAsB;gBACtB,MAAM,iBAA8B;oBAClC,IAAI,CAAC,YAAY,EAAE,KAAK,GAAG,IAAI;oBAC/B,SAAS;oBACT,YAAY,IAAI,OAAO,WAAW;oBAClC,GAAG,eAAe;gBACpB;gBACA,sBAAsB;oBAAC;uBAAmB;iBAAa;gBAEvD,YAAY;gBACZ,sBAAsB;YACxB;YAEA,eAAe;YACf,gBAAgB;YAEhB,uBAAuB;YACvB,aAAa,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;YAEzE,wCAAwC;YACxC,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE,WAAW;gBACnB;gBACA,eAAe,oBAAoB,EAAE;gBACrC,aAAa;YACf;YAEA,iDAAiD;YACjD,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;YAEpB,iDAAiD;YACjD,QAAQ,GAAG,CAAC;YACZ,YAAY;YACZ,sBAAsB;YAEtB,iBAAiB;YACjB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,uCAAuC;YACvC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAC;QAChD,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,UAAU,OAAO,EAAE,SAAS;QAC3C,IAAI,WAAW,WAAW,OAAO,CAAC,EAAE,SAAS;QAC7C,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA4D,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CAChI,6LAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,6LAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuF;;;;;;kCACrG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;YAM9C,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,qBAAqB,qBAAqB;;;;;;kCAE7C,6LAAC,wJAAA,CAAA,UAAe;wBACd,YAAY;wBACZ,UAAU;wBACV,UAAU;4BACR,YAAY;4BACZ,sBAAsB;wBACxB;wBACA,aAAa,sBAAsB;;;;;;;;;;;qCAIvC,6LAAC;0BACC,cAAA,6LAAC,wJAAA,CAAA,UAAe;oBACd,cAAc;oBACd,YAAY;oBACZ,QAAQ;oBACR,UAAU;;;;;;;;;;;;;;;;;AAMtB;GA/QwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}