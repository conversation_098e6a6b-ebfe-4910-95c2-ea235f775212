@import "tailwindcss/preflight";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Fix for select dropdown options in dark mode */
html.dark select {
  background-color: #0A0F1A !important; /* Dark mode background */
  color: #E2E8F0 !important; /* Dark mode text */
}

html.dark select option {
  background-color: #0A0F1A !important; /* Dark mode background */
  color: #E2E8F0 !important; /* Dark mode text */
}

/* Fix for light mode */
select option {
  background-color: #FFFFFF !important; /* Light mode background */
  color: #1A202C !important; /* Light mode text */
}


