{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/layout/FuturisticNavbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/FuturisticNavbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/FuturisticNavbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/layout/FuturisticNavbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/FuturisticNavbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/FuturisticNavbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/ThemeWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ThemeWrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ThemeWrapper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/ThemeWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ThemeWrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ThemeWrapper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\n// Using CSS imports for fonts instead of Next.js font optimization to avoid build hanging\nimport \"./globals.css\";\nimport FuturisticNavbar from '@/components/layout/FuturisticNavbar';\nimport ThemeWrapper from '@/components/ThemeWrapper';\n\n// Fonts are now loaded via CSS imports in globals.css\n\nexport const metadata: Metadata = {\n  title: \"Cashminder - Personal Finance Manager\",\n  description: \"Track your expenses, manage your budget, and achieve your financial goals\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        {/*\n          We're removing the inline script to avoid hydration issues with browser extensions.\n          Theme handling will be done entirely client-side in the ThemeProvider component.\n        */}\n        <title>Cashminder - Smart Money Management</title>\n        <meta name=\"description\" content=\"Take control of your finances with <PERSON><PERSON><PERSON>'s intelligent money management tools\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n\n{/* Fonts will be added after server is stable */}\n\n        <script dangerouslySetInnerHTML={{\n          __html: `\n            // Block Sentry requests to prevent console errors\n            const originalFetch = window.fetch;\n            window.fetch = function(url, options) {\n              if (url && typeof url === 'string' && url.includes('sentry')) {\n                console.log('Blocked Sentry request:', url);\n                return Promise.resolve(new Response('', { status: 200 }));\n              }\n              return originalFetch.apply(this, arguments);\n            };\n          `\n        }} />\n      </head>\n      <body\n        className=\"font-inter antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300\"\n      >\n        <ThemeWrapper>\n          <div className=\"flex flex-col min-h-screen\">\n            <div className=\"flex-grow\">\n              {/* Futuristic Navbar */}\n              <FuturisticNavbar />\n              <main className=\"py-10\">\n                <div className=\"px-4 mx-auto max-w-7xl sm:px-6 lg:px-8\">\n                  {children}\n                </div>\n              </main>\n            </div>\n          </div>\n        </ThemeWrapper>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCAKC,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCAItB,8OAAC;wBAAO,yBAAyB;4BAC/B,QAAQ,CAAC;;;;;;;;;;UAUT,CAAC;wBACH;;;;;;;;;;;;0BAEF,8OAAC;gBACC,WAAU;0BAEV,cAAA,8OAAC,kIAAA,CAAA,UAAY;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gJAAA,CAAA,UAAgB;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnB", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}