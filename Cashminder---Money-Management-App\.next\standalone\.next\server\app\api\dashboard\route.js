(()=>{var e={};e.id=618,e.ids=[618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(56037),n=r.n(s),a=r(82598);let o=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,match:[/^\S+@\S+\.\S+$/,"Please enter a valid email address"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters long"]},name:{type:String,trim:!0},avatar:{type:String},isNewUser:{type:Boolean,default:!0},role:{type:String,enum:["user","admin"],default:"user"},isVerified:{type:Boolean,default:!1},verificationToken:String,resetPasswordToken:String,resetPasswordExpires:Date,lastLogin:{type:Date},preferences:{currency:{type:String,default:"USD"},theme:{type:String,default:"light"},language:{type:String,default:"en"},notifications:{type:Boolean,default:!0},dashboardLayout:{type:String}},profile:{bio:String,location:String,website:String,phone:String},stats:{totalTransactions:{type:Number,default:0},totalSavings:{type:Number,default:0},streakDays:{type:Number,default:0},lastActive:{type:Date,default:Date.now}}},{timestamps:!0});o.pre("save",async function(e){if(!this.isModified("password"))return e();try{this.password=await (0,a.E)(this.password),e()}catch(t){e(t)}});let i=n().models.User||n().model("User",o)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},57758:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),n=r.n(s);let a=new s.Schema({userId:{type:s.Schema.Types.ObjectId,ref:"User",required:!0},amount:{type:Number,required:!0},description:{type:String,required:!0},category:{type:String,required:!0},categoryId:{type:s.Schema.Types.ObjectId,ref:"Category"},date:{type:Date,default:Date.now},type:{type:String,enum:["income","expense"],required:!0},paymentMethod:{type:String},tags:[{type:String}],location:{type:String},notes:{type:String},isRecurring:{type:Boolean,default:!1},recurringDetails:{frequency:{type:String,enum:["daily","weekly","monthly","yearly"]},endDate:{type:Date}}},{timestamps:!0}),o=n().models.Transaction||n().model("Transaction",a)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(56037),n=r.n(s);let a=process.env.MONGODB_URI||"";if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let i=async function(){if(o.conn)return o.conn;o.promise||(o.promise=n().connect(a).then(e=>(console.log("MongoDB connected successfully"),e)).catch(e=>{throw console.error("MongoDB connection error:",e),e}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},82598:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,b:()=>a});var s=r(85663);async function n(e){return s.Ay.hash(e,10)}async function a(e,t){return s.Ay.compare(e,t)}},96487:()=>{},99278:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>y});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(75745),c=r(17063),d=r(57758),p=r(56037),l=r.n(p);async function y(e){try{let t=e.nextUrl.searchParams.get("userId");if(!t)return i.NextResponse.json({success:!1,error:"User ID is required"},{status:400});if(await (0,u.A)(),!l().Types.ObjectId.isValid(t))return i.NextResponse.json({success:!1,error:"Invalid user ID format"},{status:400});if(!await c.A.findById(t))return i.NextResponse.json({success:!1,error:"User not found"},{status:404});let r=await d.A.find({userId:t}).sort({date:-1}),s=r.filter(e=>"income"===e.type).reduce((e,t)=>e+t.amount,0),n=r.filter(e=>"expense"===e.type).reduce((e,t)=>e+t.amount,0),a=s-n,o={current:a>0?.2*a:0,target:1e4,percentage:0};return o.percentage=Math.min(100,Math.round(o.current/o.target*100)),i.NextResponse.json({success:!0,data:{totalBalance:a,income:s,expenses:n,savingsGoal:o,recentTransactions:r.slice(0,5)}})}catch(e){return console.error("Error fetching dashboard data:",e),i.NextResponse.json({success:!1,error:"Failed to fetch dashboard data",details:e instanceof Error?e.message:String(e)},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/dashboard/route",pathname:"/api/dashboard",filename:"route",bundlePath:"app/api/dashboard/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\api\\dashboard\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:h}=m;function S(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,663],()=>r(99278));module.exports=s})();