{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/lib/analytics.ts"], "sourcesContent": ["'use client';\n\nimport { Transaction, Category } from './types';\n\nexport interface AnalyticsSummary {\n  totalIncome: number;\n  totalExpenses: number;\n  netSavings: number;\n  incomeChange: number;\n  expenseChange: number;\n  savingsChange: number;\n  monthlyData: {\n    month: string;\n    income: number;\n    expenses: number;\n  }[];\n  categoryData: {\n    category: string;\n    amount: number;\n    color: string;\n  }[];\n  insights: {\n    title: string;\n    description: string;\n    type: 'positive' | 'negative' | 'neutral';\n  }[];\n}\n\nexport interface TimeRange {\n  startDate: Date;\n  endDate: Date;\n  label: string;\n}\n\nexport function getTimeRanges(): { [key: string]: TimeRange } {\n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  \n  // Last 7 days\n  const last7Start = new Date(today);\n  last7Start.setDate(today.getDate() - 6);\n  \n  // Last 30 days\n  const last30Start = new Date(today);\n  last30Start.setDate(today.getDate() - 29);\n  \n  // Last 3 months\n  const last3MonthsStart = new Date(today);\n  last3MonthsStart.setMonth(today.getMonth() - 3);\n  last3MonthsStart.setDate(1);\n  \n  // Last 6 months\n  const last6MonthsStart = new Date(today);\n  last6MonthsStart.setMonth(today.getMonth() - 6);\n  last6MonthsStart.setDate(1);\n  \n  // Year to date\n  const yearToDateStart = new Date(today.getFullYear(), 0, 1);\n  \n  return {\n    'last7days': {\n      startDate: last7Start,\n      endDate: today,\n      label: 'Last 7 days'\n    },\n    'last30days': {\n      startDate: last30Start,\n      endDate: today,\n      label: 'Last 30 days'\n    },\n    'last3months': {\n      startDate: last3MonthsStart,\n      endDate: today,\n      label: 'Last 3 months'\n    },\n    'last6months': {\n      startDate: last6MonthsStart,\n      endDate: today,\n      label: 'Last 6 months'\n    },\n    'yearToDate': {\n      startDate: yearToDateStart,\n      endDate: today,\n      label: 'Year to date'\n    }\n  };\n}\n\nexport function filterTransactionsByDateRange(transactions: Transaction[], startDate: Date, endDate: Date): Transaction[] {\n  return transactions.filter(transaction => {\n    const transactionDate = new Date(transaction.date);\n    return transactionDate >= startDate && transactionDate <= endDate;\n  });\n}\n\nexport function getPreviousPeriodTransactions(\n  transactions: Transaction[], \n  currentStartDate: Date, \n  currentEndDate: Date\n): Transaction[] {\n  const periodLength = currentEndDate.getTime() - currentStartDate.getTime();\n  const previousStartDate = new Date(currentStartDate.getTime() - periodLength);\n  const previousEndDate = new Date(currentEndDate.getTime() - periodLength);\n  \n  return filterTransactionsByDateRange(transactions, previousStartDate, previousEndDate);\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getMonthlyData(transactions: Transaction[], months = 6): { month: string; income: number; expenses: number }[] {\n  const result: { month: string; income: number; expenses: number }[] = [];\n  const now = new Date();\n  \n  // Initialize with zero values for the last 'months' months\n  for (let i = months - 1; i >= 0; i--) {\n    const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);\n    result.push({\n      month: monthDate.toLocaleString('default', { month: 'short' }),\n      income: 0,\n      expenses: 0\n    });\n  }\n  \n  // Fill in actual data\n  transactions.forEach(transaction => {\n    const transactionDate = new Date(transaction.date);\n    const monthDiff = (now.getFullYear() - transactionDate.getFullYear()) * 12 + now.getMonth() - transactionDate.getMonth();\n    \n    if (monthDiff >= 0 && monthDiff < months) {\n      const index = months - 1 - monthDiff;\n      if (transaction.is_income) {\n        result[index].income += transaction.amount;\n      } else {\n        result[index].expenses += transaction.amount;\n      }\n    }\n  });\n  \n  return result;\n}\n\nexport function getCategoryData(transactions: Transaction[], categories: Category[]): { category: string; amount: number; color: string }[] {\n  // Filter to only expense transactions\n  const expenseTransactions = transactions.filter(t => !t.is_income);\n  \n  // Group by category and sum amounts\n  const categoryAmounts: { [key: string]: number } = {};\n  expenseTransactions.forEach(transaction => {\n    if (!categoryAmounts[transaction.category_id]) {\n      categoryAmounts[transaction.category_id] = 0;\n    }\n    categoryAmounts[transaction.category_id] += transaction.amount;\n  });\n  \n  // Convert to required format\n  const result = Object.entries(categoryAmounts).map(([categoryId, amount]) => {\n    const category = categories.find(c => c.id === categoryId);\n    return {\n      category: category ? category.name : 'Unknown',\n      amount,\n      color: category?.color || '#6366f1'\n    };\n  });\n  \n  // Sort by amount (descending)\n  return result.sort((a, b) => b.amount - a.amount);\n}\n\nexport function generateInsights(\n  currentPeriodData: {\n    totalIncome: number;\n    totalExpenses: number;\n    netSavings: number;\n    categoryData: { category: string; amount: number; color: string }[];\n  },\n  previousPeriodData: {\n    totalIncome: number;\n    totalExpenses: number;\n    netSavings: number;\n    categoryData: { category: string; amount: number; color: string }[];\n  }\n): { title: string; description: string; type: 'positive' | 'negative' | 'neutral' }[] {\n  const insights: { title: string; description: string; type: 'positive' | 'negative' | 'neutral' }[] = [];\n  \n  // Savings rate insight\n  const savingsRate = currentPeriodData.totalIncome > 0 \n    ? (currentPeriodData.netSavings / currentPeriodData.totalIncome) * 100 \n    : 0;\n  \n  if (savingsRate > 20) {\n    insights.push({\n      title: `You saved ${savingsRate.toFixed(0)}% of your income this period`,\n      description: 'Great job! You\\'re saving a significant portion of your income.',\n      type: 'positive'\n    });\n  } else if (savingsRate > 0) {\n    insights.push({\n      title: `You saved ${savingsRate.toFixed(0)}% of your income this period`,\n      description: 'Consider setting a goal to save at least 20% of your income.',\n      type: 'neutral'\n    });\n  } else if (currentPeriodData.totalIncome > 0) {\n    insights.push({\n      title: 'Your expenses exceeded your income this period',\n      description: 'Try to reduce expenses or increase income to avoid depleting your savings.',\n      type: 'negative'\n    });\n  }\n  \n  // Category spending insights\n  if (currentPeriodData.categoryData.length > 0 && previousPeriodData.categoryData.length > 0) {\n    // Find the category with the biggest increase\n    const currentCategoryMap = new Map(\n      currentPeriodData.categoryData.map(item => [item.category, item.amount])\n    );\n    \n    const previousCategoryMap = new Map(\n      previousPeriodData.categoryData.map(item => [item.category, item.amount])\n    );\n    \n    let biggestIncrease = { category: '', change: 0, percentage: 0 };\n    \n    currentPeriodData.categoryData.forEach(({ category, amount }) => {\n      const previousAmount = previousCategoryMap.get(category) || 0;\n      if (previousAmount > 0) {\n        const change = amount - previousAmount;\n        const percentage = (change / previousAmount) * 100;\n        \n        if (percentage > 15 && change > biggestIncrease.change) {\n          biggestIncrease = { category, change, percentage };\n        }\n      }\n    });\n    \n    if (biggestIncrease.category) {\n      insights.push({\n        title: `Spending in ${biggestIncrease.category} increased by ${biggestIncrease.percentage.toFixed(0)}%`,\n        description: `Your spending on ${biggestIncrease.category} has increased compared to last period. Consider reviewing these expenses.`,\n        type: 'negative'\n      });\n    }\n    \n    // Top spending category\n    if (currentPeriodData.categoryData.length > 0) {\n      const topCategory = currentPeriodData.categoryData[0];\n      const topCategoryPercentage = (topCategory.amount / currentPeriodData.totalExpenses) * 100;\n      \n      insights.push({\n        title: `${topCategory.category} is your top spending category (${topCategoryPercentage.toFixed(0)}%)`,\n        description: `You spent $${topCategory.amount.toFixed(2)} on ${topCategory.category} this period.`,\n        type: 'neutral'\n      });\n    }\n  }\n  \n  // Income trend insight\n  const incomeChange = calculatePercentageChange(\n    currentPeriodData.totalIncome, \n    previousPeriodData.totalIncome\n  );\n  \n  if (Math.abs(incomeChange) > 10) {\n    insights.push({\n      title: `Your income ${incomeChange > 0 ? 'increased' : 'decreased'} by ${Math.abs(incomeChange).toFixed(0)}%`,\n      description: incomeChange > 0 \n        ? 'Great job increasing your income! Consider allocating some of this increase to savings.'\n        : 'Your income has decreased. You might need to adjust your budget accordingly.',\n      type: incomeChange > 0 ? 'positive' : 'negative'\n    });\n  }\n  \n  return insights;\n}\n\nexport function calculateAnalytics(\n  transactions: Transaction[], \n  categories: Category[],\n  timeRange: TimeRange\n): AnalyticsSummary {\n  // Filter transactions by date range\n  const currentPeriodTransactions = filterTransactionsByDateRange(\n    transactions,\n    timeRange.startDate,\n    timeRange.endDate\n  );\n  \n  // Get previous period transactions\n  const previousPeriodTransactions = getPreviousPeriodTransactions(\n    transactions,\n    timeRange.startDate,\n    timeRange.endDate\n  );\n  \n  // Calculate current period totals\n  const currentIncome = currentPeriodTransactions\n    .filter(t => t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n    \n  const currentExpenses = currentPeriodTransactions\n    .filter(t => !t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n    \n  const currentNetSavings = currentIncome - currentExpenses;\n  \n  // Calculate previous period totals\n  const previousIncome = previousPeriodTransactions\n    .filter(t => t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n    \n  const previousExpenses = previousPeriodTransactions\n    .filter(t => !t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n    \n  const previousNetSavings = previousIncome - previousExpenses;\n  \n  // Calculate percentage changes\n  const incomeChange = calculatePercentageChange(currentIncome, previousIncome);\n  const expenseChange = calculatePercentageChange(currentExpenses, previousExpenses);\n  const savingsChange = calculatePercentageChange(currentNetSavings, previousNetSavings);\n  \n  // Get monthly data\n  const monthlyData = getMonthlyData(transactions);\n  \n  // Get category data\n  const categoryData = getCategoryData(currentPeriodTransactions, categories);\n  \n  // Generate insights\n  const insights = generateInsights(\n    {\n      totalIncome: currentIncome,\n      totalExpenses: currentExpenses,\n      netSavings: currentNetSavings,\n      categoryData\n    },\n    {\n      totalIncome: previousIncome,\n      totalExpenses: previousExpenses,\n      netSavings: previousNetSavings,\n      categoryData: getCategoryData(previousPeriodTransactions, categories)\n    }\n  );\n  \n  return {\n    totalIncome: currentIncome,\n    totalExpenses: currentExpenses,\n    netSavings: currentNetSavings,\n    incomeChange,\n    expenseChange,\n    savingsChange,\n    monthlyData,\n    categoryData,\n    insights\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAkCO,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;IAErE,cAAc;IACd,MAAM,aAAa,IAAI,KAAK;IAC5B,WAAW,OAAO,CAAC,MAAM,OAAO,KAAK;IAErC,eAAe;IACf,MAAM,cAAc,IAAI,KAAK;IAC7B,YAAY,OAAO,CAAC,MAAM,OAAO,KAAK;IAEtC,gBAAgB;IAChB,MAAM,mBAAmB,IAAI,KAAK;IAClC,iBAAiB,QAAQ,CAAC,MAAM,QAAQ,KAAK;IAC7C,iBAAiB,OAAO,CAAC;IAEzB,gBAAgB;IAChB,MAAM,mBAAmB,IAAI,KAAK;IAClC,iBAAiB,QAAQ,CAAC,MAAM,QAAQ,KAAK;IAC7C,iBAAiB,OAAO,CAAC;IAEzB,eAAe;IACf,MAAM,kBAAkB,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;IAEzD,OAAO;QACL,aAAa;YACX,WAAW;YACX,SAAS;YACT,OAAO;QACT;QACA,cAAc;YACZ,WAAW;YACX,SAAS;YACT,OAAO;QACT;QACA,eAAe;YACb,WAAW;YACX,SAAS;YACT,OAAO;QACT;QACA,eAAe;YACb,WAAW;YACX,SAAS;YACT,OAAO;QACT;QACA,cAAc;YACZ,WAAW;YACX,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,SAAS,8BAA8B,YAA2B,EAAE,SAAe,EAAE,OAAa;IACvG,OAAO,aAAa,MAAM,CAAC,CAAA;QACzB,MAAM,kBAAkB,IAAI,KAAK,YAAY,IAAI;QACjD,OAAO,mBAAmB,aAAa,mBAAmB;IAC5D;AACF;AAEO,SAAS,8BACd,YAA2B,EAC3B,gBAAsB,EACtB,cAAoB;IAEpB,MAAM,eAAe,eAAe,OAAO,KAAK,iBAAiB,OAAO;IACxE,MAAM,oBAAoB,IAAI,KAAK,iBAAiB,OAAO,KAAK;IAChE,MAAM,kBAAkB,IAAI,KAAK,eAAe,OAAO,KAAK;IAE5D,OAAO,8BAA8B,cAAc,mBAAmB;AACxE;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,eAAe,YAA2B,EAAE,SAAS,CAAC;IACpE,MAAM,SAAgE,EAAE;IACxE,MAAM,MAAM,IAAI;IAEhB,2DAA2D;IAC3D,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,IAAK;QACpC,MAAM,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG;QAClE,OAAO,IAAI,CAAC;YACV,OAAO,UAAU,cAAc,CAAC,WAAW;gBAAE,OAAO;YAAQ;YAC5D,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,kBAAkB,IAAI,KAAK,YAAY,IAAI;QACjD,MAAM,YAAY,CAAC,IAAI,WAAW,KAAK,gBAAgB,WAAW,EAAE,IAAI,KAAK,IAAI,QAAQ,KAAK,gBAAgB,QAAQ;QAEtH,IAAI,aAAa,KAAK,YAAY,QAAQ;YACxC,MAAM,QAAQ,SAAS,IAAI;YAC3B,IAAI,YAAY,SAAS,EAAE;gBACzB,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,YAAY,MAAM;YAC5C,OAAO;gBACL,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,YAAY,MAAM;YAC9C;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS,gBAAgB,YAA2B,EAAE,UAAsB;IACjF,sCAAsC;IACtC,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS;IAEjE,oCAAoC;IACpC,MAAM,kBAA6C,CAAC;IACpD,oBAAoB,OAAO,CAAC,CAAA;QAC1B,IAAI,CAAC,eAAe,CAAC,YAAY,WAAW,CAAC,EAAE;YAC7C,eAAe,CAAC,YAAY,WAAW,CAAC,GAAG;QAC7C;QACA,eAAe,CAAC,YAAY,WAAW,CAAC,IAAI,YAAY,MAAM;IAChE;IAEA,6BAA6B;IAC7B,MAAM,SAAS,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,YAAY,OAAO;QACtE,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,OAAO;YACL,UAAU,WAAW,SAAS,IAAI,GAAG;YACrC;YACA,OAAO,UAAU,SAAS;QAC5B;IACF;IAEA,8BAA8B;IAC9B,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;AAClD;AAEO,SAAS,iBACd,iBAKC,EACD,kBAKC;IAED,MAAM,WAAgG,EAAE;IAExG,uBAAuB;IACvB,MAAM,cAAc,kBAAkB,WAAW,GAAG,IAChD,AAAC,kBAAkB,UAAU,GAAG,kBAAkB,WAAW,GAAI,MACjE;IAEJ,IAAI,cAAc,IAAI;QACpB,SAAS,IAAI,CAAC;YACZ,OAAO,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC,GAAG,4BAA4B,CAAC;YACxE,aAAa;YACb,MAAM;QACR;IACF,OAAO,IAAI,cAAc,GAAG;QAC1B,SAAS,IAAI,CAAC;YACZ,OAAO,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC,GAAG,4BAA4B,CAAC;YACxE,aAAa;YACb,MAAM;QACR;IACF,OAAO,IAAI,kBAAkB,WAAW,GAAG,GAAG;QAC5C,SAAS,IAAI,CAAC;YACZ,OAAO;YACP,aAAa;YACb,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,IAAI,kBAAkB,YAAY,CAAC,MAAM,GAAG,KAAK,mBAAmB,YAAY,CAAC,MAAM,GAAG,GAAG;QAC3F,8CAA8C;QAC9C,MAAM,qBAAqB,IAAI,IAC7B,kBAAkB,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ;gBAAC,KAAK,QAAQ;gBAAE,KAAK,MAAM;aAAC;QAGzE,MAAM,sBAAsB,IAAI,IAC9B,mBAAmB,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ;gBAAC,KAAK,QAAQ;gBAAE,KAAK,MAAM;aAAC;QAG1E,IAAI,kBAAkB;YAAE,UAAU;YAAI,QAAQ;YAAG,YAAY;QAAE;QAE/D,kBAAkB,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE;YAC1D,MAAM,iBAAiB,oBAAoB,GAAG,CAAC,aAAa;YAC5D,IAAI,iBAAiB,GAAG;gBACtB,MAAM,SAAS,SAAS;gBACxB,MAAM,aAAa,AAAC,SAAS,iBAAkB;gBAE/C,IAAI,aAAa,MAAM,SAAS,gBAAgB,MAAM,EAAE;oBACtD,kBAAkB;wBAAE;wBAAU;wBAAQ;oBAAW;gBACnD;YACF;QACF;QAEA,IAAI,gBAAgB,QAAQ,EAAE;YAC5B,SAAS,IAAI,CAAC;gBACZ,OAAO,CAAC,YAAY,EAAE,gBAAgB,QAAQ,CAAC,cAAc,EAAE,gBAAgB,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACvG,aAAa,CAAC,iBAAiB,EAAE,gBAAgB,QAAQ,CAAC,0EAA0E,CAAC;gBACrI,MAAM;YACR;QACF;QAEA,wBAAwB;QACxB,IAAI,kBAAkB,YAAY,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,cAAc,kBAAkB,YAAY,CAAC,EAAE;YACrD,MAAM,wBAAwB,AAAC,YAAY,MAAM,GAAG,kBAAkB,aAAa,GAAI;YAEvF,SAAS,IAAI,CAAC;gBACZ,OAAO,GAAG,YAAY,QAAQ,CAAC,gCAAgC,EAAE,sBAAsB,OAAO,CAAC,GAAG,EAAE,CAAC;gBACrG,aAAa,CAAC,WAAW,EAAE,YAAY,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,YAAY,QAAQ,CAAC,aAAa,CAAC;gBAClG,MAAM;YACR;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAe,0BACnB,kBAAkB,WAAW,EAC7B,mBAAmB,WAAW;IAGhC,IAAI,KAAK,GAAG,CAAC,gBAAgB,IAAI;QAC/B,SAAS,IAAI,CAAC;YACZ,OAAO,CAAC,YAAY,EAAE,eAAe,IAAI,cAAc,YAAY,IAAI,EAAE,KAAK,GAAG,CAAC,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC;YAC7G,aAAa,eAAe,IACxB,4FACA;YACJ,MAAM,eAAe,IAAI,aAAa;QACxC;IACF;IAEA,OAAO;AACT;AAEO,SAAS,mBACd,YAA2B,EAC3B,UAAsB,EACtB,SAAoB;IAEpB,oCAAoC;IACpC,MAAM,4BAA4B,8BAChC,cACA,UAAU,SAAS,EACnB,UAAU,OAAO;IAGnB,mCAAmC;IACnC,MAAM,6BAA6B,8BACjC,cACA,UAAU,SAAS,EACnB,UAAU,OAAO;IAGnB,kCAAkC;IAClC,MAAM,gBAAgB,0BACnB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,kBAAkB,0BACrB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,oBAAoB,gBAAgB;IAE1C,mCAAmC;IACnC,MAAM,iBAAiB,2BACpB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,mBAAmB,2BACtB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,qBAAqB,iBAAiB;IAE5C,+BAA+B;IAC/B,MAAM,eAAe,0BAA0B,eAAe;IAC9D,MAAM,gBAAgB,0BAA0B,iBAAiB;IACjE,MAAM,gBAAgB,0BAA0B,mBAAmB;IAEnE,mBAAmB;IACnB,MAAM,cAAc,eAAe;IAEnC,oBAAoB;IACpB,MAAM,eAAe,gBAAgB,2BAA2B;IAEhE,oBAAoB;IACpB,MAAM,WAAW,iBACf;QACE,aAAa;QACb,eAAe;QACf,YAAY;QACZ;IACF,GACA;QACE,aAAa;QACb,eAAe;QACf,YAAY;QACZ,cAAc,gBAAgB,4BAA4B;IAC5D;IAGF,OAAO;QACL,aAAa;QACb,eAAe;QACf,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/lib/eventBus.ts"], "sourcesContent": ["'use client';\n\n/**\n * Simple event bus for cross-component communication\n * This allows different parts of the application to communicate\n * without direct dependencies\n */\n\nexport type EventType = \n  | 'transaction_created'\n  | 'transaction_updated'\n  | 'transaction_deleted'\n  | 'transactions_changed';\n\nexport interface EventData {\n  userId?: string;\n  transactionId?: string;\n  [key: string]: any;\n}\n\n// Custom event name for our application\nconst EVENT_NAME = 'cashminder_event';\n\n/**\n * Emit an event to notify other components about a change\n */\nexport function emitEvent(type: EventType, data: EventData = {}) {\n  // Create a custom event with our data\n  const event = new CustomEvent(EVENT_NAME, {\n    detail: { type, data, timestamp: new Date().toISOString() }\n  });\n  \n  // Dispatch the event on the window object\n  window.dispatchEvent(event);\n  \n  console.log(`Event emitted: ${type}`, data);\n}\n\n/**\n * Listen for events of a specific type\n */\nexport function listenEvent(type: EventType, callback: (data: EventData) => void) {\n  const handler = (event: Event) => {\n    const customEvent = event as CustomEvent;\n    if (customEvent.detail && customEvent.detail.type === type) {\n      callback(customEvent.detail.data);\n    }\n  };\n  \n  // Add event listener\n  window.addEventListener(EVENT_NAME, handler);\n  \n  // Return a function to remove the listener\n  return () => {\n    window.removeEventListener(EVENT_NAME, handler);\n  };\n}\n\n/**\n * Helper function to refresh transactions for a user\n * This will emit an event that all transaction-dependent components should listen for\n */\nexport function refreshTransactions(userId: string) {\n  emitEvent('transactions_changed', { userId });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAoBA,wCAAwC;AACxC,MAAM,aAAa;AAKZ,SAAS,UAAU,IAAe,EAAE,OAAkB,CAAC,CAAC;IAC7D,sCAAsC;IACtC,MAAM,QAAQ,IAAI,YAAY,YAAY;QACxC,QAAQ;YAAE;YAAM;YAAM,WAAW,IAAI,OAAO,WAAW;QAAG;IAC5D;IAEA,0CAA0C;IAC1C,OAAO,aAAa,CAAC;IAErB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,EAAE;AACxC;AAKO,SAAS,YAAY,IAAe,EAAE,QAAmC;IAC9E,MAAM,UAAU,CAAC;QACf,MAAM,cAAc;QACpB,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,IAAI,KAAK,MAAM;YAC1D,SAAS,YAAY,MAAM,CAAC,IAAI;QAClC;IACF;IAEA,qBAAqB;IACrB,OAAO,gBAAgB,CAAC,YAAY;IAEpC,2CAA2C;IAC3C,OAAO;QACL,OAAO,mBAAmB,CAAC,YAAY;IACzC;AACF;AAMO,SAAS,oBAAoB,MAAc;IAChD,UAAU,wBAAwB;QAAE;IAAO;AAC7C", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/app/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useTheme } from '@/context/ThemeContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  FiBarChart2, FiPieChart, FiTrendingUp, FiCalendar,\n  FiDollarSign, FiArrowUp, FiArrowDown\n} from 'react-icons/fi';\nimport { Transaction, Category } from '@/lib/types';\nimport {\n  calculateAnalytics,\n  getTimeRanges,\n  AnalyticsSummary,\n  TimeRange\n} from '@/lib/analytics';\nimport { listenEvent } from '@/lib/eventBus';\n\n// Default categories (same as in transactions page)\nconst defaultCategories: Category[] = [\n  { id: '1', name: 'Housing', color: '#4F46E5', is_income: false, is_default: true },\n  { id: '2', name: 'Food', color: '#10B981', is_income: false, is_default: true },\n  { id: '3', name: 'Transportation', color: '#F59E0B', is_income: false, is_default: true },\n  { id: '4', name: 'Entertainment', color: '#EC4899', is_income: false, is_default: true },\n  { id: '5', name: 'Utilities', color: '#6366F1', is_income: false, is_default: true },\n  { id: '6', name: 'Salary', color: '#34D399', is_income: true, is_default: true },\n];\n\nexport default function AnalyticsPage() {\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [categories, setCategories] = useState<Category[]>(defaultCategories);\n  const [timeRanges] = useState(getTimeRanges());\n  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('last3months');\n  const [analytics, setAnalytics] = useState<AnalyticsSummary | null>(null);\n\n  // Function to load transactions and categories\n  const loadTransactionsAndCategories = () => {\n    // Check if user is logged in\n    const userJson = localStorage.getItem('cashminder_user');\n    if (!userJson) {\n      console.log('No user found in localStorage, redirecting to auth');\n      router.push('/auth');\n      return;\n    }\n\n    try {\n      // Parse user data\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n      console.log('Loading transactions for user:', userId);\n\n      // Load transactions from localStorage\n      const storedTransactions = localStorage.getItem(`cashminder_transactions_${userId}`);\n      if (storedTransactions) {\n        const parsedTransactions = JSON.parse(storedTransactions);\n        console.log('Loaded transactions from localStorage:', parsedTransactions.length);\n        setTransactions(parsedTransactions);\n      } else {\n        console.log('No transactions found in localStorage');\n        setTransactions([]);\n      }\n\n      // Load categories from localStorage or use defaults\n      const storedCategories = localStorage.getItem(`cashminder_categories_${userId}`);\n      if (storedCategories) {\n        const parsedCategories = JSON.parse(storedCategories);\n        console.log('Loaded categories from localStorage:', parsedCategories.length);\n        setCategories(parsedCategories);\n      } else {\n        console.log('Using default categories');\n      }\n\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Error loading user data:', error);\n      setIsLoading(false);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    loadTransactionsAndCategories();\n  }, [router]);\n\n  // Listen for transaction changes\n  useEffect(() => {\n    // Get user ID\n    const userJson = localStorage.getItem('cashminder_user');\n    if (!userJson) return;\n\n    const userData = JSON.parse(userJson);\n    const userId = userData.id || 'default';\n\n    // Set up event listeners for all transaction events\n    const removeCreatedListener = listenEvent('transaction_created', (data) => {\n      if (data.userId === userId) {\n        console.log('Analytics detected new transaction:', data);\n        loadTransactionsAndCategories();\n      }\n    });\n\n    const removeUpdatedListener = listenEvent('transaction_updated', (data) => {\n      if (data.userId === userId) {\n        console.log('Analytics detected updated transaction:', data);\n        loadTransactionsAndCategories();\n      }\n    });\n\n    const removeDeletedListener = listenEvent('transaction_deleted', (data) => {\n      if (data.userId === userId) {\n        console.log('Analytics detected deleted transaction:', data);\n        loadTransactionsAndCategories();\n      }\n    });\n\n    const removeChangedListener = listenEvent('transactions_changed', (data) => {\n      if (data.userId === userId) {\n        console.log('Analytics detected transactions changed');\n        loadTransactionsAndCategories();\n      }\n    });\n\n    // Clean up event listeners on unmount\n    return () => {\n      removeCreatedListener();\n      removeUpdatedListener();\n      removeDeletedListener();\n      removeChangedListener();\n    };\n  }, []);\n\n  // Calculate analytics when transactions, categories, or time range changes\n  useEffect(() => {\n    if (transactions.length === 0) {\n      // If no transactions, set empty analytics\n      setAnalytics({\n        totalIncome: 0,\n        totalExpenses: 0,\n        netSavings: 0,\n        incomeChange: 0,\n        expenseChange: 0,\n        savingsChange: 0,\n        monthlyData: getLastSixMonths(),\n        categoryData: [],\n        insights: []\n      });\n      return;\n    }\n\n    const timeRange = timeRanges[selectedTimeRange];\n    const analyticsData = calculateAnalytics(transactions, categories, timeRange);\n    setAnalytics(analyticsData);\n  }, [transactions, categories, selectedTimeRange, timeRanges]);\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  // Helper function to get last six months with zero values\n  function getLastSixMonths() {\n    const result = [];\n    const now = new Date();\n\n    for (let i = 5; i >= 0; i--) {\n      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);\n      result.push({\n        month: monthDate.toLocaleString('default', { month: 'short' }),\n        income: 0,\n        expenses: 0\n      });\n    }\n\n    return result;\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className={`text-xl ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n          <svg className=\"animate-spin -ml-1 mr-3 h-8 w-8 text-primary-500 inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading analytics...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div\n      className=\"container mx-auto px-4 py-8 max-w-7xl\"\n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={containerVariants}\n    >\n      <motion.div\n        className=\"pb-5 border-b border-light-border dark:border-dark-border mb-8\"\n        variants={itemVariants}\n      >\n        <h1 className={`text-3xl font-bold leading-tight ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n          Financial Analytics\n        </h1>\n        <p className={`mt-1 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n          Gain insights into your spending patterns and financial health\n        </p>\n      </motion.div>\n\n      {/* Time period selector */}\n      <motion.div\n        className=\"flex flex-wrap gap-2 mb-8\"\n        variants={itemVariants}\n      >\n        {Object.entries(timeRanges).map(([key, range], index) => (\n          <button\n            key={key}\n            onClick={() => setSelectedTimeRange(key)}\n            className={`px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2 transition-all duration-200 ${\n              key === selectedTimeRange\n                ? `${isDark\n                    ? 'bg-primary/30 border border-primary/50 text-dark-text-primary'\n                    : 'bg-primary/10 border border-primary/20 text-primary'}`\n                : `${isDark\n                    ? 'border border-dark-border text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface'\n                    : 'border border-light-border text-light-text-secondary hover:text-light-text-primary hover:bg-light-border/50'}`\n            }`}\n          >\n            <span>{range.label}</span>\n          </button>\n        ))}\n      </motion.div>\n\n      {/* Summary cards */}\n      <motion.div\n        className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\"\n        variants={itemVariants}\n      >\n        {[\n          {\n            title: 'Total Income',\n            value: analytics ? `$${analytics.totalIncome.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00',\n            change: analytics ? `${analytics.incomeChange > 0 ? '+' : ''}${analytics.incomeChange.toFixed(1)}%` : '0%',\n            icon: <FiArrowUp className=\"text-success-light dark:text-success-dark\" />,\n            trend: analytics && analytics.incomeChange > 0 ? 'up' : 'down',\n            isExpense: false\n          },\n          {\n            title: 'Total Expenses',\n            value: analytics ? `$${analytics.totalExpenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00',\n            change: analytics ? `${analytics.expenseChange > 0 ? '+' : ''}${analytics.expenseChange.toFixed(1)}%` : '0%',\n            icon: <FiArrowDown className=\"text-error-light dark:text-error-dark\" />,\n            trend: analytics && analytics.expenseChange > 0 ? 'up' : 'down',\n            isExpense: true\n          },\n          {\n            title: 'Net Savings',\n            value: analytics ? `$${analytics.netSavings.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$0.00',\n            change: analytics ? `${analytics.savingsChange > 0 ? '+' : ''}${analytics.savingsChange.toFixed(1)}%` : '0%',\n            icon: <FiTrendingUp className=\"text-primary\" />,\n            trend: analytics && analytics.savingsChange > 0 ? 'up' : 'down',\n            isExpense: false\n          }\n        ].map((item, index) => (\n          <motion.div\n            key={index}\n            className={`p-6 rounded-xl border ${\n              isDark\n                ? 'bg-dark-surface border-dark-border'\n                : 'bg-light-surface border-light-border shadow-sm'\n            }`}\n            whileHover={{ y: -5, boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\" }}\n          >\n            <div className=\"flex justify-between items-center mb-4\">\n              <h3 className={`text-lg font-medium ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                {item.title}\n              </h3>\n              <div className={`p-2 rounded-full ${\n                item.title === 'Total Income'\n                  ? 'bg-success-light/10 dark:bg-success-dark/10'\n                  : item.title === 'Total Expenses'\n                    ? 'bg-error-light/10 dark:bg-error-dark/10'\n                    : 'bg-primary/10 dark:bg-primary/20'\n              }`}>\n                {item.icon}\n              </div>\n            </div>\n            <div className={`text-2xl font-bold mb-2 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n              {item.value}\n            </div>\n            <div className={`flex items-center text-sm ${\n              item.trend === 'up'\n                ? (item.isExpense\n                  ? 'text-error-light dark:text-error-dark'\n                  : 'text-success-light dark:text-success-dark')\n                : item.trend === 'down'\n                  ? (item.isExpense\n                    ? 'text-success-light dark:text-success-dark'\n                    : 'text-error-light dark:text-error-dark')\n                  : 'text-light-text-muted dark:text-dark-text-muted'\n            }`}>\n              {item.trend === 'up'\n                ? <FiTrendingUp className=\"mr-1\" />\n                : item.trend === 'down'\n                  ? <FiArrowDown className=\"mr-1\" />\n                  : <FiTrendingUp className=\"mr-1\" />\n              }\n              <span>{item.change} from previous period</span>\n            </div>\n          </motion.div>\n        ))}\n      </motion.div>\n\n      {/* Main charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n        {/* Income vs Expenses Chart */}\n        <motion.div\n          className={`p-6 rounded-xl border ${\n            isDark\n              ? 'bg-dark-surface border-dark-border'\n              : 'bg-light-surface border-light-border shadow-sm'\n          }`}\n          variants={itemVariants}\n        >\n          <div className=\"flex justify-between items-center mb-6\">\n            <h3 className={`text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n              Income vs Expenses\n            </h3>\n            <FiBarChart2 className={`w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n          </div>\n\n          {/* Chart placeholder - in a real app, use a chart library */}\n          <div className=\"h-64 flex items-center justify-center\">\n            <div className=\"w-full h-full flex items-end justify-between\">\n              {analytics && analytics.monthlyData.map((data, index) => {\n                // Calculate the maximum value for scaling\n                const maxValue = analytics.monthlyData.reduce((max, item) => {\n                  const itemMax = Math.max(item.income, item.expenses);\n                  return itemMax > max ? itemMax : max;\n                }, 1); // Minimum 1 to avoid division by zero\n\n                // Scale factor - max height is 180px\n                const scaleFactor = maxValue > 0 ? 180 / maxValue : 0;\n\n                return (\n                  <div key={index} className=\"flex flex-col items-center w-1/6\">\n                    <div className=\"w-full flex justify-center space-x-1\">\n                      <motion.div\n                        className=\"w-5 bg-primary rounded-t\"\n                        initial={{ height: 0 }}\n                        animate={{ height: `${Math.max(data.income * scaleFactor, 0)}px` }}\n                        transition={{ duration: 0.8, delay: index * 0.1 }}\n                      />\n                      <motion.div\n                        className=\"w-5 bg-secondary rounded-t\"\n                        initial={{ height: 0 }}\n                        animate={{ height: `${Math.max(data.expenses * scaleFactor, 0)}px` }}\n                        transition={{ duration: 0.8, delay: index * 0.1 + 0.2 }}\n                      />\n                    </div>\n                    <span className={`text-xs mt-2 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                      {data.month}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex justify-center mt-4 space-x-6\">\n            <div className=\"flex items-center\">\n              <div className=\"w-3 h-3 bg-primary rounded-full mr-2\"></div>\n              <span className={`text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>Income</span>\n            </div>\n            <div className=\"flex items-center\">\n              <div className=\"w-3 h-3 bg-secondary rounded-full mr-2\"></div>\n              <span className={`text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>Expenses</span>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Expense Categories Chart */}\n        <motion.div\n          className={`p-6 rounded-xl border ${\n            isDark\n              ? 'bg-dark-surface border-dark-border'\n              : 'bg-light-surface border-light-border shadow-sm'\n          }`}\n          variants={itemVariants}\n        >\n          <div className=\"flex justify-between items-center mb-6\">\n            <h3 className={`text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n              Expense Breakdown\n            </h3>\n            <FiPieChart className={`w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n          </div>\n\n          {/* Chart placeholder - in a real app, use a chart library */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"relative h-64 flex items-center justify-center\">\n              <div className=\"w-32 h-32 rounded-full border-8 border-gray-200 dark:border-gray-700\"></div>\n\n              {analytics && analytics.categoryData.length > 0 ? (\n                <>\n                  {analytics.categoryData.map((category, index) => {\n                    // Calculate total expenses\n                    const totalExpenses = analytics.categoryData.reduce((sum, cat) => sum + cat.amount, 0);\n\n                    // Calculate degrees for this category (out of 360)\n                    const degrees = totalExpenses > 0 ? (category.amount / totalExpenses) * 360 : 0;\n\n                    // Calculate rotation offset based on previous categories\n                    const previousCategories = analytics.categoryData.slice(0, index);\n                    const previousDegrees = previousCategories.reduce((sum, cat) => {\n                      return sum + (totalExpenses > 0 ? (cat.amount / totalExpenses) * 360 : 0);\n                    }, 0);\n\n                    return (\n                      <motion.div\n                        key={index}\n                        className=\"absolute w-32 h-32 rounded-full\"\n                        style={{\n                          background: `conic-gradient(${category.color} 0deg, ${category.color} ${degrees}deg, transparent ${degrees}deg)`,\n                          transform: `rotate(${previousDegrees}deg)`\n                        }}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ duration: 0.5, delay: index * 0.1 }}\n                      />\n                    );\n                  })}\n\n                  <div className={`absolute text-lg font-bold ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                    ${analytics.totalExpenses.toFixed(0)}\n                  </div>\n                </>\n              ) : (\n                <div className={`absolute text-base ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                  No expense data\n                </div>\n              )}\n            </div>\n\n            <div className=\"space-y-3\">\n              {analytics && analytics.categoryData.length > 0 ? (\n                analytics.categoryData.map((category, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"flex items-center justify-between\"\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                  >\n                    <div className=\"flex items-center\">\n                      <div className=\"w-3 h-3 rounded-full mr-2\" style={{ backgroundColor: category.color }}></div>\n                      <span className={`text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                        {category.category}\n                      </span>\n                    </div>\n                    <span className={`text-sm font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                      ${category.amount.toFixed(2)}\n                    </span>\n                  </motion.div>\n                ))\n              ) : (\n                <div className={`text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                  Add expense transactions to see your spending breakdown\n                </div>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Insights section */}\n      <motion.div\n        className={`p-6 rounded-xl border ${\n          isDark\n            ? 'bg-dark-surface border-dark-border'\n            : 'bg-light-surface border-light-border shadow-sm'\n        } mb-8`}\n        variants={itemVariants}\n      >\n        <div className=\"flex justify-between items-center mb-6\">\n          <h3 className={`text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n            Financial Insights\n          </h3>\n          <FiTrendingUp className={`w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n        </div>\n\n        <div className=\"space-y-4\">\n          {analytics && analytics.insights.length > 0 ? (\n            analytics.insights.map((insight, index) => (\n              <motion.div\n                key={index}\n                className={`p-4 rounded-lg border ${\n                  isDark\n                    ? 'border-dark-border bg-dark-surface/50'\n                    : 'border-light-border bg-light-border/10'\n                }`}\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n              >\n                <div className=\"flex items-start\">\n                  <div className={`p-2 rounded-full ${\n                    isDark ? 'bg-dark-border' : 'bg-light-border/50'\n                  } mr-3 mt-1`}>\n                    {insight.type === 'positive' ? (\n                      <FiTrendingUp className=\"text-success-light dark:text-success-dark\" />\n                    ) : insight.type === 'negative' ? (\n                      <FiArrowUp className=\"text-error-light dark:text-error-dark\" />\n                    ) : (\n                      <FiDollarSign className=\"text-primary\" />\n                    )}\n                  </div>\n                  <div>\n                    <h4 className={`text-base font-medium mb-1 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                      {insight.title}\n                    </h4>\n                    <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                      {insight.description}\n                    </p>\n                  </div>\n                </div>\n              </motion.div>\n            ))\n          ) : (\n            <div className={`p-8 text-center ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n              <FiDollarSign className=\"w-12 h-12 mx-auto mb-4 opacity-30\" />\n              <p className=\"text-lg font-medium mb-2\">No insights available yet</p>\n              <p className=\"text-sm\">Add more transactions to get personalized financial insights</p>\n            </div>\n          )}\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAKA;AAMA;AAjBA;;;;;;;;;AAmBA,oDAAoD;AACpD,MAAM,oBAAgC;IACpC;QAAE,IAAI;QAAK,MAAM;QAAW,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACjF;QAAE,IAAI;QAAK,MAAM;QAAQ,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IAC9E;QAAE,IAAI;QAAK,MAAM;QAAkB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACxF;QAAE,IAAI;QAAK,MAAM;QAAiB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACvF;QAAE,IAAI;QAAK,MAAM;QAAa,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACnF;QAAE,IAAI;QAAK,MAAM;QAAU,OAAO;QAAW,WAAW;QAAM,YAAY;IAAK;CAChF;AAEc,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,+CAA+C;IAC/C,MAAM,gCAAgC;QACpC,6BAA6B;QAC7B,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAC9B,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,sCAAsC;YACtC,MAAM,qBAAqB,aAAa,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ;YACnF,IAAI,oBAAoB;gBACtB,MAAM,qBAAqB,KAAK,KAAK,CAAC;gBACtC,QAAQ,GAAG,CAAC,0CAA0C,mBAAmB,MAAM;gBAC/E,gBAAgB;YAClB,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,gBAAgB,EAAE;YACpB;YAEA,oDAAoD;YACpD,MAAM,mBAAmB,aAAa,OAAO,CAAC,CAAC,sBAAsB,EAAE,QAAQ;YAC/E,IAAI,kBAAkB;gBACpB,MAAM,mBAAmB,KAAK,KAAK,CAAC;gBACpC,QAAQ,GAAG,CAAC,wCAAwC,iBAAiB,MAAM;gBAC3E,cAAc;YAChB,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,aAAa;QACf;IACF;IAEA,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU;QAEf,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;QAE9B,oDAAoD;QACpD,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,uBAAuB,CAAC;YAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;gBAC1B,QAAQ,GAAG,CAAC,uCAAuC;gBACnD;YACF;QACF;QAEA,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,uBAAuB,CAAC;YAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;gBAC1B,QAAQ,GAAG,CAAC,2CAA2C;gBACvD;YACF;QACF;QAEA,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,uBAAuB,CAAC;YAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;gBAC1B,QAAQ,GAAG,CAAC,2CAA2C;gBACvD;YACF;QACF;QAEA,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,wBAAwB,CAAC;YACjE,IAAI,KAAK,MAAM,KAAK,QAAQ;gBAC1B,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;QAEA,sCAAsC;QACtC,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,0CAA0C;YAC1C,aAAa;gBACX,aAAa;gBACb,eAAe;gBACf,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,aAAa;gBACb,cAAc,EAAE;gBAChB,UAAU,EAAE;YACd;YACA;QACF;QAEA,MAAM,YAAY,UAAU,CAAC,kBAAkB;QAC/C,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,YAAY;QACnE,aAAa;IACf,GAAG;QAAC;QAAc;QAAY;QAAmB;KAAW;IAE5D,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,0DAA0D;IAC1D,SAAS;QACP,MAAM,SAAS,EAAE;QACjB,MAAM,MAAM,IAAI;QAEhB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG;YAClE,OAAO,IAAI,CAAC;gBACV,OAAO,UAAU,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;gBAC5D,QAAQ;gBACR,UAAU;YACZ;QACF;QAEA,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;;kCAC5F,8OAAC;wBAAI,WAAU;wBAAgE,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CACpI,8OAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,8OAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAQ;QACR,UAAU;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC;wBAAG,WAAW,CAAC,iCAAiC,EAAE,SAAS,2BAA2B,2BAA2B;kCAAE;;;;;;kCAGpH,8OAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,SAAS,6BAA6B,6BAA6B;kCAAE;;;;;;;;;;;;0BAMrG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBAC7C,8OAAC;wBAEC,SAAS,IAAM,qBAAqB;wBACpC,WAAW,CAAC,mGAAmG,EAC7G,QAAQ,oBACJ,GAAG,SACC,kEACA,uDAAuD,GAC3D,GAAG,SACC,0GACA,+GAA+G,EACvH;kCAEF,cAAA,8OAAC;sCAAM,MAAM,KAAK;;;;;;uBAZb;;;;;;;;;;0BAkBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET;oBACC;wBACE,OAAO;wBACP,OAAO,YAAY,CAAC,CAAC,EAAE,UAAU,WAAW,CAAC,cAAc,CAAC,WAAW;4BAAE,uBAAuB;4BAAG,uBAAuB;wBAAE,IAAI,GAAG;wBACnI,QAAQ,YAAY,GAAG,UAAU,YAAY,GAAG,IAAI,MAAM,KAAK,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wBACtG,oBAAM,8OAAC,8IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAC3B,OAAO,aAAa,UAAU,YAAY,GAAG,IAAI,OAAO;wBACxD,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,OAAO,YAAY,CAAC,CAAC,EAAE,UAAU,aAAa,CAAC,cAAc,CAAC,WAAW;4BAAE,uBAAuB;4BAAG,uBAAuB;wBAAE,IAAI,GAAG;wBACrI,QAAQ,YAAY,GAAG,UAAU,aAAa,GAAG,IAAI,MAAM,KAAK,UAAU,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wBACxG,oBAAM,8OAAC,8IAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC7B,OAAO,aAAa,UAAU,aAAa,GAAG,IAAI,OAAO;wBACzD,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,OAAO,YAAY,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,cAAc,CAAC,WAAW;4BAAE,uBAAuB;4BAAG,uBAAuB;wBAAE,IAAI,GAAG;wBAClI,QAAQ,YAAY,GAAG,UAAU,aAAa,GAAG,IAAI,MAAM,KAAK,UAAU,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;wBACxG,oBAAM,8OAAC,8IAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,OAAO,aAAa,UAAU,aAAa,GAAG,IAAI,OAAO;wBACzD,WAAW;oBACb;iBACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAW,CAAC,sBAAsB,EAChC,SACI,uCACA,kDACJ;wBACF,YAAY;4BAAE,GAAG,CAAC;4BAAG,WAAW;wBAAsC;;0CAEtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,CAAC,oBAAoB,EAAE,SAAS,6BAA6B,6BAA6B;kDACtG,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAW,CAAC,iBAAiB,EAChC,KAAK,KAAK,KAAK,iBACX,gDACA,KAAK,KAAK,KAAK,mBACb,4CACA,oCACN;kDACC,KAAK,IAAI;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAW,CAAC,wBAAwB,EAAE,SAAS,2BAA2B,2BAA2B;0CACvG,KAAK,KAAK;;;;;;0CAEb,8OAAC;gCAAI,WAAW,CAAC,0BAA0B,EACzC,KAAK,KAAK,KAAK,OACV,KAAK,SAAS,GACb,0CACA,8CACF,KAAK,KAAK,KAAK,SACZ,KAAK,SAAS,GACb,8CACA,0CACF,mDACN;;oCACC,KAAK,KAAK,KAAK,qBACZ,8OAAC,8IAAA,CAAA,eAAY;wCAAC,WAAU;;;;;+CACxB,KAAK,KAAK,KAAK,uBACb,8OAAC,8IAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DACvB,8OAAC,8IAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDAE9B,8OAAC;;4CAAM,KAAK,MAAM;4CAAC;;;;;;;;;;;;;;uBA1ChB;;;;;;;;;;0BAiDX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAC,sBAAsB,EAChC,SACI,uCACA,kDACJ;wBACF,UAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,2BAA2B;kDAAE;;;;;;kDAGvG,8OAAC,8IAAA,CAAA,cAAW;wCAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;;;;;;;0CAIxG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,aAAa,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM;wCAC7C,0CAA0C;wCAC1C,MAAM,WAAW,UAAU,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK;4CAClD,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,QAAQ;4CACnD,OAAO,UAAU,MAAM,UAAU;wCACnC,GAAG,IAAI,sCAAsC;wCAE7C,qCAAqC;wCACrC,MAAM,cAAc,WAAW,IAAI,MAAM,WAAW;wCAEpD,qBACE,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,QAAQ;4DAAE;4DACrB,SAAS;gEAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,aAAa,GAAG,EAAE,CAAC;4DAAC;4DACjE,YAAY;gEAAE,UAAU;gEAAK,OAAO,QAAQ;4DAAI;;;;;;sEAElD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,QAAQ;4DAAE;4DACrB,SAAS;gEAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,aAAa,GAAG,EAAE,CAAC;4DAAC;4DACnE,YAAY;gEAAE,UAAU;gEAAK,OAAO,QAAQ,MAAM;4DAAI;;;;;;;;;;;;8DAG1D,8OAAC;oDAAK,WAAW,CAAC,aAAa,EAAE,SAAS,4BAA4B,4BAA4B;8DAC/F,KAAK,KAAK;;;;;;;2CAhBL;;;;;oCAoBd;;;;;;;;;;;0CAIJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;0DAAE;;;;;;;;;;;;kDAEnG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMvG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAC,sBAAsB,EAChC,SACI,uCACA,kDACJ;wBACF,UAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,2BAA2B;kDAAE;;;;;;kDAGvG,8OAAC,8IAAA,CAAA,aAAU;wCAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;;;;;;;0CAIvG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAEd,aAAa,UAAU,YAAY,CAAC,MAAM,GAAG,kBAC5C;;oDACG,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU;wDACrC,2BAA2B;wDAC3B,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,MAAM,EAAE;wDAEpF,mDAAmD;wDACnD,MAAM,UAAU,gBAAgB,IAAI,AAAC,SAAS,MAAM,GAAG,gBAAiB,MAAM;wDAE9E,yDAAyD;wDACzD,MAAM,qBAAqB,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG;wDAC3D,MAAM,kBAAkB,mBAAmB,MAAM,CAAC,CAAC,KAAK;4DACtD,OAAO,MAAM,CAAC,gBAAgB,IAAI,AAAC,IAAI,MAAM,GAAG,gBAAiB,MAAM,CAAC;wDAC1E,GAAG;wDAEH,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,WAAU;4DACV,OAAO;gEACL,YAAY,CAAC,eAAe,EAAE,SAAS,KAAK,CAAC,OAAO,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE,QAAQ,iBAAiB,EAAE,QAAQ,IAAI,CAAC;gEAChH,WAAW,CAAC,OAAO,EAAE,gBAAgB,IAAI,CAAC;4DAC5C;4DACA,SAAS;gEAAE,SAAS;4DAAE;4DACtB,SAAS;gEAAE,SAAS;4DAAE;4DACtB,YAAY;gEAAE,UAAU;gEAAK,OAAO,QAAQ;4DAAI;2DAR3C;;;;;oDAWX;kEAEA,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,SAAS,2BAA2B,2BAA2B;;4DAAE;4DAC3G,UAAU,aAAa,CAAC,OAAO,CAAC;;;;;;;;6EAItC,8OAAC;gDAAI,WAAW,CAAC,mBAAmB,EAAE,SAAS,6BAA6B,6BAA6B;0DAAE;;;;;;;;;;;;kDAM/G,8OAAC;wCAAI,WAAU;kDACZ,aAAa,UAAU,YAAY,CAAC,MAAM,GAAG,IAC5C,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,sBACpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;;kEAEhD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAA4B,OAAO;oEAAE,iBAAiB,SAAS,KAAK;gEAAC;;;;;;0EACpF,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;0EAC5F,SAAS,QAAQ;;;;;;;;;;;;kEAGtB,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,2BAA2B;;4DAAE;4DACrG,SAAS,MAAM,CAAC,OAAO,CAAC;;;;;;;;+CAbvB;;;;sEAkBT,8OAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU1G,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,sBAAsB,EAChC,SACI,uCACA,iDACL,KAAK,CAAC;gBACP,UAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAW,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,2BAA2B;0CAAE;;;;;;0CAGvG,8OAAC,8IAAA,CAAA,eAAY;gCAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;;;;;;;kCAGzG,8OAAC;wBAAI,WAAU;kCACZ,aAAa,UAAU,QAAQ,CAAC,MAAM,GAAG,IACxC,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAW,CAAC,sBAAsB,EAChC,SACI,0CACA,0CACJ;gCACF,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,iBAAiB,EAChC,SAAS,mBAAmB,qBAC7B,UAAU,CAAC;sDACT,QAAQ,IAAI,KAAK,2BAChB,8OAAC,8IAAA,CAAA,eAAY;gDAAC,WAAU;;;;;uDACtB,QAAQ,IAAI,KAAK,2BACnB,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,8OAAC,8IAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAG5B,8OAAC;;8DACC,8OAAC;oDAAG,WAAW,CAAC,2BAA2B,EAAE,SAAS,2BAA2B,2BAA2B;8DACzG,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;8DACvF,QAAQ,WAAW;;;;;;;;;;;;;;;;;;+BA3BrB;;;;sDAkCT,8OAAC;4BAAI,WAAW,CAAC,gBAAgB,EAAE,SAAS,6BAA6B,6BAA6B;;8CACpG,8OAAC,8IAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}]}