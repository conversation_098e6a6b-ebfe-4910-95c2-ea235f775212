{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/context/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    // This effect runs on client-side only\n    try {\n      // Check local storage first\n      const savedTheme = localStorage.getItem('theme') as Theme;\n      let themeToApply: Theme;\n\n      if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {\n        themeToApply = savedTheme;\n      } else {\n        // If no saved theme, check system preference\n        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        themeToApply = prefersDark ? 'dark' : 'light';\n        // Save the initial theme preference\n        localStorage.setItem('theme', themeToApply);\n      }\n\n      // Update state\n      setTheme(themeToApply);\n\n      // Apply theme to DOM\n      if (themeToApply === 'dark') {\n        document.documentElement.classList.add('dark');\n        document.body.classList.add('dark');\n        console.log('Dark mode initialized');\n      } else {\n        document.documentElement.classList.remove('dark');\n        document.body.classList.remove('dark');\n        console.log('Light mode initialized');\n      }\n\n      console.log('Theme initialized to:', themeToApply);\n      console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n    } catch (error) {\n      console.error('Failed to initialize theme:', error);\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    try {\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n\n      // Force toggle the dark class on the html element\n      if (newTheme === 'dark') {\n        document.documentElement.classList.add('dark');\n        document.body.classList.add('dark');\n        console.log('Dark mode enabled');\n      } else {\n        document.documentElement.classList.remove('dark');\n        document.body.classList.remove('dark');\n        console.log('Light mode enabled');\n      }\n\n      // Save to localStorage\n      localStorage.setItem('theme', newTheme);\n\n      // Log the current state for debugging\n      console.log('Theme toggled to:', newTheme);\n      console.log('Dark class present:', document.documentElement.classList.contains('dark'));\n    } catch (error) {\n      console.error('Failed to toggle theme:', error);\n    }\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,uCAAuC;YACvC,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI;gBAEJ,IAAI,cAAc,CAAC,eAAe,UAAU,eAAe,OAAO,GAAG;oBACnE,eAAe;gBACjB,OAAO;oBACL,6CAA6C;oBAC7C,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBAC7E,eAAe,cAAc,SAAS;oBACtC,oCAAoC;oBACpC,aAAa,OAAO,CAAC,SAAS;gBAChC;gBAEA,eAAe;gBACf,SAAS;gBAET,qBAAqB;gBACrB,IAAI,iBAAiB,QAAQ;oBAC3B,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;oBACvC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;oBAC5B,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC1C,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC/B,QAAQ,GAAG,CAAC;gBACd;gBAEA,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;kCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YAET,kDAAkD;YAClD,IAAI,aAAa,QAAQ;gBACvB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gBACvC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBAC5B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC1C,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC/B,QAAQ,GAAG,CAAC;YACd;YAEA,uBAAuB;YACvB,aAAa,OAAO,CAAC,SAAS;YAE9B,sCAAsC;YACtC,QAAQ,GAAG,CAAC,qBAAqB;YACjC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;QACjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;GAzEgB;KAAA;AA2ET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/ui/FuturisticThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from '@/context/ThemeContext';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useEffect } from 'react';\n\nexport default function FuturisticThemeToggle() {\n  const { theme, toggleTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  // Only show the toggle after mounting to avoid hydration mismatch\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Handle the toggle click\n  const handleToggle = () => {\n    if (isAnimating) return;\n\n    setIsAnimating(true);\n    toggleTheme();\n\n    // Reset animation state after animation completes\n    setTimeout(() => {\n      setIsAnimating(false);\n    }, 600);\n  };\n\n  // Don't render anything until mounted to prevent hydration issues\n  if (!mounted) {\n    return <div className=\"w-12 h-12\" />;\n  }\n\n  const isDark = theme === 'dark';\n\n  return (\n    <motion.div\n      className=\"relative w-12 h-12 flex items-center justify-center\"\n      initial={false}\n      animate={{ scale: isAnimating ? 1.1 : 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <motion.button\n        onClick={handleToggle}\n        className={`w-12 h-12 rounded-full relative overflow-hidden flex items-center justify-center ${\n          isDark\n            ? 'bg-dark-card border border-dark-border shadow-inner'\n            : 'bg-light-card border border-light-border shadow-md'\n        } transition-all duration-300`}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}\n      >\n        <AnimatePresence mode=\"wait\">\n          {isDark ? (\n            <motion.div\n              key=\"dark-icon\"\n              initial={{ y: 20, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              exit={{ y: -20, opacity: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute inset-0 flex items-center justify-center\"\n            >\n              {/* Moon with stars */}\n              <div className=\"relative\">\n                <motion.div\n                  className=\"w-6 h-6 rounded-full bg-gray-100\"\n                  animate={{\n                    boxShadow: [\"0 0 0px 0px rgba(255,255,255,0.5)\", \"0 0 10px 2px rgba(255,255,255,0.3)\"],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    repeatType: \"reverse\"\n                  }}\n                />\n                <motion.div\n                  className=\"absolute w-1 h-1 rounded-full bg-blue-200 top-0 right-0\"\n                  animate={{ opacity: [0.5, 1, 0.5] }}\n                  transition={{ duration: 1.5, repeat: Infinity }}\n                />\n                <motion.div\n                  className=\"absolute w-1 h-1 rounded-full bg-blue-200 bottom-1 left-0\"\n                  animate={{ opacity: [0.7, 0.3, 0.7] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                />\n              </div>\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"light-icon\"\n              initial={{ y: -20, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              exit={{ y: 20, opacity: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute inset-0 flex items-center justify-center\"\n            >\n              {/* Sun with rays */}\n              <div className=\"relative\">\n                <motion.div\n                  className=\"w-6 h-6 rounded-full bg-yellow-400\"\n                  animate={{\n                    boxShadow: [\"0 0 0px 0px rgba(250,204,21,0.5)\", \"0 0 10px 2px rgba(250,204,21,0.3)\"],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    repeatType: \"reverse\"\n                  }}\n                />\n                {/* Sun rays */}\n                {[...Array(8)].map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"absolute w-1 h-2 bg-yellow-400\"\n                    style={{\n                      left: '50%',\n                      top: '50%',\n                      marginLeft: '-0.5px',\n                      marginTop: '-1px',\n                      transformOrigin: '50% 0',\n                      transform: `rotate(${i * 45}deg) translateY(-5px)`\n                    }}\n                    animate={{\n                      height: [2, 3, 2],\n                      opacity: [0.8, 1, 0.8]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      repeat: Infinity,\n                      delay: i * 0.1\n                    }}\n                  />\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.button>\n\n      {/* Animated background effect */}\n      <AnimatePresence>\n        {isAnimating && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0.7 }}\n            animate={{ scale: 4, opacity: 0 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.6 }}\n            className={`absolute inset-0 rounded-full ${\n              isDark ? 'bg-primary' : 'bg-primary'\n            }`}\n            style={{ zIndex: -1 }}\n          />\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,WAAW;QACb;0CAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,eAAe;QACnB,IAAI,aAAa;QAEjB,eAAe;QACf;QAEA,kDAAkD;QAClD,WAAW;YACT,eAAe;QACjB,GAAG;IACL;IAEA,kEAAkE;IAClE,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAU;;;;;;IACxB;IAEA,MAAM,SAAS,UAAU;IAEzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;QACT,SAAS;YAAE,OAAO,cAAc,MAAM;QAAE;QACxC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAW,CAAC,iFAAiF,EAC3F,SACI,wDACA,qDACL,4BAA4B,CAAC;gBAC9B,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,cAAY,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;0BAEzD,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAGV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,WAAW;4CAAC;4CAAqC;yCAAqC;oCACxF;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,YAAY;oCACd;;;;;;8CAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCAAC;oCAClC,YAAY;wCAAE,UAAU;wCAAK,QAAQ;oCAAS;;;;;;8CAEhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAAC;oCACpC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;;;;;;;;;;;;uBA5B5C;;;;6CAiCN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAGV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,WAAW;4CAAC;4CAAoC;yCAAoC;oCACtF;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,YAAY;oCACd;;;;;;gCAGD;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM;4CACN,KAAK;4CACL,YAAY;4CACZ,WAAW;4CACX,iBAAiB;4CACjB,WAAW,CAAC,OAAO,EAAE,IAAI,GAAG,qBAAqB,CAAC;wCACpD;wCACA,SAAS;4CACP,QAAQ;gDAAC;gDAAG;gDAAG;6CAAE;4CACjB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;wCACb;uCAlBK;;;;;;;;;;;uBAvBP;;;;;;;;;;;;;;;0BAmDZ,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAI;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAC,8BAA8B,EACxC,SAAS,eAAe,cACxB;oBACF,OAAO;wBAAE,QAAQ,CAAC;oBAAE;;;;;;;;;;;;;;;;;AAMhC;GAxJwB;;QACS,kIAAA,CAAA,WAAQ;;;KADjB", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/layout/FuturisticNavbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport FuturisticThemeToggle from '../ui/FuturisticThemeToggle';\nimport { useTheme } from '@/context/ThemeContext';\nimport {\n  FiHome, FiPieChart, FiDollarSign, FiTarget,\n  FiSettings, FiUser, FiMenu, FiX, FiLogOut\n} from 'react-icons/fi';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n}\n\nexport default function FuturisticNavbar() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [user, setUser] = useState<any>(null);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const pathname = usePathname();\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n\n  // Check if user is logged in\n  useEffect(() => {\n    // Function to check user authentication status\n    const checkAuth = () => {\n      const userData = localStorage.getItem('cashminder_user');\n      if (userData) {\n        setUser(JSON.parse(userData));\n      } else {\n        setUser(null);\n      }\n    };\n\n    // Check on initial load\n    checkAuth();\n\n    // Set up event listener for storage changes (for when user logs in/out in another tab)\n    window.addEventListener('storage', checkAuth);\n\n    // Set up custom event listener for auth changes within the same tab\n    window.addEventListener('auth_state_changed', checkAuth);\n\n    // Check auth status every 5 seconds to ensure UI is in sync\n    const interval = setInterval(checkAuth, 5000);\n\n    return () => {\n      window.removeEventListener('storage', checkAuth);\n      window.removeEventListener('auth_state_changed', checkAuth);\n      clearInterval(interval);\n    };\n  }, []);\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Navigation items based on authentication status\n  const authenticatedNavItems: NavItem[] = [\n    { name: 'Dashboard', href: '/dashboard', icon: <FiHome className=\"w-5 h-5\" /> },\n    { name: 'Transactions', href: '/transactions', icon: <FiDollarSign className=\"w-5 h-5\" /> },\n    { name: 'Analytics', href: '/analytics', icon: <FiPieChart className=\"w-5 h-5\" /> },\n    { name: 'Goals', href: '/goals', icon: <FiTarget className=\"w-5 h-5\" /> },\n    { name: 'Settings', href: '/settings', icon: <FiSettings className=\"w-5 h-5\" /> },\n  ];\n\n  const unauthenticatedNavItems: NavItem[] = [\n    { name: 'Home', href: '/', icon: <FiHome className=\"w-5 h-5\" /> },\n    { name: 'Features', href: '/#features', icon: <FiPieChart className=\"w-5 h-5\" /> },\n    { name: 'Pricing', href: '/pricing', icon: <FiDollarSign className=\"w-5 h-5\" /> },\n  ];\n\n  // Use the appropriate navigation items based on authentication status\n  const navItems = user ? authenticatedNavItems : unauthenticatedNavItems;\n\n  const handleLogout = () => {\n    // Remove user data from localStorage\n    localStorage.removeItem('cashminder_user');\n\n    // Dispatch custom event to notify other components about auth state change\n    window.dispatchEvent(new Event('auth_state_changed'));\n\n    // Set user state to null\n    setUser(null);\n\n    // Redirect to home page\n    window.location.href = '/';\n  };\n\n  return (\n    <>\n      {/* Desktop Navbar */}\n      <motion.nav\n        className={`fixed top-0 left-0 right-0 z-50 ${\n          isScrolled\n            ? 'bg-light-bg/95 dark:bg-dark-bg/95 backdrop-blur-md border-b border-light-border dark:border-dark-border shadow-sm'\n            : 'bg-transparent'\n        } transition-all duration-300`}\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              {/* Logo */}\n              <Link href=\"/\" className=\"flex-shrink-0 flex items-center\">\n                <motion.div\n                  className=\"w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md\"\n                  whileHover={{ scale: 1.05, rotate: 5 }}\n                  whileTap={{ scale: 0.95 }}\n                  animate={{\n                    boxShadow: [\n                      '0 0 0 rgba(0, 198, 255, 0.4)',\n                      '0 0 15px rgba(0, 198, 255, 0.6)',\n                      '0 0 0 rgba(0, 198, 255, 0.4)'\n                    ]\n                  }}\n                  transition={{ duration: 2, repeat: Infinity, repeatType: \"reverse\" }}\n                >\n                  <motion.span\n                    className=\"text-dark-bg font-bold text-xl\"\n                    animate={{\n                      scale: [1, 1.1, 1]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    C\n                  </motion.span>\n                </motion.div>\n                <span className=\"font-bold text-xl text-light-text-primary dark:text-dark-text-primary\">\n                  Cashminder\n                </span>\n              </Link>\n\n              {/* Desktop Navigation */}\n              <div className=\"hidden md:ml-6 md:flex md:space-x-4\">\n                {navItems.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 ${\n                        isActive\n                          ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30'\n                          : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'\n                      }`}\n                    >\n                      {item.icon}\n                      <span>{item.name}</span>\n                      {isActive && (\n                        <motion.div\n                          className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-primary\"\n                          layoutId=\"navbar-indicator\"\n                          initial={{ opacity: 0 }}\n                          animate={{ opacity: 1 }}\n                          transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n                          style={{\n                            boxShadow: '0 0 8px rgba(0, 198, 255, 0.6)'\n                          }}\n                        />\n                      )}\n                    </Link>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Right side items */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Theme Toggle */}\n              <FuturisticThemeToggle />\n\n              {/* User Menu or Login Button */}\n              {user ? (\n                <div className=\"relative flex items-center space-x-3\">\n                  {/* User greeting - only visible on desktop */}\n                  <span className=\"hidden md:block text-sm font-medium text-light-text-secondary dark:text-dark-text-secondary\">\n                    Hello, {user.name || 'User'}\n                  </span>\n\n                  {/* Logout button */}\n                  <motion.button\n                    className=\"flex items-center space-x-2 px-3 py-2 rounded-full bg-light-accent dark:bg-dark-accent border border-light-border dark:border-dark-border transition-all duration-200\"\n                    whileHover={{\n                      scale: 1.05,\n                      boxShadow: \"var(--card-shadow)\"\n                    }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={handleLogout}\n                  >\n                    <FiLogOut className=\"w-5 h-5 text-primary\" />\n                    <span className=\"text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">Logout</span>\n                  </motion.button>\n                </div>\n              ) : (\n                <Link href=\"/auth\">\n                  <motion.button\n                    className=\"flex items-center space-x-2 px-4 py-2 rounded-full bg-primary hover:bg-primary-400 text-dark-bg transition-all duration-200 shadow-md\"\n                    whileHover={{\n                      scale: 1.05,\n                      boxShadow: \"var(--glow-primary)\"\n                    }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <FiUser className=\"w-5 h-5\" />\n                    <span className=\"text-sm font-medium\">Login</span>\n                  </motion.button>\n                </Link>\n              )}\n\n              {/* Mobile menu button */}\n              <div className=\"flex md:hidden\">\n                <motion.button\n                  className={`inline-flex items-center justify-center p-2 rounded-md ${\n                    isDark\n                      ? 'text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface'\n                      : 'text-light-text-secondary hover:text-light-text-primary hover:bg-light-border'\n                  }`}\n                  whileTap={{ scale: 0.9 }}\n                  onClick={() => setIsOpen(!isOpen)}\n                >\n                  <span className=\"sr-only\">Open main menu</span>\n                  {isOpen ? (\n                    <FiX className=\"block h-6 w-6\" />\n                  ) : (\n                    <FiMenu className=\"block h-6 w-6\" />\n                  )}\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden bg-light-card dark:bg-dark-card border-t border-light-border dark:border-dark-border shadow-lg glass-card\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n                {navItems.map((item) => {\n                  const isActive = pathname === item.href;\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`px-3 py-2 rounded-md text-base font-medium flex items-center space-x-3 ${\n                        isActive\n                          ? 'text-primary bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30'\n                          : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent'\n                      }`}\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.icon}\n                      <span>{item.name}</span>\n                    </Link>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from being hidden under the navbar */}\n      <div className=\"h-16\"></div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IAEzB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,+CAA+C;YAC/C,MAAM;wDAAY;oBAChB,MAAM,WAAW,aAAa,OAAO,CAAC;oBACtC,IAAI,UAAU;wBACZ,QAAQ,KAAK,KAAK,CAAC;oBACrB,OAAO;wBACL,QAAQ;oBACV;gBACF;;YAEA,wBAAwB;YACxB;YAEA,uFAAuF;YACvF,OAAO,gBAAgB,CAAC,WAAW;YAEnC,oEAAoE;YACpE,OAAO,gBAAgB,CAAC,sBAAsB;YAE9C,4DAA4D;YAC5D,MAAM,WAAW,YAAY,WAAW;YAExC;8CAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,sBAAsB;oBACjD,cAAc;gBAChB;;QACF;qCAAG,EAAE;IAEL,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;2DAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;8CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;qCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,wBAAmC;QACvC;YAAE,MAAM;YAAa,MAAM;YAAc,oBAAM,6LAAC,iJAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAAa;QAC9E;YAAE,MAAM;YAAgB,MAAM;YAAiB,oBAAM,6LAAC,iJAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;QAAa;QAC1F;YAAE,MAAM;YAAa,MAAM;YAAc,oBAAM,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAAa;QAClF;YAAE,MAAM;YAAS,MAAM;YAAU,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAAa;QACxE;YAAE,MAAM;YAAY,MAAM;YAAa,oBAAM,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAAa;KACjF;IAED,MAAM,0BAAqC;QACzC;YAAE,MAAM;YAAQ,MAAM;YAAK,oBAAM,6LAAC,iJAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAAa;QAChE;YAAE,MAAM;YAAY,MAAM;YAAc,oBAAM,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAAa;QACjF;YAAE,MAAM;YAAW,MAAM;YAAY,oBAAM,6LAAC,iJAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;QAAa;KACjF;IAED,sEAAsE;IACtE,MAAM,WAAW,OAAO,wBAAwB;IAEhD,MAAM,eAAe;QACnB,qCAAqC;QACrC,aAAa,UAAU,CAAC;QAExB,2EAA2E;QAC3E,OAAO,aAAa,CAAC,IAAI,MAAM;QAE/B,yBAAyB;QACzB,QAAQ;QAER,wBAAwB;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,gCAAgC,EAC1C,aACI,sHACA,iBACL,4BAA4B,CAAC;gBAC9B,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;;kCAE1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAM,QAAQ;oDAAE;oDACrC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,SAAS;wDACP,WAAW;4DACT;4DACA;4DACA;yDACD;oDACH;oDACA,YAAY;wDAAE,UAAU;wDAAG,QAAQ;wDAAU,YAAY;oDAAU;8DAEnE,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wDACV,WAAU;wDACV,SAAS;4DACP,OAAO;gEAAC;gEAAG;gEAAK;6DAAE;wDACpB;wDACA,YAAY;4DAAE,UAAU;4DAAG,QAAQ;wDAAS;kEAC7C;;;;;;;;;;;8DAIH,6LAAC;oDAAK,WAAU;8DAAwE;;;;;;;;;;;;sDAM1F,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC;gDACb,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,0GAA0G,EACpH,WACI,kGACA,sJACJ;;wDAED,KAAK,IAAI;sEACV,6LAAC;sEAAM,KAAK,IAAI;;;;;;wDACf,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,UAAS;4DACT,SAAS;gEAAE,SAAS;4DAAE;4DACtB,SAAS;gEAAE,SAAS;4DAAE;4DACtB,YAAY;gEAAE,MAAM;gEAAU,WAAW;gEAAK,SAAS;4DAAG;4DAC1D,OAAO;gEACL,WAAW;4DACb;;;;;;;mDAnBC,KAAK,IAAI;;;;;4CAwBpB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,oJAAA,CAAA,UAAqB;;;;;wCAGrB,qBACC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAK,WAAU;;wDAA8F;wDACpG,KAAK,IAAI,IAAI;;;;;;;8DAIvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDACV,OAAO;wDACP,WAAW;oDACb;oDACA,UAAU;wDAAE,OAAO;oDAAK;oDACxB,SAAS;;sEAET,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAA0E;;;;;;;;;;;;;;;;;iEAI9F,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAU;gDACV,YAAY;oDACV,OAAO;oDACP,WAAW;gDACb;gDACA,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;;;;;;sDAM5C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAW,CAAC,uDAAuD,EACjE,SACI,gFACA,iFACJ;gDACF,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS,IAAM,UAAU,CAAC;;kEAE1B,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDACzB,uBACC,6LAAC,iJAAA,CAAA,MAAG;wDAAC,WAAU;;;;;6EAEf,6LAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS9B,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,uEAAuE,EACjF,WACI,kGACA,sJACJ;wCACF,SAAS,IAAM,UAAU;;4CAExB,KAAK,IAAI;0DACV,6LAAC;0DAAM,KAAK,IAAI;;;;;;;uCAVX,KAAK,IAAI;;;;;gCAapB;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GA1QwB;;QAIL,qIAAA,CAAA,cAAW;QACV,kIAAA,CAAA,WAAQ;;;KALJ", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/components/ThemeWrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider } from '@/context/ThemeContext';\n\nexport default function ThemeWrapper({ children }: { children: React.ReactNode }) {\n  return (\n    <ThemeProvider>\n      {children}\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,aAAa,EAAE,QAAQ,EAAiC;IAC9E,qBACE,6LAAC,kIAAA,CAAA,gBAAa;kBACX;;;;;;AAGP;KANwB", "debugId": null}}]}