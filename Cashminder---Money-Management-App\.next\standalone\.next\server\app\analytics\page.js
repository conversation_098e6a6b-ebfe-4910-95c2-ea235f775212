(()=>{var e={};e.id=745,e.ids=[745],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mk\\\\Cashminder---Money-Management-App\\\\src\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\analytics\\page.tsx","default")},23403:(e,t,a)=>{Promise.resolve().then(a.bind(a,63039))},25226:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),d=a(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);a.d(t,l);let o={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,21031)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\analytics\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\analytics\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63039:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(60687),s=a(43210),i=a(65101),n=a(29255),d=a(16189),l=a(36749);a(81435);let o=[{id:"1",name:"Housing",color:"#4F46E5",is_income:!1,is_default:!0},{id:"2",name:"Food",color:"#10B981",is_income:!1,is_default:!0},{id:"3",name:"Transportation",color:"#F59E0B",is_income:!1,is_default:!0},{id:"4",name:"Entertainment",color:"#EC4899",is_income:!1,is_default:!0},{id:"5",name:"Utilities",color:"#6366F1",is_income:!1,is_default:!0},{id:"6",name:"Salary",color:"#34D399",is_income:!0,is_default:!0}];function c(){let{theme:e}=(0,n.D)(),t="dark"===e;(0,d.useRouter)();let[a,c]=(0,s.useState)(!0),[x,m]=(0,s.useState)([]),[h,p]=(0,s.useState)(o),[g]=(0,s.useState)(function(){let e=new Date,t=new Date(e.getFullYear(),e.getMonth(),e.getDate()),a=new Date(t);a.setDate(t.getDate()-6);let r=new Date(t);r.setDate(t.getDate()-29);let s=new Date(t);s.setMonth(t.getMonth()-3),s.setDate(1);let i=new Date(t);i.setMonth(t.getMonth()-6),i.setDate(1);let n=new Date(t.getFullYear(),0,1);return{last7days:{startDate:a,endDate:t,label:"Last 7 days"},last30days:{startDate:r,endDate:t,label:"Last 30 days"},last3months:{startDate:s,endDate:t,label:"Last 3 months"},last6months:{startDate:i,endDate:t,label:"Last 6 months"},yearToDate:{startDate:n,endDate:t,label:"Year to date"}}}()),[u,y]=(0,s.useState)("last3months"),[b,v]=(0,s.useState)(null),f={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100,damping:12}}};return a?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:`text-xl ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-8 w-8 text-primary-500 inline-block",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading analytics..."]})}):(0,r.jsxs)(i.P.div,{className:"container mx-auto px-4 py-8 max-w-7xl",initial:"hidden",animate:"visible",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},children:[(0,r.jsxs)(i.P.div,{className:"pb-5 border-b border-light-border dark:border-dark-border mb-8",variants:f,children:[(0,r.jsx)("h1",{className:`text-3xl font-bold leading-tight ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:"Financial Analytics"}),(0,r.jsx)("p",{className:`mt-1 text-sm ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:"Gain insights into your spending patterns and financial health"})]}),(0,r.jsx)(i.P.div,{className:"flex flex-wrap gap-2 mb-8",variants:f,children:Object.entries(g).map(([e,a],s)=>(0,r.jsx)("button",{onClick:()=>y(e),className:`px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2 transition-all duration-200 ${e===u?`${t?"bg-primary/30 border border-primary/50 text-dark-text-primary":"bg-primary/10 border border-primary/20 text-primary"}`:`${t?"border border-dark-border text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface":"border border-light-border text-light-text-secondary hover:text-light-text-primary hover:bg-light-border/50"}`}`,children:(0,r.jsx)("span",{children:a.label})},e))}),(0,r.jsx)(i.P.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",variants:f,children:[{title:"Total Income",value:b?`$${b.totalIncome.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`:"$0.00",change:b?`${b.incomeChange>0?"+":""}${b.incomeChange.toFixed(1)}%`:"0%",icon:(0,r.jsx)(l.ei4,{className:"text-success-light dark:text-success-dark"}),trend:b&&b.incomeChange>0?"up":"down",isExpense:!1},{title:"Total Expenses",value:b?`$${b.totalExpenses.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`:"$0.00",change:b?`${b.expenseChange>0?"+":""}${b.expenseChange.toFixed(1)}%`:"0%",icon:(0,r.jsx)(l.LPr,{className:"text-error-light dark:text-error-dark"}),trend:b&&b.expenseChange>0?"up":"down",isExpense:!0},{title:"Net Savings",value:b?`$${b.netSavings.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`:"$0.00",change:b?`${b.savingsChange>0?"+":""}${b.savingsChange.toFixed(1)}%`:"0%",icon:(0,r.jsx)(l.ARf,{className:"text-primary"}),trend:b&&b.savingsChange>0?"up":"down",isExpense:!1}].map((e,a)=>(0,r.jsxs)(i.P.div,{className:`p-6 rounded-xl border ${t?"bg-dark-surface border-dark-border":"bg-light-surface border-light-border shadow-sm"}`,whileHover:{y:-5,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)"},children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:`text-lg font-medium ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:e.title}),(0,r.jsx)("div",{className:`p-2 rounded-full ${"Total Income"===e.title?"bg-success-light/10 dark:bg-success-dark/10":"Total Expenses"===e.title?"bg-error-light/10 dark:bg-error-dark/10":"bg-primary/10 dark:bg-primary/20"}`,children:e.icon})]}),(0,r.jsx)("div",{className:`text-2xl font-bold mb-2 ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:e.value}),(0,r.jsxs)("div",{className:`flex items-center text-sm ${"up"===e.trend?e.isExpense?"text-error-light dark:text-error-dark":"text-success-light dark:text-success-dark":"down"===e.trend?e.isExpense?"text-success-light dark:text-success-dark":"text-error-light dark:text-error-dark":"text-light-text-muted dark:text-dark-text-muted"}`,children:["up"===e.trend?(0,r.jsx)(l.ARf,{className:"mr-1"}):"down"===e.trend?(0,r.jsx)(l.LPr,{className:"mr-1"}):(0,r.jsx)(l.ARf,{className:"mr-1"}),(0,r.jsxs)("span",{children:[e.change," from previous period"]})]})]},a))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)(i.P.div,{className:`p-6 rounded-xl border ${t?"bg-dark-surface border-dark-border":"bg-light-surface border-light-border shadow-sm"}`,variants:f,children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:`text-lg font-medium ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:"Income vs Expenses"}),(0,r.jsx)(l.vQY,{className:`w-5 h-5 ${t?"text-dark-text-secondary":"text-light-text-secondary"}`})]}),(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-full h-full flex items-end justify-between",children:b&&b.monthlyData.map((e,a)=>{let s=b.monthlyData.reduce((e,t)=>{let a=Math.max(t.income,t.expenses);return a>e?a:e},1),n=s>0?180/s:0;return(0,r.jsxs)("div",{className:"flex flex-col items-center w-1/6",children:[(0,r.jsxs)("div",{className:"w-full flex justify-center space-x-1",children:[(0,r.jsx)(i.P.div,{className:"w-5 bg-primary rounded-t",initial:{height:0},animate:{height:`${Math.max(e.income*n,0)}px`},transition:{duration:.8,delay:.1*a}}),(0,r.jsx)(i.P.div,{className:"w-5 bg-secondary rounded-t",initial:{height:0},animate:{height:`${Math.max(e.expenses*n,0)}px`},transition:{duration:.8,delay:.1*a+.2}})]}),(0,r.jsx)("span",{className:`text-xs mt-2 ${t?"text-dark-text-tertiary":"text-light-text-tertiary"}`,children:e.month})]},a)})})}),(0,r.jsxs)("div",{className:"flex justify-center mt-4 space-x-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-primary rounded-full mr-2"}),(0,r.jsx)("span",{className:`text-sm ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:"Income"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-secondary rounded-full mr-2"}),(0,r.jsx)("span",{className:`text-sm ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:"Expenses"})]})]})]}),(0,r.jsxs)(i.P.div,{className:`p-6 rounded-xl border ${t?"bg-dark-surface border-dark-border":"bg-light-surface border-light-border shadow-sm"}`,variants:f,children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:`text-lg font-medium ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:"Expense Breakdown"}),(0,r.jsx)(l.eXT,{className:`w-5 h-5 ${t?"text-dark-text-secondary":"text-light-text-secondary"}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"relative h-64 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-32 h-32 rounded-full border-8 border-gray-200 dark:border-gray-700"}),b&&b.categoryData.length>0?(0,r.jsxs)(r.Fragment,{children:[b.categoryData.map((e,t)=>{let a=b.categoryData.reduce((e,t)=>e+t.amount,0),s=a>0?e.amount/a*360:0,n=b.categoryData.slice(0,t).reduce((e,t)=>e+(a>0?t.amount/a*360:0),0);return(0,r.jsx)(i.P.div,{className:"absolute w-32 h-32 rounded-full",style:{background:`conic-gradient(${e.color} 0deg, ${e.color} ${s}deg, transparent ${s}deg)`,transform:`rotate(${n}deg)`},initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.1*t}},t)}),(0,r.jsxs)("div",{className:`absolute text-lg font-bold ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:["$",b.totalExpenses.toFixed(0)]})]}):(0,r.jsx)("div",{className:`absolute text-base ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:"No expense data"})]}),(0,r.jsx)("div",{className:"space-y-3",children:b&&b.categoryData.length>0?b.categoryData.map((e,a)=>(0,r.jsxs)(i.P.div,{className:"flex items-center justify-between",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*a},children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:`text-sm ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:e.category})]}),(0,r.jsxs)("span",{className:`text-sm font-medium ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:["$",e.amount.toFixed(2)]})]},a)):(0,r.jsx)("div",{className:`text-sm ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:"Add expense transactions to see your spending breakdown"})})]})]})]}),(0,r.jsxs)(i.P.div,{className:`p-6 rounded-xl border ${t?"bg-dark-surface border-dark-border":"bg-light-surface border-light-border shadow-sm"} mb-8`,variants:f,children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:`text-lg font-medium ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:"Financial Insights"}),(0,r.jsx)(l.ARf,{className:`w-5 h-5 ${t?"text-dark-text-secondary":"text-light-text-secondary"}`})]}),(0,r.jsx)("div",{className:"space-y-4",children:b&&b.insights.length>0?b.insights.map((e,a)=>(0,r.jsx)(i.P.div,{className:`p-4 rounded-lg border ${t?"border-dark-border bg-dark-surface/50":"border-light-border bg-light-border/10"}`,initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*a},children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:`p-2 rounded-full ${t?"bg-dark-border":"bg-light-border/50"} mr-3 mt-1`,children:"positive"===e.type?(0,r.jsx)(l.ARf,{className:"text-success-light dark:text-success-dark"}):"negative"===e.type?(0,r.jsx)(l.ei4,{className:"text-error-light dark:text-error-dark"}):(0,r.jsx)(l.z8N,{className:"text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:`text-base font-medium mb-1 ${t?"text-dark-text-primary":"text-light-text-primary"}`,children:e.title}),(0,r.jsx)("p",{className:`text-sm ${t?"text-dark-text-tertiary":"text-light-text-tertiary"}`,children:e.description})]})]})},a)):(0,r.jsxs)("div",{className:`p-8 text-center ${t?"text-dark-text-secondary":"text-light-text-secondary"}`,children:[(0,r.jsx)(l.z8N,{className:"w-12 h-12 mx-auto mb-4 opacity-30"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"No insights available yet"}),(0,r.jsx)("p",{className:"text-sm",children:"Add more transactions to get personalized financial insights"})]})})]})]})}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81435:(e,t,a)=>{"use strict";a.d(t,{VC:()=>n,c:()=>s,k6:()=>i});let r="cashminder_event";function s(e,t={}){let a=new CustomEvent(r,{detail:{type:e,data:t,timestamp:new Date().toISOString()}});window.dispatchEvent(a),console.log(`Event emitted: ${e}`,t)}function i(e,t){let a=a=>{a.detail&&a.detail.type===e&&t(a.detail.data)};return window.addEventListener(r,a),()=>{window.removeEventListener(r,a)}}function n(e){s("transactions_changed",{userId:e})}},86451:(e,t,a)=>{Promise.resolve().then(a.bind(a,21031))}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,791,658,119],()=>a(25226));module.exports=r})();