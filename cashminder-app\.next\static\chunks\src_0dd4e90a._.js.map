{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Combine Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\n// Format date\nexport function formatDate(dateString: string, formatStr: string = 'MMM d, yyyy'): string {\n  try {\n    const date = parseISO(dateString);\n    return format(date, formatStr);\n  } catch (error) {\n    return dateString;\n  }\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Generate random color\nexport function generateRandomColor(): string {\n  const colors = [\n    '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA5A5', '#A5FFD6',\n    '#FFC145', '#FF6B8B', '#845EC2', '#D65DB1', '#FF9671',\n  ];\n  return colors[Math.floor(Math.random() * colors.length)];\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number = 25): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,UAAkB,EAAE,YAAoB,aAAa;IAC9E,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS;IACd,MAAM,SAAS;QACb;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,EAAE;IAC/D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/goals/GoalCard.tsx"], "sourcesContent": ["'use client';\n\nimport { SavingsGoal } from '@/lib/types';\nimport { calculatePercentage, formatCurrency, formatDate } from '@/lib/utils';\nimport { FiEdit2, FiTrash2 } from 'react-icons/fi';\n\ninterface GoalCardProps {\n  goal: SavingsGoal;\n  onEdit: (goal: SavingsGoal) => void;\n  onDelete: (goalId: string) => void;\n  onUpdateAmount: (goalId: string, newAmount: number) => void;\n}\n\nexport default function GoalCard({\n  goal,\n  onEdit,\n  onDelete,\n  onUpdateAmount,\n}: GoalCardProps) {\n  const percentage = calculatePercentage(goal.current_amount, goal.target_amount);\n  const remaining = goal.target_amount - goal.current_amount;\n\n  const handleAddFunds = () => {\n    const amount = prompt('Enter amount to add:', '0');\n    if (amount !== null) {\n      const numAmount = Number(amount);\n      if (!isNaN(numAmount) && numAmount > 0) {\n        const newAmount = Math.min(goal.current_amount + numAmount, goal.target_amount);\n        onUpdateAmount(goal.id, newAmount);\n      }\n    }\n  };\n\n  return (\n    <div className=\"overflow-hidden rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm hover:shadow-md transition-shadow\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg leading-6 font-medium text-light-text-primary dark:text-dark-text-primary\">{goal.name}</h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => onEdit(goal)}\n              className=\"p-1 rounded-full text-light-text-muted dark:text-dark-text-muted hover:text-primary dark:hover:text-primary transition-colors\"\n            >\n              <FiEdit2 className=\"h-5 w-5\" />\n            </button>\n            <button\n              onClick={() => onDelete(goal.id)}\n              className=\"p-1 rounded-full text-light-text-muted dark:text-dark-text-muted hover:text-error-light dark:hover:text-error-dark transition-colors\"\n            >\n              <FiTrash2 className=\"h-5 w-5\" />\n            </button>\n          </div>\n        </div>\n\n        <div className=\"mt-4 flex items-baseline\">\n          <p className=\"text-2xl font-semibold text-light-text-primary dark:text-dark-text-primary\">\n            {formatCurrency(goal.current_amount)}\n          </p>\n          <p className=\"ml-2 text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n            of {formatCurrency(goal.target_amount)}\n          </p>\n        </div>\n\n        <div className=\"mt-4\">\n          <div className=\"w-full h-2 bg-light-accent dark:bg-dark-bg rounded-full\">\n            <div\n              className=\"h-2 bg-blue-gradient rounded-full\"\n              style={{ width: `${percentage}%` }}\n            ></div>\n          </div>\n          <div className=\"mt-1 text-xs text-light-text-secondary dark:text-dark-text-secondary\">\n            {percentage}% complete • {formatCurrency(remaining)} to go\n          </div>\n        </div>\n\n        {goal.target_date && (\n          <div className=\"mt-4 text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n            Target date: {formatDate(goal.target_date)}\n          </div>\n        )}\n\n        <div className=\"mt-4\">\n          <button\n            onClick={handleAddFunds}\n            className=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-dark-bg bg-primary hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\n          >\n            Add Funds\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAae,SAAS,SAAS,EAC/B,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,cAAc,EACA;IACd,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,cAAc,EAAE,KAAK,aAAa;IAC9E,MAAM,YAAY,KAAK,aAAa,GAAG,KAAK,cAAc;IAE1D,MAAM,iBAAiB;QACrB,MAAM,SAAS,OAAO,wBAAwB;QAC9C,IAAI,WAAW,MAAM;YACnB,MAAM,YAAY,OAAO;YACzB,IAAI,CAAC,MAAM,cAAc,YAAY,GAAG;gBACtC,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,cAAc,GAAG,WAAW,KAAK,aAAa;gBAC9E,eAAe,KAAK,EAAE,EAAE;YAC1B;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqF,KAAK,IAAI;;;;;;sCAC5G,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO;oCACtB,WAAU;8CAEV,cAAA,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCACC,SAAS,IAAM,SAAS,KAAK,EAAE;oCAC/B,WAAU;8CAEV,cAAA,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAK1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;sCAErC,6LAAC;4BAAE,WAAU;;gCAAuE;gCAC9E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,WAAW,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;;gCACZ;gCAAW;gCAAc,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;gCAAW;;;;;;;;;;;;;gBAIvD,KAAK,WAAW,kBACf,6LAAC;oBAAI,WAAU;;wBAAuE;wBACtE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;;;;;;;8BAI7C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;KA/EwB", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/goals/GoalForm.tsx"], "sourcesContent": ["'use client';\n\nimport { SavingsGoal } from '@/lib/types';\nimport { useState } from 'react';\n\ninterface GoalFormProps {\n  onSubmit: (goal: Omit<SavingsGoal, 'id' | 'user_id' | 'created_at'>) => void;\n  onCancel: () => void;\n  initialData?: Partial<SavingsGoal>;\n}\n\nexport default function GoalForm({\n  onSubmit,\n  onCancel,\n  initialData,\n}: GoalFormProps) {\n  const [name, setName] = useState(initialData?.name || '');\n  const [targetAmount, setTargetAmount] = useState(initialData?.target_amount?.toString() || '');\n  const [currentAmount, setCurrentAmount] = useState(initialData?.current_amount?.toString() || '0');\n  const [targetDate, setTargetDate] = useState(\n    initialData?.target_date\n      ? new Date(initialData.target_date).toISOString().split('T')[0]\n      : ''\n  );\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!name.trim()) {\n      newErrors.name = 'Goal name is required';\n    }\n\n    if (!targetAmount || isNaN(Number(targetAmount)) || Number(targetAmount) <= 0) {\n      newErrors.targetAmount = 'Please enter a valid target amount greater than 0';\n    }\n\n    if (!currentAmount || isNaN(Number(currentAmount)) || Number(currentAmount) < 0) {\n      newErrors.currentAmount = 'Please enter a valid current amount (0 or greater)';\n    }\n\n    if (Number(currentAmount) > Number(targetAmount)) {\n      newErrors.currentAmount = 'Current amount cannot be greater than target amount';\n    }\n\n    if (targetDate && new Date(targetDate) <= new Date()) {\n      newErrors.targetDate = 'Target date must be in the future';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    onSubmit({\n      name,\n      target_amount: Number(targetAmount),\n      current_amount: Number(currentAmount),\n      target_date: targetDate ? new Date(targetDate).toISOString() : undefined,\n    });\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label htmlFor=\"name\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Goal Name\n        </label>\n        <div className=\"mt-1\">\n          <input\n            type=\"text\"\n            name=\"name\"\n            id=\"name\"\n            value={name}\n            onChange={(e) => setName(e.target.value)}\n            className=\"block w-full bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\n            placeholder=\"e.g., Emergency Fund, New Car, Vacation\"\n          />\n        </div>\n        {errors.name && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.name}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"targetAmount\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Target Amount\n        </label>\n        <div className=\"relative mt-1 rounded-md shadow-sm\">\n          <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n            <span className=\"text-light-text-secondary dark:text-dark-text-secondary sm:text-sm\">$</span>\n          </div>\n          <input\n            type=\"number\"\n            name=\"targetAmount\"\n            id=\"targetAmount\"\n            step=\"0.01\"\n            min=\"0\"\n            value={targetAmount}\n            onChange={(e) => setTargetAmount(e.target.value)}\n            className=\"block w-full pl-7 pr-12 bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md focus:ring-primary focus:border-primary sm:text-sm\"\n            placeholder=\"0.00\"\n          />\n        </div>\n        {errors.targetAmount && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.targetAmount}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"currentAmount\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Current Amount\n        </label>\n        <div className=\"relative mt-1 rounded-md shadow-sm\">\n          <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n            <span className=\"text-light-text-secondary dark:text-dark-text-secondary sm:text-sm\">$</span>\n          </div>\n          <input\n            type=\"number\"\n            name=\"currentAmount\"\n            id=\"currentAmount\"\n            step=\"0.01\"\n            min=\"0\"\n            value={currentAmount}\n            onChange={(e) => setCurrentAmount(e.target.value)}\n            className=\"block w-full pl-7 pr-12 bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md focus:ring-primary focus:border-primary sm:text-sm\"\n            placeholder=\"0.00\"\n          />\n        </div>\n        {errors.currentAmount && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.currentAmount}</p>}\n      </div>\n\n      <div>\n        <label htmlFor=\"targetDate\" className=\"block text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">\n          Target Date (Optional)\n        </label>\n        <div className=\"mt-1\">\n          <input\n            type=\"date\"\n            name=\"targetDate\"\n            id=\"targetDate\"\n            value={targetDate}\n            onChange={(e) => setTargetDate(e.target.value)}\n            className=\"block w-full bg-light-accent dark:bg-dark-accent border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm\"\n          />\n        </div>\n        {errors.targetDate && <p className=\"mt-2 text-sm text-error-light dark:text-error-dark\">{errors.targetDate}</p>}\n      </div>\n\n      <div className=\"flex justify-end space-x-3\">\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"px-4 py-2 text-sm font-medium text-light-text-primary dark:text-dark-text-primary bg-light-accent dark:bg-dark-accent border border-light-border dark:border-dark-border rounded-lg shadow-sm hover:bg-light-border dark:hover:bg-dark-border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\n        >\n          Cancel\n        </button>\n        <button\n          type=\"submit\"\n          className=\"px-4 py-2 text-sm font-medium text-dark-bg bg-primary border border-transparent rounded-lg shadow-sm hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\n        >\n          Save\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAWe,SAAS,SAAS,EAC/B,QAAQ,EACR,QAAQ,EACR,WAAW,EACG;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,QAAQ;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,eAAe,cAAc;IAC3F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,gBAAgB,cAAc;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzC,aAAa,cACT,IAAI,KAAK,YAAY,WAAW,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAC7D;IAEN,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,gBAAgB,MAAM,OAAO,kBAAkB,OAAO,iBAAiB,GAAG;YAC7E,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,CAAC,iBAAiB,MAAM,OAAO,mBAAmB,OAAO,iBAAiB,GAAG;YAC/E,UAAU,aAAa,GAAG;QAC5B;QAEA,IAAI,OAAO,iBAAiB,OAAO,eAAe;YAChD,UAAU,aAAa,GAAG;QAC5B;QAEA,IAAI,cAAc,IAAI,KAAK,eAAe,IAAI,QAAQ;YACpD,UAAU,UAAU,GAAG;QACzB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,SAAS;YACP;YACA,eAAe,OAAO;YACtB,gBAAgB,OAAO;YACvB,aAAa,aAAa,IAAI,KAAK,YAAY,WAAW,KAAK;QACjE;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAO,WAAU;kCAAgF;;;;;;kCAGhH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4BACvC,WAAU;4BACV,aAAY;;;;;;;;;;;oBAGf,OAAO,IAAI,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,IAAI;;;;;;;;;;;;0BAGhG,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAe,WAAU;kCAAgF;;;;;;kCAGxH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqE;;;;;;;;;;;0CAEvF,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;gCACV,aAAY;;;;;;;;;;;;oBAGf,OAAO,YAAY,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,YAAY;;;;;;;;;;;;0BAGhH,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAgB,WAAU;kCAAgF;;;;;;kCAGzH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqE;;;;;;;;;;;0CAEvF,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;gCACV,aAAY;;;;;;;;;;;;oBAGf,OAAO,aAAa,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,aAAa;;;;;;;;;;;;0BAGlH,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAa,WAAU;kCAAgF;;;;;;kCAGtH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;;;;;;;;;;oBAGb,OAAO,UAAU,kBAAI,6LAAC;wBAAE,WAAU;kCAAsD,OAAO,UAAU;;;;;;;;;;;;0BAG5G,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GA7JwB;KAAA", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/app/goals/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { SavingsGoal } from '@/lib/types';\nimport { useRouter } from 'next/navigation';\nimport GoalCard from '@/components/goals/GoalCard';\nimport GoalForm from '@/components/goals/GoalForm';\nimport { FiPlus } from 'react-icons/fi';\n\n// Mock savings goals\nconst mockGoals: SavingsGoal[] = [\n  {\n    id: '1',\n    user_id: '1',\n    name: 'Emergency Fund',\n    target_amount: 10000,\n    current_amount: 5000,\n    target_date: new Date(new Date().getFullYear() + 1, 0, 1).toISOString(),\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    user_id: '1',\n    name: 'New Car',\n    target_amount: 20000,\n    current_amount: 2500,\n    target_date: new Date(new Date().getFullYear() + 2, 0, 1).toISOString(),\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    user_id: '1',\n    name: 'Vacation',\n    target_amount: 3000,\n    current_amount: 1200,\n    target_date: new Date(new Date().getFullYear(), new Date().getMonth() + 6, 1).toISOString(),\n    created_at: new Date().toISOString(),\n  },\n];\n\nexport default function GoalsPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [goals, setGoals] = useState<SavingsGoal[]>(mockGoals);\n  const [showForm, setShowForm] = useState(false);\n  const [editingGoal, setEditingGoal] = useState<SavingsGoal | null>(null);\n\n  useEffect(() => {\n    // Check if user is logged in using localStorage\n    const userJson = localStorage.getItem('cashminder_user');\n\n    if (!userJson) {\n      router.push('/auth/login');\n      return;\n    }\n\n    try {\n      // Parse user data\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n      const isNewUser = userData.isNewUser === true;\n\n      // If this is a new user, show empty data\n      if (isNewUser) {\n        // For new users, initialize with empty data\n        setGoals([]);\n      } else {\n        // For returning users, try to load their data from localStorage\n        const storedGoals = localStorage.getItem(`cashminder_goals_${userId}`);\n\n        if (storedGoals) {\n          setGoals(JSON.parse(storedGoals));\n        } else {\n          // Start with empty goals for new users\n          setGoals([]);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading user data:', error);\n      // Start with empty goals in case of error\n      setGoals([]);\n    }\n\n    setIsLoading(false);\n  }, [router]);\n\n  const handleAddGoal = () => {\n    setEditingGoal(null);\n    setShowForm(true);\n  };\n\n  const handleEditGoal = (goal: SavingsGoal) => {\n    setEditingGoal(goal);\n    setShowForm(true);\n  };\n\n  const handleDeleteGoal = (goalId: string) => {\n    if (window.confirm('Are you sure you want to delete this savings goal?')) {\n      try {\n        // Get the current user\n        const userJson = localStorage.getItem('cashminder_user');\n        if (!userJson) return;\n\n        const userData = JSON.parse(userJson);\n        const userId = userData.id || 'default';\n\n        // Update goals in state\n        const updatedGoals = goals.filter((g) => g.id !== goalId);\n        setGoals(updatedGoals);\n\n        // Save to localStorage\n        localStorage.setItem(`cashminder_goals_${userId}`, JSON.stringify(updatedGoals));\n      } catch (error) {\n        console.error('Error deleting goal:', error);\n        alert('Failed to delete goal. Please try again.');\n      }\n    }\n  };\n\n  const handleUpdateAmount = (goalId: string, newAmount: number) => {\n    try {\n      // Get the current user\n      const userJson = localStorage.getItem('cashminder_user');\n      if (!userJson) return;\n\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n\n      // Update goals in state\n      const updatedGoals = goals.map((g) =>\n        g.id === goalId\n          ? { ...g, current_amount: newAmount }\n          : g\n      );\n      setGoals(updatedGoals);\n\n      // Save to localStorage\n      localStorage.setItem(`cashminder_goals_${userId}`, JSON.stringify(updatedGoals));\n    } catch (error) {\n      console.error('Error updating goal amount:', error);\n      alert('Failed to update goal amount. Please try again.');\n    }\n  };\n\n  const handleSubmitGoal = (goalData: Omit<SavingsGoal, 'id' | 'user_id' | 'created_at'>) => {\n    try {\n      // Get the current user\n      const userJson = localStorage.getItem('cashminder_user');\n      if (!userJson) return;\n\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n\n      let updatedGoals: SavingsGoal[];\n\n      if (editingGoal) {\n        // Update existing goal\n        updatedGoals = goals.map((g) =>\n          g.id === editingGoal.id\n            ? { ...g, ...goalData }\n            : g\n        );\n      } else {\n        // Add new goal\n        const newGoal: SavingsGoal = {\n          id: `goal_${Date.now()}`,\n          user_id: userId,\n          created_at: new Date().toISOString(),\n          ...goalData,\n        };\n        updatedGoals = [...goals, newGoal];\n      }\n\n      // Update state\n      setGoals(updatedGoals);\n\n      // Save to localStorage\n      localStorage.setItem(`cashminder_goals_${userId}`, JSON.stringify(updatedGoals));\n    } catch (error) {\n      console.error('Error saving goal:', error);\n      alert('Failed to save goal. Please try again.');\n    }\n\n    setShowForm(false);\n    setEditingGoal(null);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-xl text-light-text-secondary dark:text-dark-text-secondary\">\n          <svg className=\"animate-spin -ml-1 mr-3 h-8 w-8 text-primary inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading goals...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8 max-w-7xl\">\n      <div className=\"pb-5 border-b border-light-border dark:border-dark-border sm:flex sm:items-center sm:justify-between mb-8\">\n        <h1 className=\"text-3xl font-bold leading-tight text-light-text-primary dark:text-dark-text-primary\">Savings Goals</h1>\n        <div className=\"mt-3 sm:mt-0 sm:ml-4\">\n          <button\n            type=\"button\"\n            onClick={handleAddGoal}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-dark-bg bg-primary hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\n          >\n            <FiPlus className=\"-ml-1 mr-2 h-5 w-5\" />\n            Add Goal\n          </button>\n        </div>\n      </div>\n\n      {showForm ? (\n        <div className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm\">\n          <h2 className=\"text-lg font-medium text-light-text-primary dark:text-dark-text-primary mb-4\">\n            {editingGoal ? 'Edit Savings Goal' : 'Add Savings Goal'}\n          </h2>\n          <GoalForm\n            onSubmit={handleSubmitGoal}\n            onCancel={() => {\n              setShowForm(false);\n              setEditingGoal(null);\n            }}\n            initialData={editingGoal || undefined}\n          />\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3\">\n          {goals.length > 0 ? (\n            goals.map((goal) => (\n              <GoalCard\n                key={goal.id}\n                goal={goal}\n                onEdit={handleEditGoal}\n                onDelete={handleDeleteGoal}\n                onUpdateAmount={handleUpdateAmount}\n              />\n            ))\n          ) : (\n            <div className=\"col-span-full py-12 text-center\">\n              <svg className=\"w-16 h-16 mx-auto mb-4 text-light-text-muted dark:text-dark-text-muted\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <p className=\"font-medium text-light-text-primary dark:text-dark-text-primary mb-2\">No savings goals found</p>\n              <p className=\"text-light-text-secondary dark:text-dark-text-secondary\">Click \"Add Goal\" to create your first savings goal.</p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,qBAAqB;AACrB,MAAM,YAA2B;IAC/B;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,eAAe;QACf,gBAAgB;QAChB,aAAa,IAAI,KAAK,IAAI,OAAO,WAAW,KAAK,GAAG,GAAG,GAAG,WAAW;QACrE,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,eAAe;QACf,gBAAgB;QAChB,aAAa,IAAI,KAAK,IAAI,OAAO,WAAW,KAAK,GAAG,GAAG,GAAG,WAAW;QACrE,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,eAAe;QACf,gBAAgB;QAChB,aAAa,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,KAAK,GAAG,GAAG,WAAW;QACzF,YAAY,IAAI,OAAO,WAAW;IACpC;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,gDAAgD;YAChD,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,CAAC,UAAU;gBACb,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,kBAAkB;gBAClB,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;gBAC9B,MAAM,YAAY,SAAS,SAAS,KAAK;gBAEzC,yCAAyC;gBACzC,IAAI,WAAW;oBACb,4CAA4C;oBAC5C,SAAS,EAAE;gBACb,OAAO;oBACL,gEAAgE;oBAChE,MAAM,cAAc,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ;oBAErE,IAAI,aAAa;wBACf,SAAS,KAAK,KAAK,CAAC;oBACtB,OAAO;wBACL,uCAAuC;wBACvC,SAAS,EAAE;oBACb;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,0CAA0C;gBAC1C,SAAS,EAAE;YACb;YAEA,aAAa;QACf;8BAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,OAAO,CAAC,uDAAuD;YACxE,IAAI;gBACF,uBAAuB;gBACvB,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,IAAI,CAAC,UAAU;gBAEf,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;gBAE9B,wBAAwB;gBACxB,MAAM,eAAe,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAClD,SAAS;gBAET,uBAAuB;gBACvB,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;YACpE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAE9B,wBAAwB;YACxB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,IAC9B,EAAE,EAAE,KAAK,SACL;oBAAE,GAAG,CAAC;oBAAE,gBAAgB;gBAAU,IAClC;YAEN,SAAS;YAET,uBAAuB;YACvB,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAE9B,IAAI;YAEJ,IAAI,aAAa;gBACf,uBAAuB;gBACvB,eAAe,MAAM,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,YAAY,EAAE,GACnB;wBAAE,GAAG,CAAC;wBAAE,GAAG,QAAQ;oBAAC,IACpB;YAER,OAAO;gBACL,eAAe;gBACf,MAAM,UAAuB;oBAC3B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBACxB,SAAS;oBACT,YAAY,IAAI,OAAO,WAAW;oBAClC,GAAG,QAAQ;gBACb;gBACA,eAAe;uBAAI;oBAAO;iBAAQ;YACpC;YAEA,eAAe;YACf,SAAS;YAET,uBAAuB;YACvB,aAAa,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;QAEA,YAAY;QACZ,eAAe;IACjB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA4D,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CAChI,6LAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,6LAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuF;;;;;;kCACrG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAuB;;;;;;;;;;;;;;;;;;YAM9C,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,cAAc,sBAAsB;;;;;;kCAEvC,6LAAC,0IAAA,CAAA,UAAQ;wBACP,UAAU;wBACV,UAAU;4BACR,YAAY;4BACZ,eAAe;wBACjB;wBACA,aAAa,eAAe;;;;;;;;;;;qCAIhC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC,0IAAA,CAAA,UAAQ;wBAEP,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,gBAAgB;uBAJX,KAAK,EAAE;;;;8CAQhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAyE,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC7H,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAK,GAAE;;;;;;;;;;;sCAEzE,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;sCACpF,6LAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;;;;;;;;;;;;AAOrF;GAxNwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}