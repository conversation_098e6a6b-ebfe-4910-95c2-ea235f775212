{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NYAyTW8Qh+xTEp0LMAwr+/JIogYY2vq+j/IVhP2/7qA=", "__NEXT_PREVIEW_MODE_ID": "bef33dc53b28644c6d7a2a350ea3b086", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b75c1a9adce8b3c31d01ffc94875bcf2108bb8b35a13f7e7c6e5e41e3b65cf9b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "94a91d115470b0275da86ceed2200ae5178214dc77acedb90460c936d5f1d82f"}}}, "instrumentation": null, "functions": {}}