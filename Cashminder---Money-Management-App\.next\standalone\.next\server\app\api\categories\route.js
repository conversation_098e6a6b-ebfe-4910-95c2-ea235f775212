(()=>{var e={};e.id=722,e.ids=[722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19387:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>p,POST:()=>g,PUT:()=>y});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),c=t(75745),u=t(73944),d=t(56037),l=t.n(d);async function p(e){try{let r=e.nextUrl.searchParams.get("userId");if(!r)return i.NextResponse.json({success:!1,error:"User ID is required"},{status:400});await (0,c.A)();let t=e.nextUrl.searchParams.get("type"),s={userId:r};t&&("income"===t||"expense"===t)&&(s.type=t);let o=await u.A.find(s).sort({name:1});return console.log(`Retrieved ${o.length} categories for user ${r}`),i.NextResponse.json({success:!0,data:o})}catch(e){return console.error("Error fetching categories:",e),i.NextResponse.json({success:!1,error:"Failed to fetch categories",details:e instanceof Error?e.message:String(e)},{status:500})}}async function g(e){try{let r=await e.json();if(!r.userId||!r.name||!r.color||!r.type)return i.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(await (0,c.A)(),!l().Types.ObjectId.isValid(r.userId))return i.NextResponse.json({success:!1,error:"Invalid user ID format"},{status:400});if(await u.A.findOne({userId:r.userId,name:r.name,type:r.type}))return i.NextResponse.json({success:!1,error:"Category with this name already exists"},{status:400});let t=new u.A(r);return await t.save(),console.log(`New category created: ${t._id} (${r.name}) for user ${r.userId}`),i.NextResponse.json({success:!0,data:t},{status:201})}catch(e){return console.error("Error creating category:",e),i.NextResponse.json({success:!1,error:"Failed to create category",details:e instanceof Error?e.message:String(e)},{status:500})}}async function y(e){try{let r=e.nextUrl.searchParams.get("id");if(!r)return i.NextResponse.json({success:!1,error:"Category ID is required"},{status:400});let t=await e.json();await (0,c.A)();let s=await u.A.findByIdAndUpdate(r,{$set:t},{new:!0,runValidators:!0});if(!s)return i.NextResponse.json({success:!1,error:"Category not found"},{status:404});return console.log(`Category updated: ${r} (${s.name})`),i.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error updating category:",e),i.NextResponse.json({success:!1,error:"Failed to update category",details:e instanceof Error?e.message:String(e)},{status:500})}}async function m(e){try{let r=e.nextUrl.searchParams.get("id");if(!r)return i.NextResponse.json({success:!1,error:"Category ID is required"},{status:400});await (0,c.A)();let t=await u.A.findById(r);if(t?.isDefault)return i.NextResponse.json({success:!1,error:"Cannot delete default categories"},{status:400});let s=await u.A.findByIdAndDelete(r);if(!s)return i.NextResponse.json({success:!1,error:"Category not found"},{status:404});return console.log(`Category deleted: ${r} (${s.name})`),i.NextResponse.json({success:!0,data:{id:r}})}catch(e){return console.error("Error deleting category:",e),i.NextResponse.json({success:!1,error:"Failed to delete category",details:e instanceof Error?e.message:String(e)},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\api\\categories\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:j}=f;function w(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73944:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(56037),o=t.n(s);let n=new s.Schema({userId:{type:s.Schema.Types.ObjectId,ref:"User",required:!0},name:{type:String,required:!0},color:{type:String,required:!0},icon:{type:String},type:{type:String,enum:["income","expense"],required:!0},description:{type:String},isDefault:{type:Boolean,default:!1},budget:{type:Number},parentCategory:{type:s.Schema.Types.ObjectId,ref:"Category"}},{timestamps:!0}),a=o().models.Category||o().model("Category",n)},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(56037),o=t.n(s);let n=process.env.MONGODB_URI||"";if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let i=async function(){if(a.conn)return a.conn;a.promise||(a.promise=o().connect(n).then(e=>(console.log("MongoDB connected successfully"),e)).catch(e=>{throw console.error("MongoDB connection error:",e),e}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(19387));module.exports=s})();