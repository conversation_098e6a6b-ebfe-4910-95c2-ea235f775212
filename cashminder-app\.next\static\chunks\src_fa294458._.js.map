{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Combine Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\n// Format date\nexport function formatDate(dateString: string, formatStr: string = 'MMM d, yyyy'): string {\n  try {\n    const date = parseISO(dateString);\n    return format(date, formatStr);\n  } catch (error) {\n    return dateString;\n  }\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Generate random color\nexport function generateRandomColor(): string {\n  const colors = [\n    '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA5A5', '#A5FFD6',\n    '#FFC145', '#FF6B8B', '#845EC2', '#D65DB1', '#FF9671',\n  ];\n  return colors[Math.floor(Math.random() * colors.length)];\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number = 25): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,UAAkB,EAAE,YAAoB,aAAa;IAC9E,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS;IACd,MAAM,SAAS;QACb;QAAW;QAAW;QAAW;QAAW;QAC5C;QAAW;QAAW;QAAW;QAAW;KAC7C;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,EAAE;IAC/D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/dashboard/DashboardSummary.tsx"], "sourcesContent": ["'use client';\n\nimport { formatCurrency, calculatePercentage } from '@/lib/utils';\nimport { FiArrowDown, FiArrowUp, FiDollarSign, FiTrendingUp, FiTrendingDown } from 'react-icons/fi';\nimport { motion } from 'framer-motion';\n\ninterface DashboardSummaryProps {\n  totalIncome: number;\n  totalExpenses: number;\n  netSavings: number;\n  previousIncome?: number;\n  previousExpenses?: number;\n}\n\nexport default function DashboardSummary({\n  totalIncome,\n  totalExpenses,\n  netSavings,\n  previousIncome = 0,\n  previousExpenses = 0,\n}: DashboardSummaryProps) {\n  // Calculate percentage changes\n  const incomeChange = previousIncome > 0 ? ((totalIncome - previousIncome) / previousIncome) * 100 : 0;\n  const expenseChange = previousExpenses > 0 ? ((totalExpenses - previousExpenses) / previousExpenses) * 100 : 0;\n\n  // Calculate savings rate\n  const savingsRate = totalIncome > 0 ? (netSavings / totalIncome) * 100 : 0;\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className=\"grid grid-cols-1 gap-5 mt-2 sm:grid-cols-3\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <motion.div\n        className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm finance-card\"\n        variants={itemVariants}\n        whileHover={{\n          y: -5,\n          boxShadow: \"0 10px 25px -3px rgba(50, 255, 126, 0.1), 0 4px 6px -2px rgba(50, 255, 126, 0.05)\"\n        }}\n      >\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-rajdhani font-semibold text-light-text-secondary dark:text-dark-text-secondary letter-spacing-wide\">\n            TOTAL INCOME\n          </h3>\n          <div className=\"p-2 rounded-full bg-success-light/10 dark:bg-success-dark/10\">\n            <FiArrowUp className=\"text-success-light dark:text-success-dark\" />\n          </div>\n        </div>\n        <div className=\"text-2xl font-orbitron font-bold mb-2 text-light-text-primary dark:text-dark-text-primary letter-spacing-wide\">\n          {formatCurrency(totalIncome)}\n        </div>\n        {previousIncome > 0 && (\n          <div className=\"flex items-center text-sm\">\n            {incomeChange > 0 ? (\n              <>\n                <FiTrendingUp className=\"mr-1 text-success-light dark:text-success-dark\" />\n                <span className=\"text-success-light dark:text-success-dark\">\n                  +{incomeChange.toFixed(1)}% from previous period\n                </span>\n              </>\n            ) : incomeChange < 0 ? (\n              <>\n                <FiTrendingDown className=\"mr-1 text-error-light dark:text-error-dark\" />\n                <span className=\"text-error-light dark:text-error-dark\">\n                  {incomeChange.toFixed(1)}% from previous period\n                </span>\n              </>\n            ) : (\n              <span className=\"text-light-text-muted dark:text-dark-text-muted\">\n                No change from previous period\n              </span>\n            )}\n          </div>\n        )}\n      </motion.div>\n\n      <motion.div\n        className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm finance-card\"\n        variants={itemVariants}\n        whileHover={{\n          y: -5,\n          boxShadow: \"0 10px 25px -3px rgba(255, 77, 77, 0.1), 0 4px 6px -2px rgba(255, 77, 77, 0.05)\"\n        }}\n      >\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-rajdhani font-semibold text-light-text-secondary dark:text-dark-text-secondary letter-spacing-wide\">\n            TOTAL EXPENSES\n          </h3>\n          <div className=\"p-2 rounded-full bg-error-light/10 dark:bg-error-dark/10\">\n            <FiArrowDown className=\"text-error-light dark:text-error-dark\" />\n          </div>\n        </div>\n        <div className=\"text-2xl font-orbitron font-bold mb-2 text-light-text-primary dark:text-dark-text-primary letter-spacing-wide\">\n          {formatCurrency(totalExpenses)}\n        </div>\n        {previousExpenses > 0 && (\n          <div className=\"flex items-center text-sm\">\n            {expenseChange > 0 ? (\n              <>\n                <FiTrendingUp className=\"mr-1 text-error-light dark:text-error-dark\" />\n                <span className=\"text-error-light dark:text-error-dark\">\n                  +{expenseChange.toFixed(1)}% from previous period\n                </span>\n              </>\n            ) : expenseChange < 0 ? (\n              <>\n                <FiTrendingDown className=\"mr-1 text-success-light dark:text-success-dark\" />\n                <span className=\"text-success-light dark:text-success-dark\">\n                  {expenseChange.toFixed(1)}% from previous period\n                </span>\n              </>\n            ) : (\n              <span className=\"text-light-text-muted dark:text-dark-text-muted\">\n                No change from previous period\n              </span>\n            )}\n          </div>\n        )}\n      </motion.div>\n\n      <motion.div\n        className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm finance-card\"\n        variants={itemVariants}\n        whileHover={{\n          y: -5,\n          boxShadow: \"var(--glow-primary)\"\n        }}\n      >\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-rajdhani font-semibold text-light-text-secondary dark:text-dark-text-secondary letter-spacing-wide\">\n            NET SAVINGS\n          </h3>\n          <div className=\"p-2 rounded-full bg-primary/10\">\n            <FiTrendingUp className=\"text-primary\" />\n          </div>\n        </div>\n        <div className=\"text-2xl font-orbitron font-bold mb-2 text-light-text-primary dark:text-dark-text-primary letter-spacing-wide\">\n          {formatCurrency(netSavings)}\n        </div>\n        <div className=\"mt-1\">\n          <div className=\"flex items-center justify-between text-sm mb-1\">\n            <span className=\"text-light-text-secondary dark:text-dark-text-secondary\">Savings Rate</span>\n            <span className=\"font-medium text-primary\">{savingsRate.toFixed(1)}%</span>\n          </div>\n          <div className=\"w-full h-2 bg-light-accent dark:bg-dark-bg rounded-full\">\n            <div\n              className=\"h-2 bg-blue-gradient rounded-full\"\n              style={{ width: `${Math.max(0, Math.min(100, savingsRate))}%` }}\n            />\n          </div>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAce,SAAS,iBAAiB,EACvC,WAAW,EACX,aAAa,EACb,UAAU,EACV,iBAAiB,CAAC,EAClB,mBAAmB,CAAC,EACE;IACtB,+BAA+B;IAC/B,MAAM,eAAe,iBAAiB,IAAI,AAAC,CAAC,cAAc,cAAc,IAAI,iBAAkB,MAAM;IACpG,MAAM,gBAAgB,mBAAmB,IAAI,AAAC,CAAC,gBAAgB,gBAAgB,IAAI,mBAAoB,MAAM;IAE7G,yBAAyB;IACzB,MAAM,cAAc,cAAc,IAAI,AAAC,aAAa,cAAe,MAAM;IAEzE,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,YAAY;oBACV,GAAG,CAAC;oBACJ,WAAW;gBACb;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkH;;;;;;0CAGhI,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;oBAEjB,iBAAiB,mBAChB,6LAAC;wBAAI,WAAU;kCACZ,eAAe,kBACd;;8CACE,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;;wCAA4C;wCACxD,aAAa,OAAO,CAAC;wCAAG;;;;;;;;2CAG5B,eAAe,kBACjB;;8CACE,6LAAC,iJAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;;wCACb,aAAa,OAAO,CAAC;wCAAG;;;;;;;;yDAI7B,6LAAC;4BAAK,WAAU;sCAAkD;;;;;;;;;;;;;;;;;0BAQ1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,YAAY;oBACV,GAAG,CAAC;oBACJ,WAAW;gBACb;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkH;;;;;;0CAGhI,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;oBAEjB,mBAAmB,mBAClB,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,kBACf;;8CACE,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;;wCAAwC;wCACpD,cAAc,OAAO,CAAC;wCAAG;;;;;;;;2CAG7B,gBAAgB,kBAClB;;8CACE,6LAAC,iJAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;;wCACb,cAAc,OAAO,CAAC;wCAAG;;;;;;;;yDAI9B,6LAAC;4BAAK,WAAU;sCAAkD;;;;;;;;;;;;;;;;;0BAQ1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,YAAY;oBACV,GAAG,CAAC;oBACJ,WAAW;gBACb;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkH;;;;;;0CAGhI,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG5B,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;kDAC1E,6LAAC;wCAAK,WAAU;;4CAA4B,YAAY,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,cAAc,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5E;KAvKwB", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/dashboard/ExpenseChart.tsx"], "sourcesContent": ["'use client';\n\nimport { Category } from '@/lib/types';\nimport { formatCurrency } from '@/lib/utils';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, Title, LinearScale, BarElement } from 'chart.js';\nimport { Pie, Doughnut } from 'react-chartjs-2';\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\n\nChartJS.register(ArcElement, Tooltip, Legend, Title, LinearScale, BarElement);\n\ninterface ExpenseChartProps {\n  categories: {\n    category: Category;\n    amount: number;\n  }[];\n}\n\nexport default function ExpenseChart({ categories }: ExpenseChartProps) {\n  const [chartType, setChartType] = useState<'pie' | 'doughnut'>('doughnut');\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\n\n  // Calculate total expenses\n  const totalExpenses = categories.reduce((sum, item) => sum + item.amount, 0);\n\n  // Sort categories by amount (descending)\n  const sortedCategories = [...categories].sort((a, b) => b.amount - a.amount);\n\n  // Filter categories based on selection\n  const displayCategories = selectedCategory\n    ? sortedCategories.filter(item => item.category.id === selectedCategory)\n    : sortedCategories;\n\n  const data = {\n    labels: displayCategories.map((item) => item.category.name),\n    datasets: [\n      {\n        data: displayCategories.map((item) => item.amount),\n        backgroundColor: displayCategories.map((item) => item.category.color || '#4F46E5'),\n        borderColor: displayCategories.map(() => '#ffffff'),\n        borderWidth: 2,\n        hoverOffset: 15,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom' as const,\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        callbacks: {\n          label: function (context: any) {\n            const label = context.label || '';\n            const value = context.raw || 0;\n            const percentage = ((value / totalExpenses) * 100).toFixed(1);\n            return `${label}: ${formatCurrency(value)} (${percentage}%)`;\n          },\n        },\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        padding: 12,\n        titleFont: {\n          size: 14,\n        },\n        bodyFont: {\n          size: 13\n        },\n        displayColors: true,\n        boxPadding: 5\n      },\n      title: {\n        display: true,\n        text: 'Expense Distribution',\n        font: {\n          size: 16,\n          weight: 'bold' as const\n        },\n        padding: {\n          bottom: 15\n        }\n      }\n    },\n    animation: {\n      animateScale: true,\n      animateRotate: true\n    },\n    cutout: chartType === 'doughnut' ? '60%' : undefined,\n  };\n\n  const handleToggleChartType = () => {\n    setChartType(chartType === 'pie' ? 'doughnut' : 'pie');\n  };\n\n  const handleCategoryClick = (categoryId: string) => {\n    setSelectedCategory(selectedCategory === categoryId ? null : categoryId);\n  };\n\n  return (\n    <motion.div\n      className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      whileHover={{ y: -5, boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\" }}\n    >\n      <div className=\"flex justify-between items-center mb-4\">\n        <h3 className=\"text-lg font-medium text-light-text-primary dark:text-dark-text-primary\">Expense Breakdown</h3>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={handleToggleChartType}\n            className=\"px-3 py-1 text-xs font-medium rounded-md bg-primary/10 dark:bg-primary/20 text-primary hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors\"\n          >\n            {chartType === 'pie' ? 'Switch to Doughnut' : 'Switch to Pie'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"mt-4 h-72\">\n        {categories.length > 0 ? (\n          <div className=\"relative h-full\">\n            {chartType === 'pie' ? (\n              <Pie data={data} options={options} />\n            ) : (\n              <Doughnut data={data} options={options} />\n            )}\n\n            {chartType === 'doughnut' && (\n              <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\n                <div className=\"text-center\">\n                  <p className=\"text-sm text-light-text-secondary dark:text-dark-text-secondary\">Total</p>\n                  <p className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">{formatCurrency(totalExpenses)}</p>\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"flex flex-col items-center justify-center h-full text-light-text-secondary dark:text-dark-text-secondary\">\n            <svg className=\"w-16 h-16 mb-4 text-light-text-muted dark:text-dark-text-muted\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <p className=\"mb-2 font-medium text-light-text-primary dark:text-dark-text-primary\">No expense data yet</p>\n            <p className=\"text-sm text-center\">Add transactions to see your expense breakdown</p>\n            <a href=\"/transactions\" className=\"mt-4 px-4 py-2 text-sm font-medium text-dark-bg bg-primary rounded-md hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\">\n              Add Transaction\n            </a>\n          </div>\n        )}\n      </div>\n\n      {categories.length > 0 ? (\n        <div className=\"mt-6\">\n          <h4 className=\"text-sm font-medium text-light-text-primary dark:text-dark-text-primary mb-2\">Top Expenses</h4>\n          <div className=\"space-y-2\">\n            {sortedCategories.slice(0, 3).map((item) => (\n              <motion.div\n                key={item.category.id}\n                className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${\n                  selectedCategory === item.category.id\n                    ? 'bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30'\n                    : 'hover:bg-light-accent dark:hover:bg-dark-accent border border-transparent'\n                }`}\n                onClick={() => handleCategoryClick(item.category.id)}\n                whileHover={{ scale: 1.01 }}\n                whileTap={{ scale: 0.99 }}\n              >\n                <div className=\"flex items-center\">\n                  <div\n                    className=\"w-3 h-3 rounded-full mr-2\"\n                    style={{ backgroundColor: item.category.color || '#00C6FF' }}\n                  />\n                  <span className=\"text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">{item.category.name}</span>\n                </div>\n                <div className=\"text-sm font-medium text-light-text-primary dark:text-dark-text-primary\">{formatCurrency(item.amount)}</div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      ) : null}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CAAC,+JAAA,CAAA,aAAU,EAAE,+JAAA,CAAA,UAAO,EAAE,+JAAA,CAAA,SAAM,EAAE,+JAAA,CAAA,QAAK,EAAE,+JAAA,CAAA,cAAW,EAAE,+JAAA,CAAA,aAAU;AAS7D,SAAS,aAAa,EAAE,UAAU,EAAqB;;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,2BAA2B;IAC3B,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;IAE1E,yCAAyC;IACzC,MAAM,mBAAmB;WAAI;KAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IAE3E,uCAAuC;IACvC,MAAM,oBAAoB,mBACtB,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,oBACrD;IAEJ,MAAM,OAAO;QACX,QAAQ,kBAAkB,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,IAAI;QAC1D,UAAU;YACR;gBACE,MAAM,kBAAkB,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM;gBACjD,iBAAiB,kBAAkB,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,KAAK,IAAI;gBACxE,aAAa,kBAAkB,GAAG,CAAC,IAAM;gBACzC,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;gBACV,QAAQ;oBACN,eAAe;oBACf,SAAS;oBACT,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAU,OAAY;wBAC3B,MAAM,QAAQ,QAAQ,KAAK,IAAI;wBAC/B,MAAM,QAAQ,QAAQ,GAAG,IAAI;wBAC7B,MAAM,aAAa,CAAC,AAAC,QAAQ,gBAAiB,GAAG,EAAE,OAAO,CAAC;wBAC3D,OAAO,GAAG,MAAM,EAAE,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC;oBAC9D;gBACF;gBACA,iBAAiB;gBACjB,SAAS;gBACT,WAAW;oBACT,MAAM;gBACR;gBACA,UAAU;oBACR,MAAM;gBACR;gBACA,eAAe;gBACf,YAAY;YACd;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;gBACA,SAAS;oBACP,QAAQ;gBACV;YACF;QACF;QACA,WAAW;YACT,cAAc;YACd,eAAe;QACjB;QACA,QAAQ,cAAc,aAAa,QAAQ;IAC7C;IAEA,MAAM,wBAAwB;QAC5B,aAAa,cAAc,QAAQ,aAAa;IAClD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,qBAAqB,aAAa,OAAO;IAC/D;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,GAAG,CAAC;YAAG,WAAW;QAAsC;;0BAEtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0E;;;;;;kCACxF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAET,cAAc,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;0BAKpD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,GAAG,kBACnB,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,sBACb,6LAAC,yJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAM,SAAS;;;;;iDAE1B,6LAAC,yJAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAM,SAAS;;;;;;wBAGhC,cAAc,4BACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAkE;;;;;;kDAC/E,6LAAC;wCAAE,WAAU;kDAAyE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;yCAM7G,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAiE,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACrH,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAK,GAAE;;;;;;;;;;;sCAEzE,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;sCACpF,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,6LAAC;4BAAE,MAAK;4BAAgB,WAAU;sCAAsL;;;;;;;;;;;;;;;;;YAO7N,WAAW,MAAM,GAAG,kBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+E;;;;;;kCAC7F,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAW,CAAC,gEAAgE,EAC1E,qBAAqB,KAAK,QAAQ,CAAC,EAAE,GACjC,qFACA,6EACJ;gCACF,SAAS,IAAM,oBAAoB,KAAK,QAAQ,CAAC,EAAE;gCACnD,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,KAAK,QAAQ,CAAC,KAAK,IAAI;gDAAU;;;;;;0DAE7D,6LAAC;gDAAK,WAAU;0DAA2E,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;kDAE/G,6LAAC;wCAAI,WAAU;kDAA2E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;;;;;;;+BAjB/G,KAAK,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;uBAsB3B;;;;;;;AAGV;GA5KwB;KAAA", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/dashboard/BudgetProgress.tsx"], "sourcesContent": ["'use client';\n\nimport { Category } from '@/lib/types';\nimport { calculatePercentage, formatCurrency } from '@/lib/utils';\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { useTheme } from '@/context/ThemeContext';\n\ninterface BudgetProgressProps {\n  budgets: {\n    category: Category;\n    budgeted: number;\n    spent: number;\n    remaining: number;\n  }[];\n}\n\nexport default function BudgetProgress({ budgets }: BudgetProgressProps) {\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n  const [sortBy, setSortBy] = useState<'name' | 'percentage' | 'remaining'>('percentage');\n\n  // Sort budgets based on selected criteria\n  const sortedBudgets = [...budgets].sort((a, b) => {\n    if (sortBy === 'name') {\n      return a.category.name.localeCompare(b.category.name);\n    } else if (sortBy === 'percentage') {\n      const percentageA = calculatePercentage(a.spent, a.budgeted);\n      const percentageB = calculatePercentage(b.spent, b.budgeted);\n      return percentageB - percentageA; // Descending order\n    } else {\n      return a.remaining - b.remaining; // Ascending order (least remaining first)\n    }\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.05\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -10 },\n    visible: { opacity: 1, x: 0 }\n  };\n\n  return (\n    <motion.div\n      className={`p-6 rounded-xl border finance-card ${\n        isDark\n          ? 'bg-dark-card border-dark-border shadow-lg'\n          : 'bg-light-card border-light-border shadow-sm'\n      }`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      whileHover={{\n        y: -2,\n        boxShadow: isDark ? \"var(--glow-primary)\" : \"var(--hover-shadow)\"\n      }}\n    >\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className={`text-lg font-rajdhani font-semibold leading-6 letter-spacing-wide ${\n          isDark ? 'text-dark-text-primary' : 'text-light-text-primary'\n        }`}>\n          BUDGET PROGRESS\n        </h3>\n        <div className=\"flex space-x-1 text-xs\">\n          <span className={`mr-2 font-rajdhani ${\n            isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'\n          }`}>\n            Sort by:\n          </span>\n          <button\n            onClick={() => setSortBy('name')}\n            className={`px-2 py-1 rounded font-rajdhani font-medium transition-all duration-200 ${\n              sortBy === 'name'\n                ? isDark\n                  ? 'bg-primary/20 text-primary border border-primary/30'\n                  : 'bg-primary/10 text-primary border border-primary/20'\n                : isDark\n                ? 'text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                : 'text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n            }`}\n          >\n            NAME\n          </button>\n          <button\n            onClick={() => setSortBy('percentage')}\n            className={`px-2 py-1 rounded font-rajdhani font-medium transition-all duration-200 ${\n              sortBy === 'percentage'\n                ? isDark\n                  ? 'bg-primary/20 text-primary border border-primary/30'\n                  : 'bg-primary/10 text-primary border border-primary/20'\n                : isDark\n                ? 'text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                : 'text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n            }`}\n          >\n            % USED\n          </button>\n          <button\n            onClick={() => setSortBy('remaining')}\n            className={`px-2 py-1 rounded font-rajdhani font-medium transition-all duration-200 ${\n              sortBy === 'remaining'\n                ? isDark\n                  ? 'bg-primary/20 text-primary border border-primary/30'\n                  : 'bg-primary/10 text-primary border border-primary/20'\n                : isDark\n                ? 'text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                : 'text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n            }`}\n          >\n            REMAINING\n          </button>\n        </div>\n      </div>\n\n      <motion.div\n        className=\"space-y-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {sortedBudgets.length > 0 ? (\n          sortedBudgets.map((budget) => {\n            const percentage = calculatePercentage(budget.spent, budget.budgeted);\n            let barColor, textColor, bgColor;\n\n            if (percentage >= 90) {\n              barColor = 'bg-danger';\n              textColor = isDark ? 'text-red-400' : 'text-red-600';\n              bgColor = isDark ? 'bg-red-900/20 border-red-800/30' : 'bg-red-50 border-red-200';\n            } else if (percentage >= 75) {\n              barColor = 'bg-warning';\n              textColor = isDark ? 'text-yellow-400' : 'text-yellow-600';\n              bgColor = isDark ? 'bg-yellow-900/20 border-yellow-800/30' : 'bg-yellow-50 border-yellow-200';\n            } else {\n              barColor = 'bg-success';\n              textColor = isDark ? 'text-green-400' : 'text-green-600';\n              bgColor = isDark ? 'bg-green-900/20 border-green-800/30' : 'bg-green-50 border-green-200';\n            }\n\n            return (\n              <motion.div\n                key={budget.category.id}\n                className={`p-4 border rounded-xl transition-all duration-200 ${bgColor} ${\n                  isDark\n                    ? 'hover:bg-dark-accent/50 hover:border-dark-border'\n                    : 'hover:bg-light-accent hover:border-light-border'\n                }`}\n                variants={itemVariants}\n                whileHover={{ scale: 1.02, y: -2 }}\n              >\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center\">\n                    <div\n                      className=\"w-3 h-3 rounded-full mr-3\"\n                      style={{ backgroundColor: budget.category.color || '#4F46E5' }}\n                    />\n                    <span className={`text-sm font-rajdhani font-semibold letter-spacing-wide ${\n                      isDark ? 'text-dark-text-primary' : 'text-light-text-primary'\n                    }`}>\n                      {budget.category.name.toUpperCase()}\n                    </span>\n                  </div>\n                  <span className={`text-sm font-orbitron font-bold ${\n                    isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'\n                  }`}>\n                    {formatCurrency(budget.spent)} / {formatCurrency(budget.budgeted)}\n                  </span>\n                </div>\n\n                <div className={`w-full h-3 rounded-full ${\n                  isDark ? 'bg-dark-accent' : 'bg-light-accent'\n                }`}>\n                  <motion.div\n                    className={`h-3 rounded-full ${barColor} shadow-sm`}\n                    style={{ width: '0%' }}\n                    animate={{ width: `${Math.min(percentage, 100)}%` }}\n                    transition={{ duration: 1.2, ease: \"easeOut\" }}\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between mt-3\">\n                  <span className={`text-xs font-rajdhani font-bold ${textColor} letter-spacing-wide`}>\n                    {percentage}% USED\n                  </span>\n                  <span className={`text-xs font-orbitron font-medium ${\n                    isDark ? 'text-dark-text-muted' : 'text-light-text-muted'\n                  }`}>\n                    {formatCurrency(budget.remaining)} remaining\n                  </span>\n                </div>\n              </motion.div>\n            );\n          })\n        ) : (\n          <div className={`py-12 text-center rounded-xl ${\n            isDark\n              ? 'bg-dark-accent/50 border border-dark-border'\n              : 'bg-light-accent border border-light-border'\n          }`}>\n            <svg className={`w-16 h-16 mx-auto mb-4 ${\n              isDark ? 'text-dark-text-muted' : 'text-light-text-muted'\n            }`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n            </svg>\n            <p className={`font-rajdhani font-bold text-lg letter-spacing-wide ${\n              isDark ? 'text-dark-text-primary' : 'text-light-text-primary'\n            }`}>\n              NO BUDGETS SET UP YET\n            </p>\n            <p className={`text-sm mt-2 mb-6 font-rajdhani ${\n              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'\n            }`}>\n              Create a budget to track your spending and achieve your financial goals\n            </p>\n            <motion.a\n              href=\"/budgets\"\n              className=\"inline-flex items-center px-6 py-3 border border-transparent text-sm font-rajdhani font-semibold rounded-xl shadow-sm text-dark-bg bg-primary hover:bg-primary-hover transition-all duration-200 letter-spacing-wide\"\n              whileHover={{ scale: 1.05, boxShadow: \"var(--glow-primary)\" }}\n              whileTap={{ scale: 0.95 }}\n            >\n              CREATE BUDGET\n            </motion.a>\n          </div>\n        )}\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AANA;;;;;AAiBe,SAAS,eAAe,EAAE,OAAO,EAAuB;;IACrE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IACzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,0CAA0C;IAC1C,MAAM,gBAAgB;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC1C,IAAI,WAAW,QAAQ;YACrB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,IAAI;QACtD,OAAO,IAAI,WAAW,cAAc;YAClC,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ;YAC3D,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ;YAC3D,OAAO,cAAc,aAAa,mBAAmB;QACvD,OAAO;YACL,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS,EAAE,0CAA0C;QAC9E;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mCAAmC,EAC7C,SACI,8CACA,+CACJ;QACF,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YACV,GAAG,CAAC;YACJ,WAAW,SAAS,wBAAwB;QAC9C;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,CAAC,kEAAkE,EAChF,SAAS,2BAA2B,2BACpC;kCAAE;;;;;;kCAGJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,CAAC,mBAAmB,EACnC,SAAS,6BAA6B,6BACtC;0CAAE;;;;;;0CAGJ,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,wEAAwE,EAClF,WAAW,SACP,SACE,wDACA,wDACF,SACA,+EACA,iFACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,wEAAwE,EAClF,WAAW,eACP,SACE,wDACA,wDACF,SACA,+EACA,iFACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,wEAAwE,EAClF,WAAW,cACP,SACE,wDACA,wDACF,SACA,+EACA,iFACJ;0CACH;;;;;;;;;;;;;;;;;;0BAML,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;0BAEP,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC;oBACjB,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,QAAQ;oBACpE,IAAI,UAAU,WAAW;oBAEzB,IAAI,cAAc,IAAI;wBACpB,WAAW;wBACX,YAAY,SAAS,iBAAiB;wBACtC,UAAU,SAAS,oCAAoC;oBACzD,OAAO,IAAI,cAAc,IAAI;wBAC3B,WAAW;wBACX,YAAY,SAAS,oBAAoB;wBACzC,UAAU,SAAS,0CAA0C;oBAC/D,OAAO;wBACL,WAAW;wBACX,YAAY,SAAS,mBAAmB;wBACxC,UAAU,SAAS,wCAAwC;oBAC7D;oBAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAW,CAAC,kDAAkD,EAAE,QAAQ,CAAC,EACvE,SACI,qDACA,mDACJ;wBACF,UAAU;wBACV,YAAY;4BAAE,OAAO;4BAAM,GAAG,CAAC;wBAAE;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK,IAAI;gDAAU;;;;;;0DAE/D,6LAAC;gDAAK,WAAW,CAAC,wDAAwD,EACxE,SAAS,2BAA2B,2BACpC;0DACC,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW;;;;;;;;;;;;kDAGrC,6LAAC;wCAAK,WAAW,CAAC,gCAAgC,EAChD,SAAS,6BAA6B,6BACtC;;4CACC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;4CAAE;4CAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,QAAQ;;;;;;;;;;;;;0CAIpE,6LAAC;gCAAI,WAAW,CAAC,wBAAwB,EACvC,SAAS,mBAAmB,mBAC5B;0CACA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC;oCACnD,OAAO;wCAAE,OAAO;oCAAK;oCACrB,SAAS;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC;oCAAC;oCAClD,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAU;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,gCAAgC,EAAE,UAAU,oBAAoB,CAAC;;4CAChF;4CAAW;;;;;;;kDAEd,6LAAC;wCAAK,WAAW,CAAC,kCAAkC,EAClD,SAAS,yBAAyB,yBAClC;;4CACC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;4CAAE;;;;;;;;;;;;;;uBA9CjC,OAAO,QAAQ,CAAC,EAAE;;;;;gBAmD7B,mBAEA,6LAAC;oBAAI,WAAW,CAAC,6BAA6B,EAC5C,SACI,gDACA,8CACJ;;sCACA,6LAAC;4BAAI,WAAW,CAAC,uBAAuB,EACtC,SAAS,yBAAyB,yBAClC;4BAAE,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACzC,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAK,GAAE;;;;;;;;;;;sCAEzE,6LAAC;4BAAE,WAAW,CAAC,oDAAoD,EACjE,SAAS,2BAA2B,2BACpC;sCAAE;;;;;;sCAGJ,6LAAC;4BAAE,WAAW,CAAC,gCAAgC,EAC7C,SAAS,6BAA6B,6BACtC;sCAAE;;;;;;sCAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,MAAK;4BACL,WAAU;4BACV,YAAY;gCAAE,OAAO;gCAAM,WAAW;4BAAsB;4BAC5D,UAAU;gCAAE,OAAO;4BAAK;sCACzB;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA3NwB;;QACJ,kIAAA,CAAA,WAAQ;;;KADJ", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/dashboard/IncomeExpenseChart.tsx"], "sourcesContent": ["'use client';\n\nimport { Transaction } from '@/lib/types';\nimport { formatCurrency } from '@/lib/utils';\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend,\n  ChartOptions\n} from 'chart.js';\nimport { Chart } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface IncomeExpenseChartProps {\n  transactions: Transaction[];\n}\n\ntype TimeFrame = 'week' | 'month' | 'year';\n\nexport default function IncomeExpenseChart({ transactions }: IncomeExpenseChartProps) {\n  const [timeFrame, setTimeFrame] = useState<TimeFrame>('month');\n\n  // Group transactions by date according to the selected time frame\n  const groupedData = groupTransactionsByTimeFrame(transactions, timeFrame);\n\n  // Prepare data for the chart\n  const labels = Object.keys(groupedData);\n  const incomeData = labels.map(label => groupedData[label].income);\n  const expenseData = labels.map(label => groupedData[label].expense);\n  const netData = labels.map(label => groupedData[label].income - groupedData[label].expense);\n\n  const data = {\n    labels,\n    datasets: [\n      {\n        label: 'Income',\n        data: incomeData,\n        backgroundColor: 'rgba(50, 255, 126, 0.7)',\n        borderColor: 'rgb(50, 255, 126)',\n        borderWidth: 1,\n      },\n      {\n        label: 'Expenses',\n        data: expenseData.map(value => -value), // Negate for visual effect\n        backgroundColor: 'rgba(255, 71, 87, 0.7)',\n        borderColor: 'rgb(255, 71, 87)',\n        borderWidth: 1,\n      },\n      {\n        label: 'Net',\n        data: netData,\n        type: 'line' as const,\n        borderColor: 'rgb(0, 198, 255)',\n        backgroundColor: 'rgba(0, 198, 255, 0.1)',\n        borderWidth: 2,\n        fill: false,\n        tension: 0.4,\n        pointBackgroundColor: 'rgb(0, 198, 255)',\n        pointRadius: 3,\n        pointHoverRadius: 5,\n        yAxisID: 'y1',\n      }\n    ],\n  };\n\n  const options: any = {\n    responsive: true,\n    maintainAspectRatio: false,\n    scales: {\n      x: {\n        grid: {\n          display: false,\n        },\n      },\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return formatCurrency(Math.abs(Number(value)));\n          }\n        },\n        grid: {\n          borderDash: [2, 4] as [number, number],\n        },\n      },\n      y1: {\n        position: 'right',\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return formatCurrency(Number(value));\n          }\n        },\n        grid: {\n          display: false,\n        },\n      }\n    },\n    plugins: {\n      legend: {\n        position: 'top' as const,\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            let label = context.dataset.label || '';\n            let value = context.raw as number;\n\n            // For expenses, we're displaying negative values as positive in the tooltip\n            if (label === 'Expenses') {\n              value = Math.abs(value);\n            }\n\n            return `${label}: ${formatCurrency(value)}`;\n          }\n        },\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        padding: 10,\n        titleFont: {\n          size: 14,\n        },\n        bodyFont: {\n          size: 13\n        }\n      },\n      title: {\n        display: true,\n        text: 'Income vs. Expenses',\n        font: {\n          size: 16,\n          weight: 'bold' as const\n        },\n        padding: {\n          bottom: 15\n        }\n      }\n    },\n    animation: {\n      duration: 1000,\n    },\n  };\n\n  return (\n    <motion.div\n      className=\"p-6 rounded-xl border bg-light-surface dark:bg-dark-surface border-light-border dark:border-dark-border shadow-sm\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n      whileHover={{ y: -5, boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\" }}\n    >\n      <div className=\"flex justify-between items-center mb-4\">\n        <h3 className=\"text-lg font-medium text-light-text-primary dark:text-dark-text-primary\">Income vs. Expenses</h3>\n        <div className=\"flex space-x-1\">\n          {(['week', 'month', 'year'] as TimeFrame[]).map((frame) => (\n            <button\n              key={frame}\n              onClick={() => setTimeFrame(frame)}\n              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${\n                timeFrame === frame\n                  ? 'bg-primary text-dark-bg dark:bg-primary'\n                  : 'bg-light-accent text-light-text-secondary hover:bg-light-border dark:bg-dark-accent dark:text-dark-text-secondary dark:hover:bg-dark-border'\n              }`}\n            >\n              {frame.charAt(0).toUpperCase() + frame.slice(1)}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"mt-4 h-72\">\n        {transactions.length > 0 ? (\n          <Chart type=\"bar\" data={data} options={options} />\n        ) : (\n          <div className=\"flex flex-col items-center justify-center h-full text-light-text-secondary dark:text-dark-text-secondary\">\n            <svg className=\"w-16 h-16 mb-4 text-light-text-muted dark:text-dark-text-muted\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\n            </svg>\n            <p className=\"mb-2 font-medium text-light-text-primary dark:text-dark-text-primary\">No income or expense data yet</p>\n            <p className=\"text-sm text-center\">Add transactions to see your financial trends</p>\n            <a href=\"/transactions\" className=\"mt-4 px-4 py-2 text-sm font-medium text-dark-bg bg-primary rounded-md hover:bg-primary-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\">\n              Add Transaction\n            </a>\n          </div>\n        )}\n      </div>\n\n      {transactions.length > 0 ? (\n        <div className=\"mt-6 grid grid-cols-3 gap-4\">\n          <div className=\"bg-success-light/10 dark:bg-success-dark/10 p-3 rounded-lg border border-success-light/20 dark:border-success-dark/20\">\n            <p className=\"text-sm text-success-light dark:text-success-dark font-medium\">Total Income</p>\n            <p className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n              {formatCurrency(incomeData.reduce((sum, val) => sum + val, 0))}\n            </p>\n          </div>\n          <div className=\"bg-error-light/10 dark:bg-error-dark/10 p-3 rounded-lg border border-error-light/20 dark:border-error-dark/20\">\n            <p className=\"text-sm text-error-light dark:text-error-dark font-medium\">Total Expenses</p>\n            <p className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n              {formatCurrency(expenseData.reduce((sum, val) => sum + val, 0))}\n            </p>\n          </div>\n          <div className=\"bg-primary/10 dark:bg-primary/20 p-3 rounded-lg border border-primary/20 dark:border-primary/30\">\n            <p className=\"text-sm text-primary font-medium\">Net Savings</p>\n            <p className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n              {formatCurrency(netData.reduce((sum, val) => sum + val, 0))}\n            </p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"mt-6 p-4 border border-light-border dark:border-dark-border rounded-lg bg-light-accent dark:bg-dark-accent\">\n          <p className=\"text-center text-light-text-secondary dark:text-dark-text-secondary text-sm\">\n            Add income and expense transactions to see your financial summary\n          </p>\n        </div>\n      )}\n    </motion.div>\n  );\n}\n\n// Helper function to group transactions by time frame\nfunction groupTransactionsByTimeFrame(transactions: Transaction[], timeFrame: TimeFrame) {\n  const result: Record<string, { income: number; expense: number }> = {};\n\n  // Sort transactions by date\n  const sortedTransactions = [...transactions].sort(\n    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()\n  );\n\n  // Group by the selected time frame\n  sortedTransactions.forEach(transaction => {\n    const date = new Date(transaction.date);\n    let key: string;\n\n    if (timeFrame === 'week') {\n      // Get the week number and year\n      const weekNumber = getWeekNumber(date);\n      key = `Week ${weekNumber}`;\n    } else if (timeFrame === 'month') {\n      // Format as \"Jan 2023\"\n      key = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    } else {\n      // Format as \"2023\"\n      key = date.getFullYear().toString();\n    }\n\n    if (!result[key]) {\n      result[key] = { income: 0, expense: 0 };\n    }\n\n    if (transaction.is_income) {\n      result[key].income += transaction.amount;\n    } else {\n      result[key].expense += transaction.amount;\n    }\n  });\n\n  return result;\n}\n\n// Helper function to get the week number\nfunction getWeekNumber(date: Date): number {\n  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);\n  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;\n  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAYA;;;AAlBA;;;;;;AAoBA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,+JAAA,CAAA,gBAAa,EACb,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,aAAU,EACV,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,eAAY,EACZ,+JAAA,CAAA,QAAK,EACL,+JAAA,CAAA,UAAO,EACP,+JAAA,CAAA,SAAM;AASO,SAAS,mBAAmB,EAAE,YAAY,EAA2B;;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAEtD,kEAAkE;IAClE,MAAM,cAAc,6BAA6B,cAAc;IAE/D,6BAA6B;IAC7B,MAAM,SAAS,OAAO,IAAI,CAAC;IAC3B,MAAM,aAAa,OAAO,GAAG,CAAC,CAAA,QAAS,WAAW,CAAC,MAAM,CAAC,MAAM;IAChE,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA,QAAS,WAAW,CAAC,MAAM,CAAC,OAAO;IAClE,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA,QAAS,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO;IAE1F,MAAM,OAAO;QACX;QACA,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAA,QAAS,CAAC;gBAChC,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,sBAAsB;gBACtB,aAAa;gBACb,kBAAkB;gBAClB,SAAS;YACX;SACD;IACH;IAEA,MAAM,UAAe;QACnB,YAAY;QACZ,qBAAqB;QACrB,QAAQ;YACN,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;YACF;YACA,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,CAAC,OAAO;oBACxC;gBACF;gBACA,MAAM;oBACJ,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;YACF;YACA,IAAI;gBACF,UAAU;gBACV,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;oBAC/B;gBACF;gBACA,MAAM;oBACJ,SAAS;gBACX;YACF;QACF;QACA,SAAS;YACP,QAAQ;gBACN,UAAU;gBACV,QAAQ;oBACN,eAAe;oBACf,SAAS;oBACT,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,IAAI,QAAQ,QAAQ,OAAO,CAAC,KAAK,IAAI;wBACrC,IAAI,QAAQ,QAAQ,GAAG;wBAEvB,4EAA4E;wBAC5E,IAAI,UAAU,YAAY;4BACxB,QAAQ,KAAK,GAAG,CAAC;wBACnB;wBAEA,OAAO,GAAG,MAAM,EAAE,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;oBAC7C;gBACF;gBACA,iBAAiB;gBACjB,SAAS;gBACT,WAAW;oBACT,MAAM;gBACR;gBACA,UAAU;oBACR,MAAM;gBACR;YACF;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM;oBACN,QAAQ;gBACV;gBACA,SAAS;oBACP,QAAQ;gBACV;YACF;QACF;QACA,WAAW;YACT,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,YAAY;YAAE,GAAG,CAAC;YAAG,WAAW;QAAsC;;0BAEtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0E;;;;;;kCACxF,6LAAC;wBAAI,WAAU;kCACZ,AAAC;4BAAC;4BAAQ;4BAAS;yBAAO,CAAiB,GAAG,CAAC,CAAC,sBAC/C,6LAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,QACV,4CACA,+IACJ;0CAED,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;+BARxC;;;;;;;;;;;;;;;;0BAcb,6LAAC;gBAAI,WAAU;0BACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC,yJAAA,CAAA,QAAK;oBAAC,MAAK;oBAAM,MAAM;oBAAM,SAAS;;;;;yCAEvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAiE,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACrH,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAK,GAAE;;;;;;;;;;;sCAEzE,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;sCACpF,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,6LAAC;4BAAE,MAAK;4BAAgB,WAAU;sCAAsL;;;;;;;;;;;;;;;;;YAO7N,aAAa,MAAM,GAAG,kBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAgE;;;;;;0CAC7E,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;;;;;;;;;;;;kCAG/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA4D;;;;;;0CACzE,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;;;;;;;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;;;;;;;;;;;;;;;;;qCAK9D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAA8E;;;;;;;;;;;;;;;;;AAOrG;GA5MwB;KAAA;AA8MxB,sDAAsD;AACtD,SAAS,6BAA6B,YAA2B,EAAE,SAAoB;IACrF,MAAM,SAA8D,CAAC;IAErE,4BAA4B;IAC5B,MAAM,qBAAqB;WAAI;KAAa,CAAC,IAAI,CAC/C,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAGjE,mCAAmC;IACnC,mBAAmB,OAAO,CAAC,CAAA;QACzB,MAAM,OAAO,IAAI,KAAK,YAAY,IAAI;QACtC,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,+BAA+B;YAC/B,MAAM,aAAa,cAAc;YACjC,MAAM,CAAC,KAAK,EAAE,YAAY;QAC5B,OAAO,IAAI,cAAc,SAAS;YAChC,uBAAuB;YACvB,MAAM,KAAK,kBAAkB,CAAC,SAAS;gBAAE,OAAO;gBAAS,MAAM;YAAU;QAC3E,OAAO;YACL,mBAAmB;YACnB,MAAM,KAAK,WAAW,GAAG,QAAQ;QACnC;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,MAAM,CAAC,IAAI,GAAG;gBAAE,QAAQ;gBAAG,SAAS;YAAE;QACxC;QAEA,IAAI,YAAY,SAAS,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,YAAY,MAAM;QAC1C,OAAO;YACL,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,YAAY,MAAM;QAC3C;IACF;IAEA,OAAO;AACT;AAEA,yCAAyC;AACzC,SAAS,cAAc,IAAU;IAC/B,MAAM,iBAAiB,IAAI,KAAK,KAAK,WAAW,IAAI,GAAG;IACvD,MAAM,iBAAiB,CAAC,KAAK,OAAO,KAAK,eAAe,OAAO,EAAE,IAAI;IACrE,OAAO,KAAK,IAAI,CAAC,CAAC,iBAAiB,eAAe,MAAM,KAAK,CAAC,IAAI;AACpE", "debugId": null}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/transactions/AddTransactionForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiDollarSign, FiCalendar, FiFileText, FiTag, FiArrowUp, FiArrowDown, FiX, FiCheck } from 'react-icons/fi';\nimport { useTheme } from '@/context/ThemeContext';\nimport { Category } from '@/lib/types';\n\ninterface AddTransactionFormProps {\n  categories: Category[];\n  onAddTransaction: (transaction: {\n    amount: number;\n    description: string;\n    category: string;\n    date: string;\n    type: 'income' | 'expense';\n  }) => Promise<void>;\n  onClose: () => void;\n}\n\nexport default function AddTransactionForm({ categories, onAddTransaction, onClose }: AddTransactionFormProps) {\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n  \n  const [amount, setAmount] = useState('');\n  const [description, setDescription] = useState('');\n  const [category, setCategory] = useState('');\n  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);\n  const [type, setType] = useState<'income' | 'expense'>('expense');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    // Validate form\n    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {\n      setError('Please enter a valid amount');\n      return;\n    }\n\n    if (!description.trim()) {\n      setError('Please enter a description');\n      return;\n    }\n\n    if (!category) {\n      setError('Please select a category');\n      return;\n    }\n\n    if (!date) {\n      setError('Please select a date');\n      return;\n    }\n\n    try {\n      setIsSubmitting(true);\n      \n      await onAddTransaction({\n        amount: Number(amount),\n        description,\n        category,\n        date,\n        type\n      });\n      \n      // Reset form\n      setAmount('');\n      setDescription('');\n      setCategory('');\n      setDate(new Date().toISOString().split('T')[0]);\n      setType('expense');\n      \n      // Close the form\n      onClose();\n    } catch (error) {\n      console.error('Error adding transaction:', error);\n      setError('Failed to add transaction. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const filteredCategories = categories.filter(c => \n    (type === 'income' && c.is_income) || (type === 'expense' && !c.is_income)\n  );\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 z-[9999] flex items-start justify-center p-4 bg-black/50 backdrop-blur-sm overflow-y-auto\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      onClick={onClose}\n      style={{ paddingTop: '100px', paddingBottom: '40px' }} // Account for navbar height and bottom spacing\n    >\n      <motion.div\n        className={`w-full max-w-lg mx-auto p-6 rounded-xl shadow-xl finance-card ${\n          isDark ? 'bg-dark-card border border-dark-border' : 'bg-light-card border border-light-border'\n        }`}\n        initial={{ scale: 0.9, y: 20, opacity: 0 }}\n        animate={{ scale: 1, y: 0, opacity: 1 }}\n        exit={{ scale: 0.9, y: 20, opacity: 0 }}\n        transition={{ type: 'spring', damping: 25, stiffness: 300 }}\n        onClick={(e) => e.stopPropagation()}\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className={`text-xl font-rajdhani font-bold letter-spacing-wide ${\n            isDark ? 'text-dark-text-primary' : 'text-light-text-primary'\n          }`}>\n            ADD NEW TRANSACTION\n          </h2>\n          <motion.button\n            className={`p-1 rounded-full ${isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={onClose}\n          >\n            <FiX className={`w-5 h-5 ${isDark ? 'text-gray-300' : 'text-gray-500'}`} />\n          </motion.button>\n        </div>\n\n        <AnimatePresence>\n          {error && (\n            <motion.div\n              className={`p-3 mb-4 rounded-lg flex items-center space-x-2 ${\n                isDark ? 'bg-red-900/30 text-red-200' : 'bg-red-50 text-red-500'\n              }`}\n              initial={{ opacity: 0, y: -10 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -10 }}\n            >\n              <FiX className=\"flex-shrink-0 w-5 h-5\" />\n              <span className=\"text-sm\">{error}</span>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Transaction Type */}\n          <div className=\"grid grid-cols-2 gap-3\">\n            <motion.button\n              type=\"button\"\n              className={`flex items-center justify-center p-3 rounded-lg border ${\n                type === 'expense'\n                  ? isDark\n                    ? 'bg-red-900/30 border-red-800 text-red-200'\n                    : 'bg-red-50 border-red-200 text-red-600'\n                  : isDark\n                  ? 'bg-gray-800 border-gray-700 text-gray-300'\n                  : 'bg-gray-50 border-gray-200 text-gray-500'\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={() => setType('expense')}\n            >\n              <FiArrowDown className={`w-5 h-5 mr-2 ${type === 'expense' ? 'text-red-500' : ''}`} />\n              <span>Expense</span>\n            </motion.button>\n            \n            <motion.button\n              type=\"button\"\n              className={`flex items-center justify-center p-3 rounded-lg border ${\n                type === 'income'\n                  ? isDark\n                    ? 'bg-green-900/30 border-green-800 text-green-200'\n                    : 'bg-green-50 border-green-200 text-green-600'\n                  : isDark\n                  ? 'bg-gray-800 border-gray-700 text-gray-300'\n                  : 'bg-gray-50 border-gray-200 text-gray-500'\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={() => setType('income')}\n            >\n              <FiArrowUp className={`w-5 h-5 mr-2 ${type === 'income' ? 'text-green-500' : ''}`} />\n              <span>Income</span>\n            </motion.button>\n          </div>\n\n          {/* Amount */}\n          <div>\n            <label htmlFor=\"amount\" className={`block text-sm font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>\n              Amount\n            </label>\n            <div className=\"relative\">\n              <div className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ${isDark ? 'text-gray-300' : 'text-gray-400'}`}>\n                <FiDollarSign className=\"w-5 h-5\" />\n              </div>\n              <input\n                type=\"number\"\n                id=\"amount\"\n                value={amount}\n                onChange={(e) => setAmount(e.target.value)}\n                className={`block w-full pl-10 pr-3 py-3 border rounded-lg ${\n                  isDark\n                    ? 'bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-indigo-500 focus:border-indigo-500'\n                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500'\n                }`}\n                placeholder=\"0.00\"\n                step=\"0.01\"\n                min=\"0\"\n              />\n            </div>\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className={`block text-sm font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>\n              Description\n            </label>\n            <div className=\"relative\">\n              <div className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ${isDark ? 'text-gray-300' : 'text-gray-400'}`}>\n                <FiFileText className=\"w-5 h-5\" />\n              </div>\n              <input\n                type=\"text\"\n                id=\"description\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                className={`block w-full pl-10 pr-3 py-3 border rounded-lg ${\n                  isDark\n                    ? 'bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-indigo-500 focus:border-indigo-500'\n                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500'\n                }`}\n                placeholder=\"What was this for?\"\n              />\n            </div>\n          </div>\n\n          {/* Category */}\n          <div>\n            <label htmlFor=\"category\" className={`block text-sm font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>\n              Category\n            </label>\n            <div className=\"relative\">\n              <div className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ${isDark ? 'text-gray-300' : 'text-gray-400'}`}>\n                <FiTag className=\"w-5 h-5\" />\n              </div>\n              <select\n                id=\"category\"\n                value={category}\n                onChange={(e) => setCategory(e.target.value)}\n                className={`block w-full pl-10 pr-3 py-3 border rounded-lg appearance-none ${\n                  isDark\n                    ? 'bg-gray-800 border-gray-700 text-white focus:ring-indigo-500 focus:border-indigo-500'\n                    : 'bg-white border-gray-300 text-gray-900 focus:ring-indigo-500 focus:border-indigo-500'\n                }`}\n              >\n                <option value=\"\">Select a category</option>\n                {filteredCategories.map((cat) => (\n                  <option key={cat.id} value={cat.id}>\n                    {cat.name}\n                  </option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg className={`h-5 w-5 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          {/* Date */}\n          <div>\n            <label htmlFor=\"date\" className={`block text-sm font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>\n              Date\n            </label>\n            <div className=\"relative\">\n              <div className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ${isDark ? 'text-gray-300' : 'text-gray-400'}`}>\n                <FiCalendar className=\"w-5 h-5\" />\n              </div>\n              <input\n                type=\"date\"\n                id=\"date\"\n                value={date}\n                onChange={(e) => setDate(e.target.value)}\n                className={`block w-full pl-10 pr-3 py-3 border rounded-lg ${\n                  isDark\n                    ? 'bg-gray-800 border-gray-700 text-white focus:ring-indigo-500 focus:border-indigo-500'\n                    : 'bg-white border-gray-300 text-gray-900 focus:ring-indigo-500 focus:border-indigo-500'\n                }`}\n              />\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-3 pt-2\">\n            <motion.button\n              type=\"button\"\n              className={`px-4 py-2 rounded-lg border ${\n                isDark\n                  ? 'border-gray-700 text-gray-300 hover:bg-gray-800'\n                  : 'border-gray-300 text-gray-700 hover:bg-gray-100'\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={onClose}\n            >\n              Cancel\n            </motion.button>\n            \n            <motion.button\n              type=\"submit\"\n              className={`px-4 py-2 rounded-lg bg-indigo-600 text-white ${\n                isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-indigo-700'\n              }`}\n              whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n              whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center\">\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Saving...\n                </div>\n              ) : (\n                <div className=\"flex items-center\">\n                  <FiCheck className=\"mr-2\" />\n                  Save Transaction\n                </div>\n              )}\n            </motion.button>\n          </div>\n        </form>\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAoBe,SAAS,mBAAmB,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAA2B;;IAC3G,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IAEzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,gBAAgB;QAChB,IAAI,CAAC,UAAU,MAAM,OAAO,YAAY,OAAO,WAAW,GAAG;YAC3D,SAAS;YACT;QACF;QAEA,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,IAAI;YACF,gBAAgB;YAEhB,MAAM,iBAAiB;gBACrB,QAAQ,OAAO;gBACf;gBACA;gBACA;gBACA;YACF;YAEA,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;YACZ,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9C,QAAQ;YAER,iBAAiB;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAC3C,AAAC,SAAS,YAAY,EAAE,SAAS,IAAM,SAAS,aAAa,CAAC,EAAE,SAAS;IAG3E,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,SAAS;QACT,OAAO;YAAE,YAAY;YAAS,eAAe;QAAO;kBAEpD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAC,8DAA8D,EACxE,SAAS,2CAA2C,4CACpD;YACF,SAAS;gBAAE,OAAO;gBAAK,GAAG;gBAAI,SAAS;YAAE;YACzC,SAAS;gBAAE,OAAO;gBAAG,GAAG;gBAAG,SAAS;YAAE;YACtC,MAAM;gBAAE,OAAO;gBAAK,GAAG;gBAAI,SAAS;YAAE;YACtC,YAAY;gBAAE,MAAM;gBAAU,SAAS;gBAAI,WAAW;YAAI;YAC1D,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAEjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAW,CAAC,oDAAoD,EAClE,SAAS,2BAA2B,2BACpC;sCAAE;;;;;;sCAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAW,CAAC,iBAAiB,EAAE,SAAS,sBAAsB,qBAAqB;4BACnF,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;4BACvB,SAAS;sCAET,cAAA,6LAAC,iJAAA,CAAA,MAAG;gCAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,kBAAkB,iBAAiB;;;;;;;;;;;;;;;;;8BAI3E,6LAAC,4LAAA,CAAA,kBAAe;8BACb,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAC,gDAAgD,EAC1D,SAAS,+BAA+B,0BACxC;wBACF,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;;0CAE3B,6LAAC,iJAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;8BAKjC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,WAAW,CAAC,uDAAuD,EACjE,SAAS,YACL,SACE,8CACA,0CACF,SACA,8CACA,4CACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,QAAQ;;sDAEvB,6LAAC,iJAAA,CAAA,cAAW;4CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,YAAY,iBAAiB,IAAI;;;;;;sDAClF,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,WAAW,CAAC,uDAAuD,EACjE,SAAS,WACL,SACE,oDACA,gDACF,SACA,8CACA,4CACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,QAAQ;;sDAEvB,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,WAAW,mBAAmB,IAAI;;;;;;sDACjF,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAS,WAAW,CAAC,+BAA+B,EAAE,SAAS,kBAAkB,iBAAiB;8CAAE;;;;;;8CAGnH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,kBAAkB,iBAAiB;sDAClI,cAAA,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAW,CAAC,+CAA+C,EACzD,SACI,8GACA,6GACJ;4CACF,aAAY;4CACZ,MAAK;4CACL,KAAI;;;;;;;;;;;;;;;;;;sCAMV,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAW,CAAC,+BAA+B,EAAE,SAAS,kBAAkB,iBAAiB;8CAAE;;;;;;8CAGxH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,kBAAkB,iBAAiB;sDAClI,cAAA,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAW,CAAC,+CAA+C,EACzD,SACI,8GACA,6GACJ;4CACF,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAW,CAAC,+BAA+B,EAAE,SAAS,kBAAkB,iBAAiB;8CAAE;;;;;;8CAGrH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,kBAAkB,iBAAiB;sDAClI,cAAA,6LAAC,iJAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAW,CAAC,+DAA+D,EACzE,SACI,yFACA,wFACJ;;8DAEF,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,mBAAmB,GAAG,CAAC,CAAC,oBACvB,6LAAC;wDAAoB,OAAO,IAAI,EAAE;kEAC/B,IAAI,IAAI;uDADE,IAAI,EAAE;;;;;;;;;;;sDAKvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,kBAAkB,iBAAiB;gDAAE,SAAQ;gDAAY,MAAK;0DAChG,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjK,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAW,CAAC,+BAA+B,EAAE,SAAS,kBAAkB,iBAAiB;8CAAE;;;;;;8CAGjH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qEAAqE,EAAE,SAAS,kBAAkB,iBAAiB;sDAClI,cAAA,6LAAC,iJAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,WAAW,CAAC,+CAA+C,EACzD,SACI,yFACA,wFACJ;;;;;;;;;;;;;;;;;;sCAMR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,WAAW,CAAC,4BAA4B,EACtC,SACI,oDACA,mDACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;8CACV;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,WAAW,CAAC,8CAA8C,EACxD,eAAe,kCAAkC,uBACjD;oCACF,YAAY,CAAC,eAAe;wCAAE,OAAO;oCAAK,IAAI,CAAC;oCAC/C,UAAU,CAAC,eAAe;wCAAE,OAAO;oCAAK,IAAI,CAAC;oCAC7C,UAAU;8CAET,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,6LAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,6LAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;6DAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;GA1TwB;;QACJ,kIAAA,CAAA,WAAQ;;;KADJ", "debugId": null}}, {"offset": {"line": 2279, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/dashboard/TransactionsManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FiPlus, FiFilter, FiDownload, FiTrash2, FiEdit, FiDollarSign, FiArrowUp, FiArrowDown } from 'react-icons/fi';\nimport { useTheme } from '@/context/ThemeContext';\nimport { Transaction, Category } from '@/lib/types';\nimport AddTransactionForm from '../transactions/AddTransactionForm';\n\ninterface TransactionsManagerProps {\n  transactions: Transaction[];\n  categories: Category[];\n  onTransactionAdded: () => void;\n}\n\nexport default function TransactionsManager({ transactions, categories, onTransactionAdded }: TransactionsManagerProps) {\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n  \n  const [showAddForm, setShowAddForm] = useState(false);\n  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>(transactions);\n  const [filter, setFilter] = useState('all'); // 'all', 'income', 'expense'\n  const [isLoading, setIsLoading] = useState(false);\n  const [userId, setUserId] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Get user ID from localStorage\n    const userJson = localStorage.getItem('cashminder_user');\n    if (userJson) {\n      const userData = JSON.parse(userJson);\n      setUserId(userData.id || null);\n    }\n\n    // Apply filters\n    if (filter === 'all') {\n      setFilteredTransactions(transactions);\n    } else if (filter === 'income') {\n      setFilteredTransactions(transactions.filter(t => t.is_income));\n    } else if (filter === 'expense') {\n      setFilteredTransactions(transactions.filter(t => !t.is_income));\n    }\n  }, [transactions, filter]);\n\n  const handleAddTransaction = async (transaction: {\n    amount: number;\n    description: string;\n    category: string;\n    date: string;\n    type: 'income' | 'expense';\n  }) => {\n    if (!userId) {\n      throw new Error('User not logged in');\n    }\n\n    setIsLoading(true);\n    \n    try {\n      const response = await fetch('/api/transactions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userId,\n          amount: transaction.amount,\n          description: transaction.description,\n          category: transaction.category,\n          date: transaction.date,\n          type: transaction.type,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to add transaction');\n      }\n\n      // Notify parent component to refresh data\n      onTransactionAdded();\n      \n      return await response.json();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return new Intl.DateTimeFormat('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    }).format(date);\n  };\n\n  const getCategoryName = (categoryId: string) => {\n    const category = categories.find(c => c.id === categoryId);\n    return category ? category.name : 'Uncategorized';\n  };\n\n  const getCategoryColor = (categoryId: string) => {\n    const category = categories.find(c => c.id === categoryId);\n    return category ? category.color : '#CBD5E0';\n  };\n\n  return (\n    <div className={`rounded-xl border finance-card ${\n      isDark ? 'bg-dark-card border-dark-border' : 'bg-light-card border-light-border'\n    } backdrop-blur-sm overflow-hidden shadow-lg`}>\n      <div className=\"p-6\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6\">\n          <div>\n            <h2 className={`text-xl font-rajdhani font-bold letter-spacing-wide ${\n              isDark ? 'text-dark-text-primary' : 'text-light-text-primary'\n            }`}>\n              TRANSACTIONS\n            </h2>\n            <p className={`mt-1 text-sm font-rajdhani ${\n              isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'\n            }`}>\n              {transactions.length > 0\n                ? `Showing ${filteredTransactions.length} of ${transactions.length} transactions`\n                : 'No transactions yet. Add your first one!'}\n            </p>\n          </div>\n          \n          <div className=\"flex mt-4 sm:mt-0 space-x-2\">\n            <div className={`flex rounded-lg overflow-hidden border ${\n              isDark ? 'border-dark-border' : 'border-light-border'\n            }`}>\n              <button\n                className={`px-3 py-1.5 text-sm font-rajdhani font-semibold transition-all duration-200 ${\n                  filter === 'all'\n                    ? isDark\n                      ? 'bg-primary/20 text-primary border-r border-primary/30'\n                      : 'bg-primary/10 text-primary border-r border-primary/20'\n                    : isDark\n                    ? 'bg-transparent text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                    : 'bg-transparent text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n                }`}\n                onClick={() => setFilter('all')}\n              >\n                ALL\n              </button>\n              <button\n                className={`px-3 py-1.5 text-sm font-rajdhani font-semibold flex items-center transition-all duration-200 ${\n                  filter === 'income'\n                    ? isDark\n                      ? 'bg-success/20 text-success border-r border-success/30'\n                      : 'bg-success/10 text-success border-r border-success/20'\n                    : isDark\n                    ? 'bg-transparent text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                    : 'bg-transparent text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n                }`}\n                onClick={() => setFilter('income')}\n              >\n                <FiArrowUp className={`mr-1 ${filter === 'income' ? 'text-success' : ''}`} />\n                INCOME\n              </button>\n              <button\n                className={`px-3 py-1.5 text-sm font-rajdhani font-semibold flex items-center transition-all duration-200 ${\n                  filter === 'expense'\n                    ? isDark\n                      ? 'bg-danger/20 text-danger'\n                      : 'bg-danger/10 text-danger'\n                    : isDark\n                    ? 'bg-transparent text-dark-text-secondary hover:bg-dark-accent hover:text-dark-text-primary'\n                    : 'bg-transparent text-light-text-secondary hover:bg-light-accent hover:text-light-text-primary'\n                }`}\n                onClick={() => setFilter('expense')}\n              >\n                <FiArrowDown className={`mr-1 ${filter === 'expense' ? 'text-danger' : ''}`} />\n                EXPENSES\n              </button>\n            </div>\n            \n            <motion.button\n              className=\"flex items-center px-4 py-2 rounded-lg bg-primary hover:bg-primary-hover text-dark-bg font-rajdhani font-semibold letter-spacing-wide transition-all duration-200 shadow-md\"\n              whileHover={{\n                scale: 1.05,\n                boxShadow: \"var(--glow-primary)\"\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddForm(true)}\n            >\n              <FiPlus className=\"mr-2\" />\n              <span className=\"text-sm\">ADD</span>\n            </motion.button>\n          </div>\n        </div>\n        \n        {transactions.length === 0 ? (\n          <div className={`flex flex-col items-center justify-center py-12 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>\n            <FiDollarSign className=\"w-12 h-12 mb-4 opacity-20\" />\n            <p className=\"text-lg font-medium mb-2\">No transactions yet</p>\n            <p className=\"text-sm mb-6\">Start by adding your first transaction</p>\n            <motion.button\n              className={`flex items-center px-4 py-2 rounded-lg ${\n                isDark\n                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'\n                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'\n              }`}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddForm(true)}\n            >\n              <FiPlus className=\"mr-2\" />\n              <span>Add Transaction</span>\n            </motion.button>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr className={`text-left ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>\n                  <th className=\"pb-3 font-medium text-sm\">Date</th>\n                  <th className=\"pb-3 font-medium text-sm\">Description</th>\n                  <th className=\"pb-3 font-medium text-sm\">Category</th>\n                  <th className=\"pb-3 font-medium text-sm text-right\">Amount</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredTransactions.map((transaction) => (\n                  <motion.tr\n                    key={transaction.id}\n                    className={`border-t ${isDark ? 'border-gray-800' : 'border-gray-200'}`}\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <td className={`py-4 text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>\n                      {formatDate(transaction.date)}\n                    </td>\n                    <td className={`py-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>\n                      {transaction.description}\n                    </td>\n                    <td className=\"py-4\">\n                      <div className=\"flex items-center\">\n                        <div\n                          className=\"w-3 h-3 rounded-full mr-2\"\n                          style={{ backgroundColor: getCategoryColor(transaction.category_id) }}\n                        ></div>\n                        <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>\n                          {getCategoryName(transaction.category_id)}\n                        </span>\n                      </div>\n                    </td>\n                    <td className={`py-4 text-right font-medium ${\n                      transaction.is_income\n                        ? 'text-green-500 dark:text-green-400'\n                        : 'text-red-500 dark:text-red-400'\n                    }`}>\n                      {transaction.is_income ? '+' : '-'}${transaction.amount.toLocaleString()}\n                    </td>\n                  </motion.tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n      \n      <AnimatePresence>\n        {showAddForm && (\n          <AddTransactionForm\n            categories={categories}\n            onAddTransaction={handleAddTransaction}\n            onClose={() => setShowAddForm(false)}\n          />\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AAee,SAAS,oBAAoB,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAA4B;;IACpH,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IAEzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,6BAA6B;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,gCAAgC;YAChC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,UAAU,SAAS,EAAE,IAAI;YAC3B;YAEA,gBAAgB;YAChB,IAAI,WAAW,OAAO;gBACpB,wBAAwB;YAC1B,OAAO,IAAI,WAAW,UAAU;gBAC9B,wBAAwB,aAAa,MAAM;qDAAC,CAAA,IAAK,EAAE,SAAS;;YAC9D,OAAO,IAAI,WAAW,WAAW;gBAC/B,wBAAwB,aAAa,MAAM;qDAAC,CAAA,IAAK,CAAC,EAAE,SAAS;;YAC/D;QACF;wCAAG;QAAC;QAAc;KAAO;IAEzB,MAAM,uBAAuB,OAAO;QAOlC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ,YAAY,MAAM;oBAC1B,aAAa,YAAY,WAAW;oBACpC,UAAU,YAAY,QAAQ;oBAC9B,MAAM,YAAY,IAAI;oBACtB,MAAM,YAAY,IAAI;gBACxB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,0CAA0C;YAC1C;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,OAAO,WAAW,SAAS,IAAI,GAAG;IACpC;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,OAAO,WAAW,SAAS,KAAK,GAAG;IACrC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,+BAA+B,EAC9C,SAAS,oCAAoC,oCAC9C,2CAA2C,CAAC;;0BAC3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAW,CAAC,oDAAoD,EAClE,SAAS,2BAA2B,2BACpC;kDAAE;;;;;;kDAGJ,6LAAC;wCAAE,WAAW,CAAC,2BAA2B,EACxC,SAAS,6BAA6B,6BACtC;kDACC,aAAa,MAAM,GAAG,IACnB,CAAC,QAAQ,EAAE,qBAAqB,MAAM,CAAC,IAAI,EAAE,aAAa,MAAM,CAAC,aAAa,CAAC,GAC/E;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,uCAAuC,EACtD,SAAS,uBAAuB,uBAChC;;0DACA,6LAAC;gDACC,WAAW,CAAC,4EAA4E,EACtF,WAAW,QACP,SACE,0DACA,0DACF,SACA,8FACA,gGACJ;gDACF,SAAS,IAAM,UAAU;0DAC1B;;;;;;0DAGD,6LAAC;gDACC,WAAW,CAAC,8FAA8F,EACxG,WAAW,WACP,SACE,0DACA,0DACF,SACA,8FACA,gGACJ;gDACF,SAAS,IAAM,UAAU;;kEAEzB,6LAAC,iJAAA,CAAA,YAAS;wDAAC,WAAW,CAAC,KAAK,EAAE,WAAW,WAAW,iBAAiB,IAAI;;;;;;oDAAI;;;;;;;0DAG/E,6LAAC;gDACC,WAAW,CAAC,8FAA8F,EACxG,WAAW,YACP,SACE,6BACA,6BACF,SACA,8FACA,gGACJ;gDACF,SAAS,IAAM,UAAU;;kEAEzB,6LAAC,iJAAA,CAAA,cAAW;wDAAC,WAAW,CAAC,KAAK,EAAE,WAAW,YAAY,gBAAgB,IAAI;;;;;;oDAAI;;;;;;;;;;;;;kDAKnF,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CACV,OAAO;4CACP,WAAW;wCACb;wCACA,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,eAAe;;0DAE9B,6LAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;oBAK/B,aAAa,MAAM,KAAK,kBACvB,6LAAC;wBAAI,WAAW,CAAC,gDAAgD,EAAE,SAAS,kBAAkB,iBAAiB;;0CAC7G,6LAAC,iJAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAe;;;;;;0CAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAW,CAAC,uCAAuC,EACjD,SACI,iDACA,gDACJ;gCACF,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,eAAe;;kDAE9B,6LAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;6CAIV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;8CACC,cAAA,6LAAC;wCAAG,WAAW,CAAC,UAAU,EAAE,SAAS,kBAAkB,iBAAiB;;0DACtE,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;;;;;;8CAGxD,6LAAC;8CACE,qBAAqB,GAAG,CAAC,CAAC,4BACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,WAAW,CAAC,SAAS,EAAE,SAAS,oBAAoB,mBAAmB;4CACvE,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,6LAAC;oDAAG,WAAW,CAAC,aAAa,EAAE,SAAS,kBAAkB,iBAAiB;8DACxE,WAAW,YAAY,IAAI;;;;;;8DAE9B,6LAAC;oDAAG,WAAW,CAAC,KAAK,EAAE,SAAS,eAAe,iBAAiB;8DAC7D,YAAY,WAAW;;;;;;8DAE1B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,iBAAiB,YAAY,WAAW;gEAAE;;;;;;0EAEtE,6LAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,SAAS,kBAAkB,iBAAiB;0EACrE,gBAAgB,YAAY,WAAW;;;;;;;;;;;;;;;;;8DAI9C,6LAAC;oDAAG,WAAW,CAAC,4BAA4B,EAC1C,YAAY,SAAS,GACjB,uCACA,kCACJ;;wDACC,YAAY,SAAS,GAAG,MAAM;wDAAI;wDAAE,YAAY,MAAM,CAAC,cAAc;;;;;;;;2CA5BnE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsCjC,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,2JAAA,CAAA,UAAkB;oBACjB,YAAY;oBACZ,kBAAkB;oBAClB,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;;AAM1C;GAlQwB;;QACJ,kIAAA,CAAA,WAAQ;;;KADJ", "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/eventBus.ts"], "sourcesContent": ["'use client';\n\n/**\n * Simple event bus for cross-component communication\n * This allows different parts of the application to communicate\n * without direct dependencies\n */\n\nexport type EventType = \n  | 'transaction_created'\n  | 'transaction_updated'\n  | 'transaction_deleted'\n  | 'transactions_changed';\n\nexport interface EventData {\n  userId?: string;\n  transactionId?: string;\n  [key: string]: any;\n}\n\n// Custom event name for our application\nconst EVENT_NAME = 'cashminder_event';\n\n/**\n * Emit an event to notify other components about a change\n */\nexport function emitEvent(type: EventType, data: EventData = {}) {\n  // Create a custom event with our data\n  const event = new CustomEvent(EVENT_NAME, {\n    detail: { type, data, timestamp: new Date().toISOString() }\n  });\n  \n  // Dispatch the event on the window object\n  window.dispatchEvent(event);\n  \n  console.log(`Event emitted: ${type}`, data);\n}\n\n/**\n * Listen for events of a specific type\n */\nexport function listenEvent(type: EventType, callback: (data: EventData) => void) {\n  const handler = (event: Event) => {\n    const customEvent = event as CustomEvent;\n    if (customEvent.detail && customEvent.detail.type === type) {\n      callback(customEvent.detail.data);\n    }\n  };\n  \n  // Add event listener\n  window.addEventListener(EVENT_NAME, handler);\n  \n  // Return a function to remove the listener\n  return () => {\n    window.removeEventListener(EVENT_NAME, handler);\n  };\n}\n\n/**\n * Helper function to refresh transactions for a user\n * This will emit an event that all transaction-dependent components should listen for\n */\nexport function refreshTransactions(userId: string) {\n  emitEvent('transactions_changed', { userId });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAoBA,wCAAwC;AACxC,MAAM,aAAa;AAKZ,SAAS,UAAU,IAAe,EAAE,OAAkB,CAAC,CAAC;IAC7D,sCAAsC;IACtC,MAAM,QAAQ,IAAI,YAAY,YAAY;QACxC,QAAQ;YAAE;YAAM;YAAM,WAAW,IAAI,OAAO,WAAW;QAAG;IAC5D;IAEA,0CAA0C;IAC1C,OAAO,aAAa,CAAC;IAErB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,EAAE;AACxC;AAKO,SAAS,YAAY,IAAe,EAAE,QAAmC;IAC9E,MAAM,UAAU,CAAC;QACf,MAAM,cAAc;QACpB,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,IAAI,KAAK,MAAM;YAC1D,SAAS,YAAY,MAAM,CAAC,IAAI;QAClC;IACF;IAEA,qBAAqB;IACrB,OAAO,gBAAgB,CAAC,YAAY;IAEpC,2CAA2C;IAC3C,OAAO;QACL,OAAO,mBAAmB,CAAC,YAAY;IACzC;AACF;AAMO,SAAS,oBAAoB,MAAc;IAChD,UAAU,wBAAwB;QAAE;IAAO;AAC7C", "debugId": null}}, {"offset": {"line": 2818, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport DashboardSummary from '@/components/dashboard/DashboardSummary';\nimport ExpenseChart from '@/components/dashboard/ExpenseChart';\nimport RecentTransactions from '@/components/dashboard/RecentTransactions';\nimport BudgetProgress from '@/components/dashboard/BudgetProgress';\nimport IncomeExpenseChart from '@/components/dashboard/IncomeExpenseChart';\nimport TransactionsManager from '@/components/dashboard/TransactionsManager';\nimport { useEffect, useState } from 'react';\nimport { Category, Transaction } from '@/lib/types';\nimport { generateRandomColor } from '@/lib/utils';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { listenEvent } from '@/lib/eventBus';\n\n// Default categories for new users\nconst defaultCategories: Category[] = [\n  { id: '1', name: 'Housing', color: '#4F46E5', is_income: false, is_default: true },\n  { id: '2', name: 'Food', color: '#10B981', is_income: false, is_default: true },\n  { id: '3', name: 'Transportation', color: '#F59E0B', is_income: false, is_default: true },\n  { id: '4', name: 'Entertainment', color: '#EC4899', is_income: false, is_default: true },\n  { id: '5', name: 'Utilities', color: '#6366F1', is_income: false, is_default: true },\n  { id: '6', name: 'Salary', color: '#34D399', is_income: true, is_default: true },\n];\n\nexport default function DashboardPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [categories, setCategories] = useState<Category[]>(defaultCategories);\n  const [dashboardData, setDashboardData] = useState({\n    totalBalance: 0,\n    income: 0,\n    expenses: 0,\n    savingsGoal: {\n      current: 0,\n      target: 10000,\n      percentage: 0\n    }\n  });\n\n  const fetchDashboardData = async (userId: string) => {\n    try {\n      setIsLoading(true);\n      console.log('Fetching dashboard data for user:', userId);\n\n      // Load transactions from localStorage\n      const storedTransactions = localStorage.getItem(`cashminder_transactions_${userId}`);\n      let userTransactions: Transaction[] = [];\n\n      if (storedTransactions) {\n        userTransactions = JSON.parse(storedTransactions);\n        console.log('Loaded transactions from localStorage:', userTransactions.length);\n        setTransactions(userTransactions);\n      } else {\n        console.log('No transactions found in localStorage');\n        setTransactions([]);\n      }\n\n      // Calculate dashboard data from transactions\n      const income = userTransactions\n        .filter(t => t.is_income)\n        .reduce((sum, t) => sum + t.amount, 0);\n\n      const expenses = userTransactions\n        .filter(t => !t.is_income)\n        .reduce((sum, t) => sum + t.amount, 0);\n\n      const totalBalance = income - expenses;\n\n      // Set dashboard data\n      setDashboardData({\n        totalBalance,\n        income,\n        expenses,\n        savingsGoal: {\n          current: 0,\n          target: 5000,\n          percentage: 0\n        }\n      });\n\n      console.log('Dashboard data calculated:', { totalBalance, income, expenses });\n\n    } catch (error) {\n      console.error('Error in dashboard data fetching process:', error);\n      // Initialize with empty data\n      setTransactions([]);\n      setDashboardData({\n        totalBalance: 0,\n        income: 0,\n        expenses: 0,\n        savingsGoal: {\n          current: 0,\n          target: 5000,\n          percentage: 0\n        }\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle transaction added event\n  const handleTransactionAdded = () => {\n    // Refresh dashboard data\n    const userJson = localStorage.getItem('cashminder_user');\n    if (userJson) {\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n      fetchDashboardData(userId);\n    }\n  };\n\n  useEffect(() => {\n    // Check if user is logged in using localStorage\n    const userJson = localStorage.getItem('cashminder_user');\n\n    if (!userJson) {\n      router.push('/auth');\n      return;\n    }\n\n    try {\n      // Parse user data\n      const userData = JSON.parse(userJson);\n      const userId = userData.id || 'default';\n\n      // Fetch dashboard data\n      fetchDashboardData(userId);\n    } catch (error) {\n      console.error('Error loading user data:', error);\n      setIsLoading(false);\n      setTransactions([]);\n    }\n  }, [router]);\n\n  // Listen for transaction changes\n  useEffect(() => {\n    // Get user ID\n    const userJson = localStorage.getItem('cashminder_user');\n    if (!userJson) return;\n\n    const userData = JSON.parse(userJson);\n    const userId = userData.id || 'default';\n\n    // Set up event listeners for all transaction events\n    const removeCreatedListener = listenEvent('transaction_created', (data) => {\n      if (data.userId === userId) {\n        console.log('Dashboard detected new transaction:', data);\n        fetchDashboardData(userId);\n      }\n    });\n\n    const removeUpdatedListener = listenEvent('transaction_updated', (data) => {\n      if (data.userId === userId) {\n        console.log('Dashboard detected updated transaction:', data);\n        fetchDashboardData(userId);\n      }\n    });\n\n    const removeDeletedListener = listenEvent('transaction_deleted', (data) => {\n      if (data.userId === userId) {\n        console.log('Dashboard detected deleted transaction:', data);\n        fetchDashboardData(userId);\n      }\n    });\n\n    const removeChangedListener = listenEvent('transactions_changed', (data) => {\n      if (data.userId === userId) {\n        console.log('Dashboard detected transactions changed');\n        fetchDashboardData(userId);\n      }\n    });\n\n    // Clean up event listeners on unmount\n    return () => {\n      removeCreatedListener();\n      removeUpdatedListener();\n      removeDeletedListener();\n      removeChangedListener();\n    };\n  }, []);\n\n  // Calculate summary data for current period (this month)\n  const currentDate = new Date();\n  const currentMonth = currentDate.getMonth();\n  const currentYear = currentDate.getFullYear();\n\n  const currentPeriodTransactions = transactions.filter(t => {\n    const transactionDate = new Date(t.date);\n    return transactionDate.getMonth() === currentMonth &&\n           transactionDate.getFullYear() === currentYear;\n  });\n\n  const previousPeriodTransactions = transactions.filter(t => {\n    const transactionDate = new Date(t.date);\n    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;\n    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;\n    return transactionDate.getMonth() === previousMonth &&\n           transactionDate.getFullYear() === previousYear;\n  });\n\n  // Current period totals\n  const totalIncome = currentPeriodTransactions\n    .filter(t => t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n\n  const totalExpenses = currentPeriodTransactions\n    .filter(t => !t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n\n  const netSavings = totalIncome - totalExpenses;\n\n  // Previous period totals for comparison\n  const previousIncome = previousPeriodTransactions\n    .filter(t => t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n\n  const previousExpenses = previousPeriodTransactions\n    .filter(t => !t.is_income)\n    .reduce((sum, t) => sum + t.amount, 0);\n\n  // Calculate expense breakdown by category\n  const expensesByCategory = categories\n    .filter(c => !c.is_income)\n    .map(category => {\n      const amount = transactions\n        .filter(t => t.category_id === category.id && !t.is_income)\n        .reduce((sum, t) => sum + t.amount, 0);\n\n      return {\n        category,\n        amount,\n      };\n    })\n    .filter(item => item.amount > 0);\n\n  // Calculate budget progress\n  const budgets = categories\n    .filter(c => !c.is_income)\n    .map(category => {\n      const spent = transactions\n        .filter(t => t.category_id === category.id && !t.is_income)\n        .reduce((sum, t) => sum + t.amount, 0);\n\n      // Mock budget amounts\n      const budgetAmount = category.id === '1' ? 1000 :\n                          category.id === '2' ? 300 :\n                          category.id === '3' ? 200 :\n                          category.id === '4' ? 100 :\n                          category.id === '5' ? 150 : 100;\n\n      return {\n        category,\n        budgeted: budgetAmount,\n        spent,\n        remaining: Math.max(0, budgetAmount - spent),\n      };\n    });\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-xl text-light-text-secondary dark:text-dark-text-secondary\">\n          <svg className=\"animate-spin -ml-1 mr-3 h-8 w-8 text-primary inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading dashboard...\n        </div>\n      </div>\n    );\n  }\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className=\"container mx-auto px-4 py-8 max-w-7xl\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <motion.div\n        className=\"pb-5 border-b border-light-border dark:border-dark-border mb-8\"\n        variants={itemVariants}\n      >\n        <h1 className=\"text-3xl font-bold leading-tight text-light-text-primary dark:text-dark-text-primary\">Dashboard</h1>\n        <p className=\"mt-1 text-sm text-light-text-secondary dark:text-dark-text-secondary\">\n          {transactions.length > 0\n            ? 'Welcome back! Here\\'s an overview of your finances.'\n            : 'Welcome to Cashminder! Start by adding transactions to see your financial overview.'}\n        </p>\n      </motion.div>\n\n      <DashboardSummary\n        totalIncome={dashboardData.income}\n        totalExpenses={dashboardData.expenses}\n        netSavings={dashboardData.totalBalance}\n        previousIncome={0}\n        previousExpenses={0}\n      />\n\n      <motion.div\n        className=\"grid grid-cols-1 gap-5 mt-5 lg:grid-cols-2\"\n        variants={itemVariants}\n      >\n        <ExpenseChart categories={expensesByCategory} />\n        <IncomeExpenseChart transactions={transactions} />\n      </motion.div>\n\n      <motion.div\n        className=\"mt-8\"\n        variants={itemVariants}\n      >\n        <BudgetProgress budgets={budgets} />\n      </motion.div>\n\n      <motion.div\n        className=\"mt-8 mb-8\"\n        variants={itemVariants}\n      >\n        <TransactionsManager\n          transactions={transactions}\n          categories={categories}\n          onTransactionAdded={handleTransactionAdded}\n        />\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;;;AAbA;;;;;;;;;;AAeA,mCAAmC;AACnC,MAAM,oBAAgC;IACpC;QAAE,IAAI;QAAK,MAAM;QAAW,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACjF;QAAE,IAAI;QAAK,MAAM;QAAQ,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IAC9E;QAAE,IAAI;QAAK,MAAM;QAAkB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACxF;QAAE,IAAI;QAAK,MAAM;QAAiB,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACvF;QAAE,IAAI;QAAK,MAAM;QAAa,OAAO;QAAW,WAAW;QAAO,YAAY;IAAK;IACnF;QAAE,IAAI;QAAK,MAAM;QAAU,OAAO;QAAW,WAAW;QAAM,YAAY;IAAK;CAChF;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,cAAc;QACd,QAAQ;QACR,UAAU;QACV,aAAa;YACX,SAAS;YACT,QAAQ;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,aAAa;YACb,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,sCAAsC;YACtC,MAAM,qBAAqB,aAAa,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ;YACnF,IAAI,mBAAkC,EAAE;YAExC,IAAI,oBAAoB;gBACtB,mBAAmB,KAAK,KAAK,CAAC;gBAC9B,QAAQ,GAAG,CAAC,0CAA0C,iBAAiB,MAAM;gBAC7E,gBAAgB;YAClB,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,gBAAgB,EAAE;YACpB;YAEA,6CAA6C;YAC7C,MAAM,SAAS,iBACZ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAEtC,MAAM,WAAW,iBACd,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAEtC,MAAM,eAAe,SAAS;YAE9B,qBAAqB;YACrB,iBAAiB;gBACf;gBACA;gBACA;gBACA,aAAa;oBACX,SAAS;oBACT,QAAQ;oBACR,YAAY;gBACd;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBAAE;gBAAc;gBAAQ;YAAS;QAE7E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,6BAA6B;YAC7B,gBAAgB,EAAE;YAClB,iBAAiB;gBACf,cAAc;gBACd,QAAQ;gBACR,UAAU;gBACV,aAAa;oBACX,SAAS;oBACT,QAAQ;oBACR,YAAY;gBACd;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,yBAAyB;QACzB,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,UAAU;YACZ,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAC9B,mBAAmB;QACrB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,gDAAgD;YAChD,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,CAAC,UAAU;gBACb,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,kBAAkB;gBAClB,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;gBAE9B,uBAAuB;gBACvB,mBAAmB;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,aAAa;gBACb,gBAAgB,EAAE;YACpB;QACF;kCAAG;QAAC;KAAO;IAEX,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,cAAc;YACd,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,MAAM,SAAS,SAAS,EAAE,IAAI;YAE9B,oDAAoD;YACpD,MAAM,wBAAwB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;iEAAuB,CAAC;oBAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;wBAC1B,QAAQ,GAAG,CAAC,uCAAuC;wBACnD,mBAAmB;oBACrB;gBACF;;YAEA,MAAM,wBAAwB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;iEAAuB,CAAC;oBAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;wBAC1B,QAAQ,GAAG,CAAC,2CAA2C;wBACvD,mBAAmB;oBACrB;gBACF;;YAEA,MAAM,wBAAwB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;iEAAuB,CAAC;oBAChE,IAAI,KAAK,MAAM,KAAK,QAAQ;wBAC1B,QAAQ,GAAG,CAAC,2CAA2C;wBACvD,mBAAmB;oBACrB;gBACF;;YAEA,MAAM,wBAAwB,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;iEAAwB,CAAC;oBACjE,IAAI,KAAK,MAAM,KAAK,QAAQ;wBAC1B,QAAQ,GAAG,CAAC;wBACZ,mBAAmB;oBACrB;gBACF;;YAEA,sCAAsC;YACtC;2CAAO;oBACL;oBACA;oBACA;oBACA;gBACF;;QACF;kCAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,cAAc,IAAI;IACxB,MAAM,eAAe,YAAY,QAAQ;IACzC,MAAM,cAAc,YAAY,WAAW;IAE3C,MAAM,4BAA4B,aAAa,MAAM,CAAC,CAAA;QACpD,MAAM,kBAAkB,IAAI,KAAK,EAAE,IAAI;QACvC,OAAO,gBAAgB,QAAQ,OAAO,gBAC/B,gBAAgB,WAAW,OAAO;IAC3C;IAEA,MAAM,6BAA6B,aAAa,MAAM,CAAC,CAAA;QACrD,MAAM,kBAAkB,IAAI,KAAK,EAAE,IAAI;QACvC,MAAM,gBAAgB,iBAAiB,IAAI,KAAK,eAAe;QAC/D,MAAM,eAAe,iBAAiB,IAAI,cAAc,IAAI;QAC5D,OAAO,gBAAgB,QAAQ,OAAO,iBAC/B,gBAAgB,WAAW,OAAO;IAC3C;IAEA,wBAAwB;IACxB,MAAM,cAAc,0BACjB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,gBAAgB,0BACnB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,aAAa,cAAc;IAEjC,wCAAwC;IACxC,MAAM,iBAAiB,2BACpB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,MAAM,mBAAmB,2BACtB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEtC,0CAA0C;IAC1C,MAAM,qBAAqB,WACxB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,GAAG,CAAC,CAAA;QACH,MAAM,SAAS,aACZ,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,SAAS,EAAE,IAAI,CAAC,EAAE,SAAS,EACzD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,OAAO;YACL;YACA;QACF;IACF,GACC,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAEhC,4BAA4B;IAC5B,MAAM,UAAU,WACb,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EACxB,GAAG,CAAC,CAAA;QACH,MAAM,QAAQ,aACX,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,SAAS,EAAE,IAAI,CAAC,EAAE,SAAS,EACzD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,sBAAsB;QACtB,MAAM,eAAe,SAAS,EAAE,KAAK,MAAM,OACvB,SAAS,EAAE,KAAK,MAAM,MACtB,SAAS,EAAE,KAAK,MAAM,MACtB,SAAS,EAAE,KAAK,MAAM,MACtB,SAAS,EAAE,KAAK,MAAM,MAAM;QAEhD,OAAO;YACL;YACA,UAAU;YACV;YACA,WAAW,KAAK,GAAG,CAAC,GAAG,eAAe;QACxC;IACF;IAEF,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA4D,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CAChI,6LAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,6LAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCAAuF;;;;;;kCACrG,6LAAC;wBAAE,WAAU;kCACV,aAAa,MAAM,GAAG,IACnB,wDACA;;;;;;;;;;;;0BAIR,6LAAC,sJAAA,CAAA,UAAgB;gBACf,aAAa,cAAc,MAAM;gBACjC,eAAe,cAAc,QAAQ;gBACrC,YAAY,cAAc,YAAY;gBACtC,gBAAgB;gBAChB,kBAAkB;;;;;;0BAGpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC,kJAAA,CAAA,UAAY;wBAAC,YAAY;;;;;;kCAC1B,6LAAC,wJAAA,CAAA,UAAkB;wBAAC,cAAc;;;;;;;;;;;;0BAGpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAEV,cAAA,6LAAC,oJAAA,CAAA,UAAc;oBAAC,SAAS;;;;;;;;;;;0BAG3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAEV,cAAA,6LAAC,yJAAA,CAAA,UAAmB;oBAClB,cAAc;oBACd,YAAY;oBACZ,oBAAoB;;;;;;;;;;;;;;;;;AAK9B;GAxUwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}