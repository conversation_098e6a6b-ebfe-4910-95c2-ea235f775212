{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/components/ui/YesNoToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface YesNoToggleProps {\n  isEnabled: boolean;\n  onToggle: () => void;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport default function YesNoToggle({\n  isEnabled,\n  onToggle,\n  size = 'md'\n}: YesNoToggleProps) {\n  // Size configurations\n  const sizeConfig = {\n    sm: {\n      text: 'text-xs',\n      padding: 'px-2 py-0.5',\n      width: 'w-16'\n    },\n    md: {\n      text: 'text-sm',\n      padding: 'px-3 py-1',\n      width: 'w-20'\n    },\n    lg: {\n      text: 'text-base',\n      padding: 'px-4 py-1.5',\n      width: 'w-24'\n    }\n  };\n\n  const config = sizeConfig[size];\n\n  return (\n    <div className={`flex ${config.width} rounded-md overflow-hidden`}>\n      <motion.button\n        onClick={isEnabled ? onToggle : undefined}\n        className={`flex-1 ${config.padding} ${config.text} font-medium transition-colors ${\n          isEnabled \n            ? 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400' \n            : 'bg-error-light dark:bg-error-dark text-white'\n        }`}\n        whileHover={isEnabled ? { backgroundColor: '#e5e7eb' } : {}}\n        whileTap={isEnabled ? { scale: 0.95 } : {}}\n      >\n        No\n      </motion.button>\n      <motion.button\n        onClick={!isEnabled ? onToggle : undefined}\n        className={`flex-1 ${config.padding} ${config.text} font-medium transition-colors ${\n          !isEnabled \n            ? 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400' \n            : 'bg-success-light dark:bg-success-dark text-white'\n        }`}\n        whileHover={!isEnabled ? { backgroundColor: '#e5e7eb' } : {}}\n        whileTap={!isEnabled ? { scale: 0.95 } : {}}\n      >\n        Yes\n      </motion.button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,YAAY,EAClC,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACM;IACjB,sBAAsB;IACtB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,IAAI;YACF,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,IAAI;YACF,MAAM;YACN,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,SAAS,UAAU,CAAC,KAAK;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,2BAA2B,CAAC;;0BAC/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,YAAY,WAAW;gBAChC,WAAW,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,+BAA+B,EAChF,YACI,kEACA,gDACJ;gBACF,YAAY,YAAY;oBAAE,iBAAiB;gBAAU,IAAI,CAAC;gBAC1D,UAAU,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;0BAC1C;;;;;;0BAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,CAAC,YAAY,WAAW;gBACjC,WAAW,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,+BAA+B,EAChF,CAAC,YACG,kEACA,oDACJ;gBACF,YAAY,CAAC,YAAY;oBAAE,iBAAiB;gBAAU,IAAI,CAAC;gBAC3D,UAAU,CAAC,YAAY;oBAAE,OAAO;gBAAK,IAAI,CAAC;0BAC3C;;;;;;;;;;;;AAKP;KAtDwB", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/lib/userSettings.ts"], "sourcesContent": ["'use client';\n\nimport { User } from './types';\n\nexport interface UserSettings {\n  name: string;\n  email: string;\n  profilePicture?: string;\n  currency: string;\n  language: string;\n  dateFormat: string;\n  enableAnimations: boolean;\n  notificationPreferences: {\n    email: boolean;\n    push: boolean;\n    sms: boolean;\n    frequency: 'immediate' | 'daily' | 'weekly';\n    types: {\n      transactionAlerts: boolean;\n      budgetAlerts: boolean;\n      goalReminders: boolean;\n      billReminders: boolean;\n      securityAlerts: boolean;\n      weeklyReports: boolean;\n    };\n  };\n  securitySettings: {\n    twoFactorEnabled: boolean;\n    lastPasswordChange: string;\n  };\n}\n\nexport const defaultUserSettings: UserSettings = {\n  name: '<PERSON>',\n  email: '<EMAIL>',\n  currency: 'USD',\n  language: 'English',\n  dateFormat: 'MM/DD/YYYY',\n  enableAnimations: true,\n  notificationPreferences: {\n    email: true,\n    push: true,\n    sms: false,\n    frequency: 'immediate',\n    types: {\n      transactionAlerts: true,\n      budgetAlerts: true,\n      goalReminders: true,\n      billReminders: true,\n      securityAlerts: true,\n      weeklyReports: false\n    }\n  },\n  securitySettings: {\n    twoFactorEnabled: false,\n    lastPasswordChange: new Date().toISOString()\n  }\n};\n\nexport function getUserSettings(userId: string): UserSettings {\n  try {\n    const storedSettings = localStorage.getItem(`cashminder_settings_${userId}`);\n    if (storedSettings) {\n      // Parse stored settings\n      const parsedSettings = JSON.parse(storedSettings);\n\n      // Migrate settings if needed\n      const migratedSettings = migrateUserSettings(parsedSettings);\n\n      // Always save the migrated settings to ensure consistency\n      saveUserSettings(userId, migratedSettings);\n\n      return migratedSettings;\n    }\n    return defaultUserSettings;\n  } catch (error) {\n    console.error('Error getting user settings:', error);\n    return defaultUserSettings;\n  }\n}\n\n// Helper function to migrate user settings to the latest structure\nfunction migrateUserSettings(settings: any): UserSettings {\n  const migratedSettings = { ...settings };\n\n  // Ensure notificationPreferences exists\n  if (!migratedSettings.notificationPreferences) {\n    migratedSettings.notificationPreferences = defaultUserSettings.notificationPreferences;\n  }\n\n  // Ensure frequency exists\n  if (!migratedSettings.notificationPreferences.frequency) {\n    migratedSettings.notificationPreferences.frequency = defaultUserSettings.notificationPreferences.frequency;\n  }\n\n  // Ensure types exists\n  if (!migratedSettings.notificationPreferences.types) {\n    migratedSettings.notificationPreferences.types = defaultUserSettings.notificationPreferences.types;\n  } else {\n    // Ensure all notification types exist\n    const defaultTypes = defaultUserSettings.notificationPreferences.types;\n    migratedSettings.notificationPreferences.types = {\n      ...defaultTypes,\n      ...migratedSettings.notificationPreferences.types\n    };\n  }\n\n  return migratedSettings as UserSettings;\n}\n\nexport function saveUserSettings(userId: string, settings: UserSettings): void {\n  try {\n    localStorage.setItem(`cashminder_settings_${userId}`, JSON.stringify(settings));\n  } catch (error) {\n    console.error('Error saving user settings:', error);\n  }\n}\n\nexport function updateUserSettings(userId: string, partialSettings: Partial<UserSettings>): UserSettings {\n  const currentSettings = getUserSettings(userId);\n\n  // Create a deep copy of the current settings\n  const updatedSettings = JSON.parse(JSON.stringify(currentSettings));\n\n  // Handle special case for notification preferences to ensure deep merging\n  if (partialSettings.notificationPreferences) {\n    updatedSettings.notificationPreferences = {\n      ...updatedSettings.notificationPreferences,\n      ...partialSettings.notificationPreferences\n    };\n\n    // Handle types separately for deep merging\n    if (partialSettings.notificationPreferences.types) {\n      updatedSettings.notificationPreferences.types = {\n        ...updatedSettings.notificationPreferences.types,\n        ...partialSettings.notificationPreferences.types\n      };\n    }\n\n    // Remove notificationPreferences from partialSettings to prevent overwriting\n    const { notificationPreferences, ...restSettings } = partialSettings;\n\n    // Merge the rest of the settings\n    Object.assign(updatedSettings, restSettings);\n  } else {\n    // Simple merge for other settings\n    Object.assign(updatedSettings, partialSettings);\n  }\n\n  saveUserSettings(userId, updatedSettings);\n  return updatedSettings;\n}\n\nexport function getInitials(name: string): string {\n  if (!name) return '';\n\n  const parts = name.trim().split(' ');\n  if (parts.length === 1) {\n    return parts[0].charAt(0).toUpperCase();\n  }\n\n  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();\n}\n\nexport function getCurrentUser(): { id: string; name: string; email: string } | null {\n  try {\n    const userJson = localStorage.getItem('cashminder_user');\n    if (!userJson) return null;\n\n    const userData = JSON.parse(userJson);\n    return {\n      id: userData.id || 'default',\n      name: userData.name || 'User',\n      email: userData.email || ''\n    };\n  } catch (error) {\n    console.error('Error getting current user:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAgCO,MAAM,sBAAoC;IAC/C,MAAM;IACN,OAAO;IACP,UAAU;IACV,UAAU;IACV,YAAY;IACZ,kBAAkB;IAClB,yBAAyB;QACvB,OAAO;QACP,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;YACL,mBAAmB;YACnB,cAAc;YACd,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,eAAe;QACjB;IACF;IACA,kBAAkB;QAChB,kBAAkB;QAClB,oBAAoB,IAAI,OAAO,WAAW;IAC5C;AACF;AAEO,SAAS,gBAAgB,MAAc;IAC5C,IAAI;QACF,MAAM,iBAAiB,aAAa,OAAO,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC3E,IAAI,gBAAgB;YAClB,wBAAwB;YACxB,MAAM,iBAAiB,KAAK,KAAK,CAAC;YAElC,6BAA6B;YAC7B,MAAM,mBAAmB,oBAAoB;YAE7C,0DAA0D;YAC1D,iBAAiB,QAAQ;YAEzB,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAEA,mEAAmE;AACnE,SAAS,oBAAoB,QAAa;IACxC,MAAM,mBAAmB;QAAE,GAAG,QAAQ;IAAC;IAEvC,wCAAwC;IACxC,IAAI,CAAC,iBAAiB,uBAAuB,EAAE;QAC7C,iBAAiB,uBAAuB,GAAG,oBAAoB,uBAAuB;IACxF;IAEA,0BAA0B;IAC1B,IAAI,CAAC,iBAAiB,uBAAuB,CAAC,SAAS,EAAE;QACvD,iBAAiB,uBAAuB,CAAC,SAAS,GAAG,oBAAoB,uBAAuB,CAAC,SAAS;IAC5G;IAEA,sBAAsB;IACtB,IAAI,CAAC,iBAAiB,uBAAuB,CAAC,KAAK,EAAE;QACnD,iBAAiB,uBAAuB,CAAC,KAAK,GAAG,oBAAoB,uBAAuB,CAAC,KAAK;IACpG,OAAO;QACL,sCAAsC;QACtC,MAAM,eAAe,oBAAoB,uBAAuB,CAAC,KAAK;QACtE,iBAAiB,uBAAuB,CAAC,KAAK,GAAG;YAC/C,GAAG,YAAY;YACf,GAAG,iBAAiB,uBAAuB,CAAC,KAAK;QACnD;IACF;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,MAAc,EAAE,QAAsB;IACrE,IAAI;QACF,aAAa,OAAO,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;IACvE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;AACF;AAEO,SAAS,mBAAmB,MAAc,EAAE,eAAsC;IACvF,MAAM,kBAAkB,gBAAgB;IAExC,6CAA6C;IAC7C,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IAElD,0EAA0E;IAC1E,IAAI,gBAAgB,uBAAuB,EAAE;QAC3C,gBAAgB,uBAAuB,GAAG;YACxC,GAAG,gBAAgB,uBAAuB;YAC1C,GAAG,gBAAgB,uBAAuB;QAC5C;QAEA,2CAA2C;QAC3C,IAAI,gBAAgB,uBAAuB,CAAC,KAAK,EAAE;YACjD,gBAAgB,uBAAuB,CAAC,KAAK,GAAG;gBAC9C,GAAG,gBAAgB,uBAAuB,CAAC,KAAK;gBAChD,GAAG,gBAAgB,uBAAuB,CAAC,KAAK;YAClD;QACF;QAEA,6EAA6E;QAC7E,MAAM,EAAE,uBAAuB,EAAE,GAAG,cAAc,GAAG;QAErD,iCAAiC;QACjC,OAAO,MAAM,CAAC,iBAAiB;IACjC,OAAO;QACL,kCAAkC;QAClC,OAAO,MAAM,CAAC,iBAAiB;IACjC;IAEA,iBAAiB,QAAQ;IACzB,OAAO;AACT;AAEO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IACvC;IAEA,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW;AAC7E;AAEO,SAAS;IACd,IAAI;QACF,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,OAAO;YACL,IAAI,SAAS,EAAE,IAAI;YACnB,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,SAAS,KAAK,IAAI;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/cashminder-app/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useTheme } from '@/context/ThemeContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  FiUser, FiLock, FiBell, FiCreditCard, FiSettings,\n  FiShield, FiGlobe, FiToggleRight, FiCheck, FiX,\n  FiAlertTriangle, FiMail, FiSmartphone, FiDollarSign,\n  FiChevronDown\n} from 'react-icons/fi';\nimport FuturisticThemeToggle from '@/components/ui/FuturisticThemeToggle';\nimport YesNoToggle from '@/components/ui/YesNoToggle';\nimport {\n  UserSettings,\n  defaultUserSettings,\n  getUserSettings,\n  updateUserSettings,\n  getInitials,\n  getCurrentUser\n} from '@/lib/userSettings';\n\nexport default function SettingsPage() {\n  const { theme, toggleTheme } = useTheme();\n  const isDark = theme === 'dark';\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('account');\n  const [userSettings, setUserSettings] = useState<UserSettings>(defaultUserSettings);\n  const [userId, setUserId] = useState<string>('');\n  const [userName, setUserName] = useState<string>('');\n  const [userEmail, setUserEmail] = useState<string>('');\n  const [successMessage, setSuccessMessage] = useState<string>('');\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  // Form state\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [currency, setCurrency] = useState('USD');\n  const [language, setLanguage] = useState('English');\n  const [dateFormat, setDateFormat] = useState('MM/DD/YYYY');\n  const [enableAnimations, setEnableAnimations] = useState(true);\n\n  // Notification preferences\n  const [emailNotifications, setEmailNotifications] = useState(true);\n  const [pushNotifications, setPushNotifications] = useState(true);\n  const [smsNotifications, setSmsNotifications] = useState(false);\n  const [notificationFrequency, setNotificationFrequency] = useState<'immediate' | 'daily' | 'weekly'>('immediate');\n\n  // Notification types\n  const [transactionAlerts, setTransactionAlerts] = useState(true);\n  const [budgetAlerts, setBudgetAlerts] = useState(true);\n  const [goalReminders, setGoalReminders] = useState(true);\n  const [billReminders, setBillReminders] = useState(true);\n  const [securityAlerts, setSecurityAlerts] = useState(true);\n  const [weeklyReports, setWeeklyReports] = useState(false);\n\n  useEffect(() => {\n    // Check if user is logged in\n    const user = getCurrentUser();\n    if (!user) {\n      router.push('/auth');\n      return;\n    }\n\n    setUserId(user.id);\n    setUserName(user.name);\n    setUserEmail(user.email);\n\n    // Load user settings\n    const settings = getUserSettings(user.id);\n    setUserSettings(settings);\n\n    // Initialize form state\n    setName(settings.name);\n    setEmail(settings.email);\n    setCurrency(settings.currency);\n    setLanguage(settings.language);\n    setDateFormat(settings.dateFormat);\n    setEnableAnimations(settings.enableAnimations);\n\n    // Initialize notification preferences with safety checks\n    try {\n      // Notification channels\n      setEmailNotifications(settings.notificationPreferences?.email ?? true);\n      setPushNotifications(settings.notificationPreferences?.push ?? true);\n      setSmsNotifications(settings.notificationPreferences?.sms ?? false);\n      setNotificationFrequency(settings.notificationPreferences?.frequency ?? 'immediate');\n\n      // Notification types\n      const types = settings.notificationPreferences?.types ?? {};\n      setTransactionAlerts(types.transactionAlerts ?? true);\n      setBudgetAlerts(types.budgetAlerts ?? true);\n      setGoalReminders(types.goalReminders ?? true);\n      setBillReminders(types.billReminders ?? true);\n      setSecurityAlerts(types.securityAlerts ?? true);\n      setWeeklyReports(types.weeklyReports ?? false);\n    } catch (error) {\n      console.error('Error initializing notification settings:', error);\n      // Set default values if there's an error\n      setEmailNotifications(true);\n      setPushNotifications(true);\n      setSmsNotifications(false);\n      setNotificationFrequency('immediate');\n      setTransactionAlerts(true);\n      setBudgetAlerts(true);\n      setGoalReminders(true);\n      setBillReminders(true);\n      setSecurityAlerts(true);\n      setWeeklyReports(false);\n    }\n\n    // Finish loading\n    setIsLoading(false);\n  }, [router]);\n\n  // Save account settings\n  const saveAccountSettings = () => {\n    try {\n      // Only save name and currency, email is read-only\n      const updatedSettings = updateUserSettings(userId, {\n        name,\n        currency\n      });\n\n      setUserSettings(updatedSettings);\n      setSuccessMessage('Account settings saved successfully');\n      setTimeout(() => setSuccessMessage(''), 3000);\n    } catch (error) {\n      setErrorMessage('Failed to save account settings');\n      setTimeout(() => setErrorMessage(''), 3000);\n    }\n  };\n\n  // Save preferences\n  const savePreferences = () => {\n    try {\n      const updatedSettings = updateUserSettings(userId, {\n        language,\n        dateFormat,\n        enableAnimations\n      });\n\n      setUserSettings(updatedSettings);\n      setSuccessMessage('Preferences saved successfully');\n      setTimeout(() => setSuccessMessage(''), 3000);\n    } catch (error) {\n      setErrorMessage('Failed to save preferences');\n      setTimeout(() => setErrorMessage(''), 3000);\n    }\n  };\n\n  // Save notification settings\n  const saveNotificationSettings = () => {\n    try {\n      const updatedSettings = updateUserSettings(userId, {\n        notificationPreferences: {\n          email: emailNotifications,\n          push: pushNotifications,\n          sms: smsNotifications,\n          frequency: notificationFrequency,\n          types: {\n            transactionAlerts,\n            budgetAlerts,\n            goalReminders,\n            billReminders,\n            securityAlerts,\n            weeklyReports\n          }\n        }\n      });\n\n      setUserSettings(updatedSettings);\n      setSuccessMessage('Notification settings saved successfully');\n      setTimeout(() => setSuccessMessage(''), 3000);\n    } catch (error) {\n      setErrorMessage('Failed to save notification settings');\n      setTimeout(() => setErrorMessage(''), 3000);\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  // Settings tabs\n  const tabs = [\n    { id: 'account', label: 'Account', icon: <FiUser /> },\n    { id: 'security', label: 'Security', icon: <FiLock /> },\n    { id: 'notifications', label: 'Notifications', icon: <FiBell /> },\n    { id: 'payment', label: 'Payment Methods', icon: <FiCreditCard /> },\n    { id: 'preferences', label: 'Preferences', icon: <FiSettings /> },\n    { id: 'privacy', label: 'Privacy', icon: <FiShield /> },\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className={`text-xl ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n          <svg className=\"animate-spin -ml-1 mr-3 h-8 w-8 text-primary inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading settings...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div\n      className=\"container mx-auto px-4 py-8 max-w-7xl\"\n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={containerVariants}\n    >\n      {/* Success message */}\n      {successMessage && (\n        <motion.div\n          className=\"fixed top-4 right-4 z-50 bg-success-light dark:bg-success-dark text-white px-4 py-2 rounded-lg shadow-lg flex items-center\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n        >\n          <FiCheck className=\"mr-2\" />\n          {successMessage}\n        </motion.div>\n      )}\n\n      {/* Error message */}\n      {errorMessage && (\n        <motion.div\n          className=\"fixed top-4 right-4 z-50 bg-error-light dark:bg-error-dark text-white px-4 py-2 rounded-lg shadow-lg flex items-center\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n        >\n          <FiX className=\"mr-2\" />\n          {errorMessage}\n        </motion.div>\n      )}\n\n      <motion.div\n        className=\"pb-5 border-b border-light-border dark:border-dark-border mb-8\"\n        variants={itemVariants}\n      >\n        <h1 className={`text-3xl font-bold leading-tight ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n          Settings\n        </h1>\n        <p className={`mt-1 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n          Manage your account settings and preferences\n        </p>\n      </motion.div>\n\n      <div className=\"flex flex-col md:flex-row gap-8\">\n        {/* Settings navigation */}\n        <motion.div\n          className=\"md:w-1/4\"\n          variants={itemVariants}\n        >\n          <nav className={`space-y-1 sticky top-20 ${\n            isDark\n              ? 'bg-dark-surface border border-dark-border'\n              : 'bg-light-surface border border-light-border'\n          } rounded-xl p-4`}>\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`w-full flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${\n                  activeTab === tab.id\n                    ? `${isDark\n                        ? 'bg-primary/30 border border-primary/50 text-dark-text-primary'\n                        : 'bg-primary/10 border border-primary/20 text-primary'}`\n                    : `${isDark\n                        ? 'text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-border/50'\n                        : 'text-light-text-secondary hover:text-light-text-primary hover:bg-light-border/50'}`\n                }`}\n              >\n                <span className=\"mr-3\">{tab.icon}</span>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </motion.div>\n\n        {/* Settings content */}\n        <motion.div\n          className=\"md:w-3/4\"\n          variants={itemVariants}\n        >\n          <div className={`rounded-xl border ${\n            isDark\n              ? 'bg-dark-surface border-dark-border'\n              : 'bg-light-surface border-light-border shadow-sm'\n          } p-6`}>\n            {/* Account Settings */}\n            {activeTab === 'account' && (\n              <div className=\"space-y-6\">\n                <h2 className={`text-xl font-semibold ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                  Account Settings\n                </h2>\n\n                <div className=\"space-y-4\">\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                      Profile Picture\n                    </label>\n                    <div className=\"flex items-center\">\n                      <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${\n                        isDark ? 'from-primary to-secondary' : 'from-primary to-secondary'\n                      } flex items-center justify-center text-white text-2xl font-bold`}>\n                        {getInitials(name)}\n                      </div>\n                      <div className=\"ml-5\">\n                        <button className={`px-3 py-2 rounded-lg text-sm font-medium ${\n                          isDark\n                            ? 'bg-dark-border hover:bg-dark-border/70 text-dark-text-primary'\n                            : 'bg-light-border hover:bg-light-border/70 text-light-text-primary'\n                        }`}>\n                          Change\n                        </button>\n                        <button className={`ml-3 px-3 py-2 rounded-lg text-sm font-medium ${\n                          isDark\n                            ? 'bg-transparent hover:bg-dark-border/30 text-dark-text-secondary'\n                            : 'bg-transparent hover:bg-light-border/30 text-light-text-secondary'\n                        }`}>\n                          Remove\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                      Name\n                    </label>\n                    <div className=\"relative\">\n                      <input\n                        type=\"text\"\n                        className={`w-full px-3 py-2 rounded-lg border ${\n                          isDark\n                            ? 'bg-dark-accent border-dark-border text-dark-text-primary'\n                            : 'bg-light-accent border-light-border text-light-text-primary'\n                        } focus:ring-primary focus:border-primary`}\n                        value={name}\n                        onChange={(e) => setName(e.target.value)}\n                        placeholder=\"Enter your name\"\n                      />\n                      <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                        <FiUser className={`h-4 w-4 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`} />\n                      </div>\n                    </div>\n                    <p className={`mt-1 text-xs ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                      This name will be displayed throughout the app\n                    </p>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                      Email\n                    </label>\n                    <div className=\"relative\">\n                      <input\n                        type=\"email\"\n                        className={`w-full px-3 py-2 rounded-lg border ${\n                          isDark\n                            ? 'bg-dark-bg border-dark-border text-dark-text-secondary'\n                            : 'bg-light-bg border-light-border text-light-text-secondary'\n                        } cursor-not-allowed`}\n                        value={email}\n                        readOnly\n                      />\n                      <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                        <FiLock className={`h-4 w-4 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`} />\n                      </div>\n                    </div>\n                    <p className={`mt-1 text-xs ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                      Email address cannot be changed\n                    </p>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`}>\n                      Currency\n                    </label>\n                    <div className=\"relative\">\n                      <select\n                        className={`w-full px-3 py-2 pl-10 rounded-lg border appearance-none ${\n                          isDark\n                            ? 'bg-dark-accent border-dark-border text-dark-text-primary'\n                            : 'bg-light-accent border-light-border text-light-text-primary'\n                        } focus:ring-primary focus:border-primary`}\n                        value={currency}\n                        onChange={(e) => setCurrency(e.target.value)}\n                        style={{\n                          colorScheme: isDark ? 'dark' : 'light'\n                        }}\n                      >\n                        <option value=\"USD\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>USD - US Dollar</option>\n                        <option value=\"EUR\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>EUR - Euro</option>\n                        <option value=\"GBP\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>GBP - British Pound</option>\n                        <option value=\"JPY\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>JPY - Japanese Yen</option>\n                        <option value=\"CAD\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>CAD - Canadian Dollar</option>\n                        <option value=\"AUD\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>AUD - Australian Dollar</option>\n                        <option value=\"INR\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>INR - Indian Rupee</option>\n                      </select>\n                      <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                        <FiDollarSign className={`h-4 w-4 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`} />\n                      </div>\n                      <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                        <FiChevronDown className={`h-4 w-4 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`} />\n                      </div>\n                    </div>\n                    <p className={`mt-1 text-xs ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                      All amounts will be displayed in this currency\n                    </p>\n                  </div>\n\n                  <div className=\"flex justify-end\">\n                    <button\n                      onClick={saveAccountSettings}\n                      className={`px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r ${\n                        isDark\n                          ? 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                          : 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                      } text-white shadow-md transition-all duration-200`}\n                    >\n                      Save Changes\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Preferences Settings */}\n            {activeTab === 'preferences' && (\n              <div className=\"space-y-6\">\n                <h2 className={`text-xl font-semibold ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                  Preferences\n                </h2>\n\n                <div className=\"space-y-4\">\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                          Theme\n                        </h3>\n                        <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                          Choose between light and dark mode\n                        </p>\n                      </div>\n                      <FuturisticThemeToggle />\n                    </div>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                          Language\n                        </h3>\n                        <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                          Select your preferred language\n                        </p>\n                      </div>\n                      <select\n                        className={`px-3 py-2 rounded-lg border ${\n                          isDark\n                            ? 'bg-dark-accent border-dark-border text-dark-text-primary'\n                            : 'bg-light-accent border-light-border text-light-text-primary'\n                        }`}\n                        value={language}\n                        onChange={(e) => setLanguage(e.target.value)}\n                        style={{\n                          colorScheme: isDark ? 'dark' : 'light'\n                        }}\n                      >\n                        <option value=\"English\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>English</option>\n                        <option value=\"Spanish\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>Spanish</option>\n                        <option value=\"French\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>French</option>\n                        <option value=\"German\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>German</option>\n                        <option value=\"Japanese\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>Japanese</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                          Date Format\n                        </h3>\n                        <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                          Choose how dates are displayed\n                        </p>\n                      </div>\n                      <select\n                        className={`px-3 py-2 rounded-lg border ${\n                          isDark\n                            ? 'bg-dark-accent border-dark-border text-dark-text-primary'\n                            : 'bg-light-accent border-light-border text-light-text-primary'\n                        }`}\n                        value={dateFormat}\n                        onChange={(e) => setDateFormat(e.target.value)}\n                        style={{\n                          colorScheme: isDark ? 'dark' : 'light'\n                        }}\n                      >\n                        <option value=\"MM/DD/YYYY\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>MM/DD/YYYY</option>\n                        <option value=\"DD/MM/YYYY\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>DD/MM/YYYY</option>\n                        <option value=\"YYYY-MM-DD\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>YYYY-MM-DD</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className={`p-4 rounded-lg border ${\n                    isDark ? 'border-dark-border' : 'border-light-border'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                          Enable Animations\n                        </h3>\n                        <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                          Toggle UI animations on or off\n                        </p>\n                      </div>\n                      <YesNoToggle\n                        isEnabled={enableAnimations}\n                        onToggle={() => setEnableAnimations(!enableAnimations)}\n                        size=\"sm\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-end\">\n                    <button\n                      onClick={savePreferences}\n                      className={`px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r ${\n                        isDark\n                          ? 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                          : 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                      } text-white shadow-md transition-all duration-200`}\n                    >\n                      Save Preferences\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Notifications Settings */}\n            {activeTab === 'notifications' && (\n              <div className=\"space-y-6\">\n                <h2 className={`text-xl font-semibold ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                  Notification Settings\n                </h2>\n\n                <div className=\"space-y-6\">\n                  {/* Notification Channels */}\n                  <div>\n                    <h3 className={`text-lg font-medium mb-3 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                      Notification Channels\n                    </h3>\n                    <div className=\"space-y-4\">\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-start\">\n                            <FiMail className={`mt-1 mr-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n                            <div>\n                              <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                                Email Notifications\n                              </h3>\n                              <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                                Receive notifications via email\n                              </p>\n                            </div>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={emailNotifications}\n                            onToggle={() => setEmailNotifications(!emailNotifications)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-start\">\n                            <FiBell className={`mt-1 mr-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n                            <div>\n                              <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                                Push Notifications\n                              </h3>\n                              <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                                Receive notifications in your browser\n                              </p>\n                            </div>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={pushNotifications}\n                            onToggle={() => setPushNotifications(!pushNotifications)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-start\">\n                            <FiSmartphone className={`mt-1 mr-3 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`} />\n                            <div>\n                              <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                                SMS Notifications\n                              </h3>\n                              <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                                Receive notifications via text message\n                              </p>\n                            </div>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={smsNotifications}\n                            onToggle={() => setSmsNotifications(!smsNotifications)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Notification Frequency */}\n                  <div>\n                    <h3 className={`text-lg font-medium mb-3 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                      Notification Frequency\n                    </h3>\n                    <div className={`p-4 rounded-lg border ${\n                      isDark ? 'border-dark-border' : 'border-light-border'\n                    }`}>\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                            How often would you like to receive notifications?\n                          </h3>\n                          <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                            Choose your preferred notification frequency\n                          </p>\n                        </div>\n                        <select\n                          value={notificationFrequency}\n                          onChange={(e) => setNotificationFrequency(e.target.value as 'immediate' | 'daily' | 'weekly')}\n                          className={`px-3 py-2 rounded-lg border ${\n                            isDark\n                              ? 'bg-dark-accent border-dark-border text-dark-text-primary'\n                              : 'bg-light-accent border-light-border text-light-text-primary'\n                          }`}\n                          style={{\n                            // Fix for dropdown options in dark mode\n                            colorScheme: isDark ? 'dark' : 'light'\n                          }}\n                        >\n                          <option value=\"immediate\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>Immediate</option>\n                          <option value=\"daily\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>Daily Digest</option>\n                          <option value=\"weekly\" className={isDark ? 'bg-dark-bg text-dark-text-primary' : ''}>Weekly Summary</option>\n                        </select>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Notification Types */}\n                  <div>\n                    <h3 className={`text-lg font-medium mb-3 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                      Notification Types\n                    </h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Transaction Alerts\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Get notified about new transactions\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={transactionAlerts}\n                            onToggle={() => setTransactionAlerts(!transactionAlerts)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Budget Alerts\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Get notified when approaching budget limits\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={budgetAlerts}\n                            onToggle={() => setBudgetAlerts(!budgetAlerts)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Goal Reminders\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Get reminders about your savings goals\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={goalReminders}\n                            onToggle={() => setGoalReminders(!goalReminders)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Bill Reminders\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Get reminders about upcoming bills\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={billReminders}\n                            onToggle={() => setBillReminders(!billReminders)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Security Alerts\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Get notified about security-related events\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={securityAlerts}\n                            onToggle={() => setSecurityAlerts(!securityAlerts)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className={`p-4 rounded-lg border ${\n                        isDark ? 'border-dark-border' : 'border-light-border'\n                      }`}>\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h3 className={`text-base font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                              Weekly Reports\n                            </h3>\n                            <p className={`text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`}>\n                              Receive weekly financial summary reports\n                            </p>\n                          </div>\n                          <YesNoToggle\n                            isEnabled={weeklyReports}\n                            onToggle={() => setWeeklyReports(!weeklyReports)}\n                            size=\"sm\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-end\">\n                    <button\n                      onClick={saveNotificationSettings}\n                      className={`px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r ${\n                        isDark\n                          ? 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                          : 'from-primary to-secondary hover:from-primary-600 hover:to-secondary-600'\n                      } text-white shadow-md transition-all duration-200`}\n                    >\n                      Save Notification Settings\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Other tabs would be implemented similarly */}\n            {activeTab !== 'account' && activeTab !== 'preferences' && activeTab !== 'notifications' && (\n              <div className=\"flex flex-col items-center justify-center py-12\">\n                <FiSettings className={`w-16 h-16 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'} mb-4`} />\n                <h3 className={`text-xl font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`}>\n                  {tabs.find(tab => tab.id === activeTab)?.label} Settings\n                </h3>\n                <p className={`text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'} mt-2`}>\n                  This section is under development\n                </p>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;;AAdA;;;;;;;;;AAuBe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,SAAS,UAAU;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,6HAAA,CAAA,sBAAmB;IAClF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,2BAA2B;IAC3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IAErG,qBAAqB;IACrB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,6BAA6B;YAC7B,MAAM,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;YAC1B,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,UAAU,KAAK,EAAE;YACjB,YAAY,KAAK,IAAI;YACrB,aAAa,KAAK,KAAK;YAEvB,qBAAqB;YACrB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YACxC,gBAAgB;YAEhB,wBAAwB;YACxB,QAAQ,SAAS,IAAI;YACrB,SAAS,SAAS,KAAK;YACvB,YAAY,SAAS,QAAQ;YAC7B,YAAY,SAAS,QAAQ;YAC7B,cAAc,SAAS,UAAU;YACjC,oBAAoB,SAAS,gBAAgB;YAE7C,yDAAyD;YACzD,IAAI;gBACF,wBAAwB;gBACxB,sBAAsB,SAAS,uBAAuB,EAAE,SAAS;gBACjE,qBAAqB,SAAS,uBAAuB,EAAE,QAAQ;gBAC/D,oBAAoB,SAAS,uBAAuB,EAAE,OAAO;gBAC7D,yBAAyB,SAAS,uBAAuB,EAAE,aAAa;gBAExE,qBAAqB;gBACrB,MAAM,QAAQ,SAAS,uBAAuB,EAAE,SAAS,CAAC;gBAC1D,qBAAqB,MAAM,iBAAiB,IAAI;gBAChD,gBAAgB,MAAM,YAAY,IAAI;gBACtC,iBAAiB,MAAM,aAAa,IAAI;gBACxC,iBAAiB,MAAM,aAAa,IAAI;gBACxC,kBAAkB,MAAM,cAAc,IAAI;gBAC1C,iBAAiB,MAAM,aAAa,IAAI;YAC1C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,yCAAyC;gBACzC,sBAAsB;gBACtB,qBAAqB;gBACrB,oBAAoB;gBACpB,yBAAyB;gBACzB,qBAAqB;gBACrB,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,kBAAkB;gBAClB,iBAAiB;YACnB;YAEA,iBAAiB;YACjB,aAAa;QACf;iCAAG;QAAC;KAAO;IAEX,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,IAAI;YACF,kDAAkD;YAClD,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ;gBACjD;gBACA;YACF;YAEA,gBAAgB;YAChB,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,KAAK;QAC1C,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,KAAK;QACxC;IACF;IAEA,mBAAmB;IACnB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ;gBACjD;gBACA;gBACA;YACF;YAEA,gBAAgB;YAChB,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,KAAK;QAC1C,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,KAAK;QACxC;IACF;IAEA,6BAA6B;IAC7B,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ;gBACjD,yBAAyB;oBACvB,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,WAAW;oBACX,OAAO;wBACL;wBACA;wBACA;wBACA;wBACA;wBACA;oBACF;gBACF;YACF;YAEA,gBAAgB;YAChB,kBAAkB;YAClB,WAAW,IAAM,kBAAkB,KAAK;QAC1C,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,WAAW,IAAM,gBAAgB,KAAK;QACxC;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;QAAI;QACpD;YAAE,IAAI;YAAY,OAAO;YAAY,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;QAAI;QACtD;YAAE,IAAI;YAAiB,OAAO;YAAiB,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;QAAI;QAChE;YAAE,IAAI;YAAW,OAAO;YAAmB,oBAAM,6LAAC,iJAAA,CAAA,eAAY;;;;;QAAI;QAClE;YAAE,IAAI;YAAe,OAAO;YAAe,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;QAAI;QAChE;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;;;;;QAAI;KACvD;IAED,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,6BAA6B;;kCAC5F,6LAAC;wBAAI,WAAU;wBAA4D,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CAChI,6LAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,6LAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAQ;QACR,UAAU;;YAGT,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;;kCAE3B,6LAAC,iJAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAClB;;;;;;;YAKJ,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;;kCAE3B,6LAAC,iJAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;oBACd;;;;;;;0BAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAG,WAAW,CAAC,iCAAiC,EAAE,SAAS,2BAA2B,2BAA2B;kCAAE;;;;;;kCAGpH,6LAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,SAAS,6BAA6B,6BAA6B;kCAAE;;;;;;;;;;;;0BAKrG,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAW,CAAC,wBAAwB,EACvC,SACI,8CACA,8CACL,eAAe,CAAC;sCACd,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,8FAA8F,EACxG,cAAc,IAAI,EAAE,GAChB,GAAG,SACC,kEACA,uDAAuD,GAC3D,GAAG,SACC,kFACA,oFAAoF,EAC5F;;sDAEF,6LAAC;4CAAK,WAAU;sDAAQ,IAAI,IAAI;;;;;;wCAC/B,IAAI,KAAK;;mCAbL,IAAI,EAAE;;;;;;;;;;;;;;;kCAoBnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAW,CAAC,kBAAkB,EACjC,SACI,uCACA,iDACL,IAAI,CAAC;;gCAEH,cAAc,2BACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;sDAAE;;;;;;sDAIzG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;;sEACA,6LAAC;4DAAM,WAAW,CAAC,+BAA+B,EAAE,SAAS,6BAA6B,6BAA6B;sEAAE;;;;;;sEAGzH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,yCAAyC,EACxD,SAAS,8BAA8B,4BACxC,+DAA+D,CAAC;8EAC9D,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE;;;;;;8EAEf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAO,WAAW,CAAC,yCAAyC,EAC3D,SACI,kEACA,oEACJ;sFAAE;;;;;;sFAGJ,6LAAC;4EAAO,WAAW,CAAC,8CAA8C,EAChE,SACI,oEACA,qEACJ;sFAAE;;;;;;;;;;;;;;;;;;;;;;;;8DAOV,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;;sEACA,6LAAC;4DAAM,WAAW,CAAC,+BAA+B,EAAE,SAAS,6BAA6B,6BAA6B;sEAAE;;;;;;sEAGzH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,WAAW,CAAC,mCAAmC,EAC7C,SACI,6DACA,8DACL,wCAAwC,CAAC;oEAC1C,OAAO;oEACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACvC,aAAY;;;;;;8EAEd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;wEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;;;;;;;;;;;;;;;;;sEAGnG,6LAAC;4DAAE,WAAW,CAAC,aAAa,EAAE,SAAS,4BAA4B,4BAA4B;sEAAE;;;;;;;;;;;;8DAKnG,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;;sEACA,6LAAC;4DAAM,WAAW,CAAC,+BAA+B,EAAE,SAAS,6BAA6B,6BAA6B;sEAAE;;;;;;sEAGzH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,WAAW,CAAC,mCAAmC,EAC7C,SACI,2DACA,4DACL,mBAAmB,CAAC;oEACrB,OAAO;oEACP,QAAQ;;;;;;8EAEV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;wEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;;;;;;;;;;;;;;;;;sEAGnG,6LAAC;4DAAE,WAAW,CAAC,aAAa,EAAE,SAAS,4BAA4B,4BAA4B;sEAAE;;;;;;;;;;;;8DAKnG,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;;sEACA,6LAAC;4DAAM,WAAW,CAAC,+BAA+B,EAAE,SAAS,6BAA6B,6BAA6B;sEAAE;;;;;;sEAGzH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAW,CAAC,yDAAyD,EACnE,SACI,6DACA,8DACL,wCAAwC,CAAC;oEAC1C,OAAO;oEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC3C,OAAO;wEACL,aAAa,SAAS,SAAS;oEACjC;;sFAEA,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;sFAClF,6LAAC;4EAAO,OAAM;4EAAM,WAAW,SAAS,sCAAsC;sFAAI;;;;;;;;;;;;8EAEpF,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iJAAA,CAAA,eAAY;wEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;;;;;;;;;;;8EAEvG,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;wEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;;;;;;;;;;;;;;;;;sEAG1G,6LAAC;4DAAE,WAAW,CAAC,aAAa,EAAE,SAAS,4BAA4B,4BAA4B;sEAAE;;;;;;;;;;;;8DAKnG,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,WAAW,CAAC,0DAA0D,EACpE,SACI,4EACA,0EACL,iDAAiD,CAAC;kEACpD;;;;;;;;;;;;;;;;;;;;;;;gCASR,cAAc,+BACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;sDAAE;;;;;;sDAIzG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8DACA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kFAAE;;;;;;kFAGzG,6LAAC;wEAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kFAAE;;;;;;;;;;;;0EAI9F,6LAAC,oJAAA,CAAA,UAAqB;;;;;;;;;;;;;;;;8DAI1B,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8DACA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kFAAE;;;;;;kFAGzG,6LAAC;wEAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kFAAE;;;;;;;;;;;;0EAI9F,6LAAC;gEACC,WAAW,CAAC,4BAA4B,EACtC,SACI,6DACA,+DACJ;gEACF,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,OAAO;oEACL,aAAa,SAAS,SAAS;gEACjC;;kFAEA,6LAAC;wEAAO,OAAM;wEAAU,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACtF,6LAAC;wEAAO,OAAM;wEAAU,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACtF,6LAAC;wEAAO,OAAM;wEAAS,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACrF,6LAAC;wEAAO,OAAM;wEAAS,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACrF,6LAAC;wEAAO,OAAM;wEAAW,WAAW,SAAS,sCAAsC;kFAAI;;;;;;;;;;;;;;;;;;;;;;;8DAK7F,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8DACA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kFAAE;;;;;;kFAGzG,6LAAC;wEAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kFAAE;;;;;;;;;;;;0EAI9F,6LAAC;gEACC,WAAW,CAAC,4BAA4B,EACtC,SACI,6DACA,+DACJ;gEACF,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,OAAO;oEACL,aAAa,SAAS,SAAS;gEACjC;;kFAEA,6LAAC;wEAAO,OAAM;wEAAa,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACzF,6LAAC;wEAAO,OAAM;wEAAa,WAAW,SAAS,sCAAsC;kFAAI;;;;;;kFACzF,6LAAC;wEAAO,OAAM;wEAAa,WAAW,SAAS,sCAAsC;kFAAI;;;;;;;;;;;;;;;;;;;;;;;8DAK/F,6LAAC;oDAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8DACA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kFAAE;;;;;;kFAGzG,6LAAC;wEAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kFAAE;;;;;;;;;;;;0EAI9F,6LAAC,0IAAA,CAAA,UAAW;gEACV,WAAW;gEACX,UAAU,IAAM,oBAAoB,CAAC;gEACrC,MAAK;;;;;;;;;;;;;;;;;8DAKX,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,WAAW,CAAC,0DAA0D,EACpE,SACI,4EACA,0EACL,iDAAiD,CAAC;kEACpD;;;;;;;;;;;;;;;;;;;;;;;gCASR,cAAc,iCACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;sDAAE;;;;;;sDAIzG,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,CAAC,yBAAyB,EAAE,SAAS,2BAA2B,2BAA2B;sEAAE;;;;;;sEAG5G,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iJAAA,CAAA,SAAM;wFAAC,WAAW,CAAC,UAAU,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;kGACnG,6LAAC;;0GACC,6LAAC;gGAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;0GAAE;;;;;;0GAGzG,6LAAC;gGAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;0GAAE;;;;;;;;;;;;;;;;;;0FAKhG,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,sBAAsB,CAAC;gFACvC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iJAAA,CAAA,SAAM;wFAAC,WAAW,CAAC,UAAU,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;kGACnG,6LAAC;;0GACC,6LAAC;gGAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;0GAAE;;;;;;0GAGzG,6LAAC;gGAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;0GAAE;;;;;;;;;;;;;;;;;;0FAKhG,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,qBAAqB,CAAC;gFACtC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iJAAA,CAAA,eAAY;wFAAC,WAAW,CAAC,UAAU,EAAE,SAAS,6BAA6B,6BAA6B;;;;;;kGACzG,6LAAC;;0GACC,6LAAC;gGAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;0GAAE;;;;;;0GAGzG,6LAAC;gGAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;0GAAE;;;;;;;;;;;;;;;;;;0FAKhG,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,oBAAoB,CAAC;gFACrC,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAQf,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,CAAC,yBAAyB,EAAE,SAAS,2BAA2B,2BAA2B;sEAAE;;;;;;sEAG5G,6LAAC;4DAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;sEACA,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;0FAAE;;;;;;0FAGzG,6LAAC;gFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;0FAAE;;;;;;;;;;;;kFAI9F,6LAAC;wEACC,OAAO;wEACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;wEACxD,WAAW,CAAC,4BAA4B,EACtC,SACI,6DACA,+DACJ;wEACF,OAAO;4EACL,wCAAwC;4EACxC,aAAa,SAAS,SAAS;wEACjC;;0FAEA,6LAAC;gFAAO,OAAM;gFAAY,WAAW,SAAS,sCAAsC;0FAAI;;;;;;0FACxF,6LAAC;gFAAO,OAAM;gFAAQ,WAAW,SAAS,sCAAsC;0FAAI;;;;;;0FACpF,6LAAC;gFAAO,OAAM;gFAAS,WAAW,SAAS,sCAAsC;0FAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAO7F,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,CAAC,yBAAyB,EAAE,SAAS,2BAA2B,2BAA2B;sEAAE;;;;;;sEAG5G,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,qBAAqB,CAAC;gFACtC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,gBAAgB,CAAC;gFACjC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,iBAAiB,CAAC;gFAClC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,iBAAiB,CAAC;gFAClC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,kBAAkB,CAAC;gFACnC,MAAK;;;;;;;;;;;;;;;;;8EAKX,6LAAC;oEAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,uBAAuB,uBAChC;8EACA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAW,CAAC,sBAAsB,EAAE,SAAS,2BAA2B,2BAA2B;kGAAE;;;;;;kGAGzG,6LAAC;wFAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,4BAA4B;kGAAE;;;;;;;;;;;;0FAI9F,6LAAC,0IAAA,CAAA,UAAW;gFACV,WAAW;gFACX,UAAU,IAAM,iBAAiB,CAAC;gFAClC,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,WAAW,CAAC,0DAA0D,EACpE,SACI,4EACA,0EACL,iDAAiD,CAAC;kEACpD;;;;;;;;;;;;;;;;;;;;;;;gCASR,cAAc,aAAa,cAAc,iBAAiB,cAAc,iCACvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,aAAU;4CAAC,WAAW,CAAC,UAAU,EAAE,SAAS,4BAA4B,2BAA2B,KAAK,CAAC;;;;;;sDAC1G,6LAAC;4CAAG,WAAW,CAAC,oBAAoB,EAAE,SAAS,2BAA2B,2BAA2B;;gDAClG,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;gDAAM;;;;;;;sDAEjD,6LAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,SAAS,6BAA6B,4BAA4B,KAAK,CAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnH;GAj1BwB;;QACS,kIAAA,CAAA,WAAQ;QAExB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}