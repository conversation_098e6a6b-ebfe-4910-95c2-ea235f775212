[{"name": "hot-reloader", "duration": 314, "timestamp": 9862437148, "id": 3, "tags": {"version": "15.2.4"}, "startTime": 1750783165348, "traceId": "0ea8751c327adee0"}, {"name": "setup-dev-bundler", "duration": 1811356, "timestamp": 9861873527, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750783164785, "traceId": "0ea8751c327adee0"}, {"name": "run-instrumentation-hook", "duration": 48, "timestamp": 9863832308, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750783166743, "traceId": "0ea8751c327adee0"}, {"name": "start-dev-server", "duration": 3783475, "timestamp": 9860109298, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "1468452864", "memory.totalMem": "7866085376", "memory.heapSizeLimit": "3982491648", "memory.rss": "261754880", "memory.heapTotal": "103440384", "memory.heapUsed": "64863696"}, "startTime": 1750783163021, "traceId": "0ea8751c327adee0"}, {"name": "compile-path", "duration": 5842046, "timestamp": 9895296109, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750783198207, "traceId": "0ea8751c327adee0"}, {"name": "ensure-page", "duration": 5845998, "timestamp": 9895293509, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750783198205, "traceId": "0ea8751c327adee0"}]