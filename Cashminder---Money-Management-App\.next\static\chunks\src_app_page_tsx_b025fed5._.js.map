{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/mk/Cashminder---Money-Management-App/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  FiBarChart2, FiDollarSign, FiPieChart, FiTarget,\n  FiArrowRight, FiShield, FiTrendingUp, FiZap,\n  FiTwitter, FiGithub, FiLinkedin, FiMail, FiFileText\n} from 'react-icons/fi';\nimport { useTheme } from '@/context/ThemeContext';\n\n// Animated number counter component\nconst AnimatedCounter = ({ value, duration = 0.5 }: { value: number, duration?: number }) => {\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    let start = 0;\n    const end = value;\n    const totalMiliseconds = duration * 1000;\n\n    // Use a faster increment for large numbers\n    const increment = Math.max(1, Math.floor(end / 30));\n    const incrementTime = totalMiliseconds / (end / increment);\n\n    const timer = setInterval(() => {\n      start = Math.min(end, start + increment);\n      setCount(start);\n      if (start >= end) clearInterval(timer);\n    }, incrementTime);\n\n    return () => clearInterval(timer);\n  }, [value, duration]);\n\n  return <span>{count.toLocaleString()}</span>;\n};\n\nexport default function Home() {\n  const { theme } = useTheme();\n  const isDark = theme === 'dark';\n  const [isVisible, setIsVisible] = useState(false);\n  const [userData, setUserData] = useState({\n    totalBalance: 0,\n    income: 0,\n    expenses: 0,\n    savingsGoal: {\n      current: 0,\n      target: 10000,\n      percentage: 0\n    }\n  });\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n\n    // Check if user is logged in and get their data\n    const storedUser = localStorage.getItem('cashminder_user');\n    if (storedUser) {\n      setIsLoggedIn(true);\n\n      // In a real app, we would fetch the user's data from the API\n      // For now, we'll just use zeros for a new user\n      setUserData({\n        totalBalance: 0,\n        income: 0,\n        expenses: 0,\n        savingsGoal: {\n          current: 0,\n          target: 10000,\n          percentage: 0\n        }\n      });\n    }\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { type: 'spring', stiffness: 100 }\n    }\n  };\n\n  const features = [\n    {\n      icon: <FiBarChart2 className=\"w-8 h-8\" />,\n      title: \"AI-Powered Analytics\",\n      description: \"Harness the power of machine learning to predict spending patterns and optimize your financial decisions.\"\n    },\n    {\n      icon: <FiPieChart className=\"w-8 h-8\" />,\n      title: \"Dynamic Budgeting\",\n      description: \"Real-time budget tracking with intelligent categorization and personalized alerts.\"\n    },\n    {\n      icon: <FiTarget className=\"w-8 h-8\" />,\n      title: \"Smart Goal Tracking\",\n      description: \"Set and achieve financial goals with adaptive recommendations based on your spending habits.\"\n    },\n    {\n      icon: <FiTrendingUp className=\"w-8 h-8\" />,\n      title: \"Investment Insights\",\n      description: \"Track your investments and receive AI-powered recommendations for portfolio optimization.\"\n    },\n    {\n      icon: <FiShield className=\"w-8 h-8\" />,\n      title: \"Bank-Level Security\",\n      description: \"Your financial data is protected with enterprise-grade encryption and security protocols.\"\n    },\n    {\n      icon: <FiZap className=\"w-8 h-8\" />,\n      title: \"Real-Time Notifications\",\n      description: \"Instant alerts for unusual spending, bill payments, and financial opportunities.\"\n    }\n  ];\n\n  return (\n    <div className=\"relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"fixed inset-0 z-0\">\n        {/* Finance-themed background */}\n        <div className=\"absolute inset-0 bg-light-bg dark:bg-space-gradient animate-gradient\"></div>\n\n        {/* Grid overlay */}\n        <div className=\"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] opacity-[0.03] dark:opacity-[0.1]\"></div>\n\n        {/* Animated gradient accent */}\n        <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-secondary to-primary animate-gradient\"></div>\n\n        {/* Glowing orbs */}\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl\"></div>\n        <motion.div\n          className=\"absolute bottom-1/3 right-1/4 w-64 h-64 bg-secondary/10 dark:bg-secondary/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.1, 0.2, 0.1]\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n        ></motion.div>\n\n        {/* Animated lines */}\n        {[...Array(3)].map((_, i) => (\n          <motion.div\n            key={`line-${i}`}\n            className=\"absolute h-[1px] bg-gradient-to-r from-transparent via-primary/30 to-transparent\"\n            style={{\n              top: `${30 + i * 20}%`,\n              left: 0,\n              right: 0,\n              transformOrigin: 'center',\n            }}\n            animate={{\n              scaleX: [0.5, 1.5, 0.5],\n              opacity: [0.1, 0.3, 0.1],\n            }}\n            transition={{\n              duration: 8 + i,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: i * 0.5,\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Hero Section */}\n      <section className=\"relative min-h-screen flex items-center justify-center px-4 py-20\">\n        <div className=\"container mx-auto max-w-7xl\">\n          <motion.div\n            className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\"\n            initial=\"hidden\"\n            animate={isVisible ? \"visible\" : \"hidden\"}\n            variants={containerVariants}\n          >\n            <div className=\"space-y-8\">\n              <motion.div variants={itemVariants}>\n                <span className=\"inline-block px-4 py-2 rounded-full text-sm font-medium bg-primary/10 dark:bg-primary/20 text-primary-700 dark:text-primary mb-4\">\n                  Smart Financial Management\n                </span>\n                <h1 className=\"leading-tight\">\n                  <span className=\"block text-4xl md:text-5xl font-orbitron font-bold text-light-text-primary dark:text-dark-text-primary letter-spacing-wide mb-2\">\n                    MASTER YOUR MONEY\n                  </span>\n                  <span className=\"block text-5xl md:text-6xl font-audiowide text-shimmer\">\n                    SHAPE YOUR FUTURE\n                  </span>\n                </h1>\n              </motion.div>\n\n              <motion.p\n                className=\"text-xl font-rajdhani text-light-text-secondary dark:text-dark-text-secondary mt-6 max-w-2xl\"\n                variants={itemVariants}\n              >\n                <span className=\"font-semibold text-primary\">Visualize</span> your spending patterns,\n                <span className=\"font-semibold text-secondary\"> automate</span> your savings, and\n                <span className=\"font-semibold text-primary\"> unlock</span> your financial potential with our AI-powered platform.\n              </motion.p>\n\n              <motion.div\n                className=\"flex flex-col sm:flex-row gap-4 mt-8\"\n                variants={itemVariants}\n              >\n                <Link href={isLoggedIn ? \"/dashboard\" : \"/auth\"}>\n                  <motion.button\n                    className=\"group flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-dark-bg bg-primary hover:bg-primary-400 transition-all duration-300 shadow-md\"\n                    whileHover={{\n                      scale: 1.05,\n                      boxShadow: \"var(--glow-primary)\"\n                    }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    {isLoggedIn ? \"Go to Dashboard\" : \"Get Started\"}\n                    <FiArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\n                  </motion.button>\n                </Link>\n                <Link href=\"#features\">\n                  <motion.button\n                    className=\"flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl border border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary hover:bg-light-accent dark:hover:bg-dark-accent transition-all duration-300\"\n                    whileHover={{\n                      scale: 1.05,\n                      boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)\"\n                    }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    Learn More\n                  </motion.button>\n                </Link>\n              </motion.div>\n            </div>\n\n            <motion.div\n              className=\"relative\"\n              variants={itemVariants}\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl\"></div>\n              <motion.div\n                className=\"relative p-8 rounded-2xl glass-card\"\n                whileHover={{\n                  y: -5,\n                  boxShadow: \"var(--glow-primary)\"\n                }}\n              >\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h3 className=\"text-xl font-semibold text-light-text-primary dark:text-dark-text-primary\">Financial Overview</h3>\n                  <span className=\"text-sm text-primary font-medium\">Today</span>\n                </div>\n\n                <div className=\"space-y-6\">\n                  <div className=\"p-4 rounded-xl bg-light-card dark:bg-dark-accent border border-light-border dark:border-dark-border finance-card\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-light-text-secondary dark:text-dark-text-secondary\">Total Balance</span>\n                      <FiDollarSign className=\"text-primary\" />\n                    </div>\n                    <div className=\"text-2xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n                      ${userData.totalBalance.toLocaleString()}\n                    </div>\n                    {userData.totalBalance > 0 ? (\n                      <div className=\"flex items-center mt-1 text-success text-sm\">\n                        <FiTrendingUp className=\"mr-1\" />\n                        <span>Track your balance</span>\n                      </div>\n                    ) : (\n                      <div className=\"flex items-center mt-1 text-secondary text-sm\">\n                        <span>Add transactions to see your balance</span>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"p-4 rounded-xl bg-success/10 dark:bg-success/20 border border-success/20 dark:border-success/30 finance-card\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <span className=\"text-sm text-light-text-secondary dark:text-dark-text-secondary\">Income</span>\n                        <FiTrendingUp className=\"text-success\" />\n                      </div>\n                      <div className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n                        ${userData.income.toLocaleString()}\n                      </div>\n                    </div>\n\n                    <div className=\"p-4 rounded-xl bg-danger/10 dark:bg-danger/20 border border-danger/20 dark:border-danger/30 finance-card\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <span className=\"text-sm text-light-text-secondary dark:text-dark-text-secondary\">Expenses</span>\n                        <FiBarChart2 className=\"text-danger\" />\n                      </div>\n                      <div className=\"text-xl font-bold text-light-text-primary dark:text-dark-text-primary\">\n                        ${userData.expenses.toLocaleString()}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"p-4 rounded-xl bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30 finance-card\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-light-text-secondary dark:text-dark-text-secondary\">Savings Goal</span>\n                      <FiTarget className=\"text-primary\" />\n                    </div>\n                    <div className=\"relative pt-1\">\n                      <div className=\"flex mb-2 items-center justify-between\">\n                        <div>\n                          <span className=\"text-xs font-semibold inline-block text-light-text-primary dark:text-dark-text-primary\">\n                            {userData.savingsGoal.percentage}% Complete\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <span className=\"text-xs font-semibold inline-block text-light-text-muted dark:text-dark-text-muted\">\n                            ${userData.savingsGoal.current.toLocaleString()} / ${userData.savingsGoal.target.toLocaleString()}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"overflow-hidden h-2 mb-4 text-xs flex rounded-full bg-light-accent dark:bg-dark-bg\">\n                        <motion.div\n                          className=\"shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-gradient\"\n                          initial={{ width: \"0%\" }}\n                          animate={{ width: `${userData.savingsGoal.percentage}%` }}\n                          transition={{ duration: 1.5, ease: \"easeOut\" }}\n                        ></motion.div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"relative py-20\">\n        <div className=\"container mx-auto max-w-7xl px-4\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true, margin: \"-100px\" }}\n            transition={{ duration: 0.5 }}\n          >\n            <span className=\"inline-block px-4 py-2 rounded-full text-sm font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary mb-4\">\n              Features\n            </span>\n            <h2 className=\"font-orbitron text-4xl md:text-5xl font-bold mb-4 letter-spacing-wide\">\n              <span className=\"text-glow dark:text-glow text-primary\">\n                NEXT-GEN FINANCIAL TOOLS\n              </span>\n            </h2>\n            <p className=\"text-xl font-rajdhani max-w-3xl mx-auto text-light-text-secondary dark:text-dark-text-secondary\">\n              Cutting-edge features engineered to revolutionize how you <span className=\"font-semibold text-primary\">analyze</span>,\n              <span className=\"font-semibold text-secondary\"> optimize</span>, and\n              <span className=\"font-semibold text-primary\"> maximize</span> your financial potential\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                className=\"relative group\"\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true, margin: \"-100px\" }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity\"></div>\n                <motion.div\n                  className=\"relative p-6 rounded-xl border border-light-border dark:border-dark-border bg-light-card dark:bg-dark-card finance-card h-full\"\n                  whileHover={{\n                    y: -5,\n                    boxShadow: \"var(--glow-primary)\"\n                  }}\n                >\n                  <div className=\"text-primary mb-4 text-3xl\">{feature.icon}</div>\n                  <h3 className=\"text-xl font-semibold mb-2 text-light-text-primary dark:text-dark-text-primary\">{feature.title}</h3>\n                  <p className=\"text-light-text-secondary dark:text-dark-text-secondary\">{feature.description}</p>\n                </motion.div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"relative py-20\">\n        <div className=\"container mx-auto max-w-7xl px-4\">\n          <motion.div\n            className=\"rounded-2xl glass-card p-8 md:p-12\"\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true, margin: \"-100px\" }}\n            transition={{ duration: 0.5 }}\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {[\n                { label: \"Active Users\", value: 25000, suffix: \"+\" },\n                { label: \"Transactions Processed\", value: 1500000, suffix: \"+\" },\n                { label: \"Savings Goals Achieved\", value: 8700, suffix: \"+\" },\n                { label: \"Customer Satisfaction\", value: 98, suffix: \"%\" }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  className=\"text-center\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  viewport={{ once: true, margin: \"-100px\" }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                >\n                  <div className=\"text-4xl font-bold mb-2 text-light-text-primary dark:text-dark-text-primary\">\n                    <AnimatedCounter value={stat.value} duration={0.8} />{stat.suffix}\n                  </div>\n                  <p className=\"text-light-text-secondary dark:text-dark-text-secondary\">{stat.label}</p>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative py-20\">\n        <div className=\"container mx-auto max-w-7xl px-4\">\n          <motion.div\n            className=\"text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true, margin: \"-100px\" }}\n            transition={{ duration: 0.5 }}\n          >\n            <h2 className=\"mb-6\">\n              <span className=\"block text-3xl md:text-4xl font-rajdhani font-bold text-light-text-primary dark:text-dark-text-primary mb-2\">\n                READY TO ELEVATE YOUR\n              </span>\n              <span className=\"block text-5xl md:text-6xl font-audiowide text-glow-green text-secondary letter-spacing-wide\">\n                FINANCIAL INTELLIGENCE?\n              </span>\n            </h2>\n            <p className=\"text-xl font-rajdhani max-w-3xl mx-auto mb-8 text-light-text-secondary dark:text-dark-text-secondary\">\n              Join the <span className=\"font-semibold text-primary\">10,000+</span> users who are already leveraging our platform to\n              <span className=\"font-semibold text-secondary\"> build wealth</span>,\n              <span className=\"font-semibold text-primary\"> reduce debt</span>, and\n              <span className=\"font-semibold text-secondary\"> secure</span> their financial future\n            </p>\n            <Link href={isLoggedIn ? \"/dashboard\" : \"/auth\"}>\n              <motion.button\n                className=\"group flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-dark-bg bg-secondary hover:bg-secondary-400 transition-all duration-300 mx-auto shadow-md\"\n                whileHover={{\n                  scale: 1.05,\n                  boxShadow: \"var(--glow-secondary)\"\n                }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {isLoggedIn ? \"Go to Dashboard\" : \"Get Started Now\"}\n                <FiArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\n              </motion.button>\n            </Link>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"relative py-12 border-t border-light-border dark:border-dark-border bg-light-accent dark:bg-dark-accent\">\n        <div className=\"container mx-auto max-w-7xl px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\">\n            {/* Logo and description */}\n            <div className=\"flex flex-col items-center md:items-start\">\n              <Link href=\"/\" className=\"flex items-center mb-4 hover:opacity-80 transition-opacity\">\n                <div className=\"w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md\">\n                  <span className=\"text-dark-bg font-bold text-xl\">C</span>\n                </div>\n                <span className=\"font-audiowide text-xl text-light-text-primary dark:text-dark-text-primary\">\n                  Cashminder\n                </span>\n              </Link>\n              <p className=\"text-light-text-secondary dark:text-dark-text-secondary text-center md:text-left mb-4\">\n                Your intelligent financial companion for smarter money management and wealth building.\n              </p>\n              <div className=\"flex space-x-4 mt-2\">\n                <Link href=\"https://twitter.com/I_m_shivansh\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"Twitter\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\">\n                    <FiTwitter className=\"text-primary\" />\n                  </div>\n                </Link>\n                <Link href=\"https://github.com/shivanshpathak01\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"GitHub\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\">\n                    <FiGithub className=\"text-primary\" />\n                  </div>\n                </Link>\n                <Link href=\"https://www.linkedin.com/in/shivansh-pathak-02a72121a/\" target=\"_blank\" rel=\"noopener noreferrer\" aria-label=\"LinkedIn\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors cursor-pointer\">\n                    <FiLinkedin className=\"text-primary\" />\n                  </div>\n                </Link>\n              </div>\n            </div>\n\n            {/* Quick links */}\n            <div className=\"flex flex-col items-center md:items-start\">\n              <h3 className=\"font-rajdhani font-bold text-xl mb-4 text-light-text-primary dark:text-dark-text-primary\">\n                Quick Links\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/dashboard\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiArrowRight className=\"mr-2 text-primary\" size={14} />\n                    Dashboard\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/transactions\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiArrowRight className=\"mr-2 text-primary\" size={14} />\n                    Transactions\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/analytics\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiArrowRight className=\"mr-2 text-primary\" size={14} />\n                    Analytics\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/goals\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiArrowRight className=\"mr-2 text-primary\" size={14} />\n                    Goals\n                  </Link>\n                </li>\n              </ul>\n            </div>\n\n            {/* Legal and contact */}\n            <div className=\"flex flex-col items-center md:items-start\">\n              <h3 className=\"font-rajdhani font-bold text-xl mb-4 text-light-text-primary dark:text-dark-text-primary\">\n                Legal & Contact\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/privacy\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiShield className=\"mr-2 text-primary\" size={14} />\n                    Privacy Policy\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/terms\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiFileText className=\"mr-2 text-primary\" size={14} />\n                    Terms of Service\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/contact\" className=\"text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary transition-colors flex items-center\">\n                    <FiMail className=\"mr-2 text-primary\" size={14} />\n                    Contact the Developer\n                  </Link>\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"pt-8 border-t border-light-border dark:border-dark-border text-center\">\n            <p className=\"text-light-text-secondary dark:text-dark-text-secondary mb-2\">\n              Made with <span className=\"text-red-500\">❤️</span> by Shivansh Pathak\n            </p>\n            <p className=\"text-light-text-muted dark:text-dark-text-muted\">\n              &copy; {new Date().getFullYear()} Cashminder. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAKA;;;AAVA;;;;;;AAYA,oCAAoC;AACpC,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,EAAwC;;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;YACZ,MAAM,MAAM;YACZ,MAAM,mBAAmB,WAAW;YAEpC,2CAA2C;YAC3C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM;YAC/C,MAAM,gBAAgB,mBAAmB,CAAC,MAAM,SAAS;YAEzD,MAAM,QAAQ;mDAAY;oBACxB,QAAQ,KAAK,GAAG,CAAC,KAAK,QAAQ;oBAC9B,SAAS;oBACT,IAAI,SAAS,KAAK,cAAc;gBAClC;kDAAG;YAEH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG;QAAC;QAAO;KAAS;IAEpB,qBAAO,6LAAC;kBAAM,MAAM,cAAc;;;;;;AACpC;GAtBM;KAAA;AAwBS,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,UAAU;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,QAAQ;QACR,UAAU;QACV,aAAa;YACX,SAAS;YACT,QAAQ;YACR,YAAY;QACd;IACF;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;YAEb,gDAAgD;YAChD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,cAAc;gBAEd,6DAA6D;gBAC7D,+CAA+C;gBAC/C,YAAY;oBACV,cAAc;oBACd,QAAQ;oBACR,UAAU;oBACV,aAAa;wBACX,SAAS;wBACT,QAAQ;wBACR,YAAY;oBACd;gBACF;YACF;QACF;yBAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,MAAM;gBAAU,WAAW;YAAI;QAC/C;IACF;IAEA,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,iJAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;oBAID;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;gCACtB,MAAM;gCACN,OAAO;gCACP,iBAAiB;4BACnB;4BACA,SAAS;gCACP,QAAQ;oCAAC;oCAAK;oCAAK;iCAAI;gCACvB,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;4BAC1B;4BACA,YAAY;gCACV,UAAU,IAAI;gCACd,QAAQ;gCACR,MAAM;gCACN,OAAO,IAAI;4BACb;2BAjBK,CAAC,KAAK,EAAE,GAAG;;;;;;;;;;;0BAuBtB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAQ;wBACR,SAAS,YAAY,YAAY;wBACjC,UAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,UAAU;;0DACpB,6LAAC;gDAAK,WAAU;0DAAmI;;;;;;0DAGnJ,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAkI;;;;;;kEAGlJ,6LAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;;;;;;;kDAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,UAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;4CAAgB;0DAC7D,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;4CAAgB;0DAC/D,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;4CAAc;;;;;;;kDAG7D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;;0DAEV,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,aAAa,eAAe;0DACtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDACV,OAAO;wDACP,WAAW;oDACb;oDACA,UAAU;wDAAE,OAAO;oDAAK;;wDAEvB,aAAa,oBAAoB;sEAClC,6LAAC,iJAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG5B,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDACV,OAAO;wDACP,WAAW;oDACb;oDACA,UAAU;wDAAE,OAAO;oDAAK;8DACzB;;;;;;;;;;;;;;;;;;;;;;;0CAOP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;;kDAEV,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CACV,GAAG,CAAC;4CACJ,WAAW;wCACb;;0DAEA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4E;;;;;;kEAC1F,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAGrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAkE;;;;;;kFAClF,6LAAC,iJAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;;oEAAyE;oEACpF,SAAS,YAAY,CAAC,cAAc;;;;;;;4DAEvC,SAAS,YAAY,GAAG,kBACvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iJAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,6LAAC;kFAAK;;;;;;;;;;;qFAGR,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;8EAAK;;;;;;;;;;;;;;;;;kEAKZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAkE;;;;;;0FAClF,6LAAC,iJAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;kFAE1B,6LAAC;wEAAI,WAAU;;4EAAwE;4EACnF,SAAS,MAAM,CAAC,cAAc;;;;;;;;;;;;;0EAIpC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAkE;;;;;;0FAClF,6LAAC,iJAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;;kFAEzB,6LAAC;wEAAI,WAAU;;4EAAwE;4EACnF,SAAS,QAAQ,CAAC,cAAc;;;;;;;;;;;;;;;;;;;kEAKxC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAkE;;;;;;kFAClF,6LAAC,iJAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;0EAEtB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FACC,cAAA,6LAAC;oFAAK,WAAU;;wFACb,SAAS,WAAW,CAAC,UAAU;wFAAC;;;;;;;;;;;;0FAGrC,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFAAK,WAAU;;wFAAqF;wFACjG,SAAS,WAAW,CAAC,OAAO,CAAC,cAAc;wFAAG;wFAAK,SAAS,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;kFAIrG,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4EACT,WAAU;4EACV,SAAS;gFAAE,OAAO;4EAAK;4EACvB,SAAS;gFAAE,OAAO,GAAG,SAAS,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;4EAAC;4EACxD,YAAY;gFAAE,UAAU;gFAAK,MAAM;4EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajE,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAS;4BACzC,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC;oCAAK,WAAU;8CAA+H;;;;;;8CAG/I,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;8CAI1D,6LAAC;oCAAE,WAAU;;wCAAkG;sDACnD,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAAc;sDACrH,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;wCAAgB;sDAC/D,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAAgB;;;;;;;;;;;;;sCAIjE,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAS;oCACzC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;;sDAEhD,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDACV,GAAG,CAAC;gDACJ,WAAW;4CACb;;8DAEA,6LAAC;oDAAI,WAAU;8DAA8B,QAAQ,IAAI;;;;;;8DACzD,6LAAC;oDAAG,WAAU;8DAAkF,QAAQ,KAAK;;;;;;8DAC7G,6LAAC;oDAAE,WAAU;8DAA2D,QAAQ,WAAW;;;;;;;;;;;;;mCAjBxF;;;;;;;;;;;;;;;;;;;;;0BA0Bf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAS;wBACzC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,OAAO;oCAAgB,OAAO;oCAAO,QAAQ;gCAAI;gCACnD;oCAAE,OAAO;oCAA0B,OAAO;oCAAS,QAAQ;gCAAI;gCAC/D;oCAAE,OAAO;oCAA0B,OAAO;oCAAM,QAAQ;gCAAI;gCAC5D;oCAAE,OAAO;oCAAyB,OAAO;oCAAI,QAAQ;gCAAI;6BAC1D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAS;oCACzC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;;sDAEhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAgB,OAAO,KAAK,KAAK;oDAAE,UAAU;;;;;;gDAAQ,KAAK,MAAM;;;;;;;sDAEnE,6LAAC;4CAAE,WAAU;sDAA2D,KAAK,KAAK;;;;;;;mCAV7E;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBjB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAS;wBACzC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAA8G;;;;;;kDAG9H,6LAAC;wCAAK,WAAU;kDAA+F;;;;;;;;;;;;0CAIjH,6LAAC;gCAAE,WAAU;;oCAAuG;kDACzG,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;oCAAc;kDACpE,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAAoB;kDACnE,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;oCAAmB;kDAChE,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAAc;;;;;;;0CAE/D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,aAAa,eAAe;0CACtC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCACV,OAAO;wCACP,WAAW;oCACb;oCACA,UAAU;wCAAE,OAAO;oCAAK;;wCAEvB,aAAa,oBAAoB;sDAClC,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;8DAEnD,6LAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAI/F,6LAAC;4CAAE,WAAU;sDAAwF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmC,QAAO;oDAAS,KAAI;oDAAsB,cAAW;8DACjG,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGzB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsC,QAAO;oDAAS,KAAI;oDAAsB,cAAW;8DACpG,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGxB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAyD,QAAO;oDAAS,KAAI;oDAAsB,cAAW;8DACvH,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2F;;;;;;sDAGzG,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;;0EAChC,6LAAC,iJAAA,CAAA,eAAY;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAgB,WAAU;;0EACnC,6LAAC,iJAAA,CAAA,eAAY;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;;0EAChC,6LAAC,iJAAA,CAAA,eAAY;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;;0EAC5B,6LAAC,iJAAA,CAAA,eAAY;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAQhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2F;;;;;;sDAGzG,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;0EAC9B,6LAAC,iJAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;8DAIxD,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;;0EAC5B,6LAAC,iJAAA,CAAA,aAAU;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;8DAI1D,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;0EAC9B,6LAAC,iJAAA,CAAA,SAAM;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAA+D;sDAChE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAS;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;;wCAAkD;wCACrD,IAAI,OAAO,WAAW;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;IAhiBwB;;QACJ,kIAAA,CAAA,WAAQ;;;MADJ", "debugId": null}}]}