<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/b412c1caed99ddf9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-0b26fac893bca99f.js"/><script src="/_next/static/chunks/4bd1b696-16d61eec428fe094.js" async=""></script><script src="/_next/static/chunks/684-c0b26dbc85cf007a.js" async=""></script><script src="/_next/static/chunks/main-app-0bb0582c6e19163d.js" async=""></script><script src="/_next/static/chunks/ee560e2c-e9c09e02cef8864c.js" async=""></script><script src="/_next/static/chunks/493-1cf8f8ab3d5926af.js" async=""></script><script src="/_next/static/chunks/384-c168db35f9f8295a.js" async=""></script><script src="/_next/static/chunks/874-9f1ca1e6138a33f8.js" async=""></script><script src="/_next/static/chunks/app/layout-3e61b786b068fce4.js" async=""></script><script src="/_next/static/chunks/app/settings/page-761cd65c9b01f5b4.js" async=""></script><title>Cashminder - Smart Money Management</title><meta name="description" content="Take control of your finances with Cashminder&#x27;s intelligent money management tools"/><link rel="icon" href="/favicon.ico"/><title>Cashminder - Personal Finance Manager</title><meta name="description" content="Track your expenses, manage your budget, and achieve your financial goals"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>
            // Block Sentry requests to prevent console errors
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
              if (url && typeof url === 'string' && url.includes('sentry')) {
                console.log('Blocked Sentry request:', url);
                return Promise.resolve(new Response('', { status: 200 }));
              }
              return originalFetch.apply(this, arguments);
            };
          </script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="font-inter antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300"><div class="flex flex-col min-h-screen"><div class="flex-grow"><nav class="fixed top-0 left-0 right-0 z-50 bg-transparent transition-all duration-300" style="transform:translateY(-100px)"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex items-center"><a class="flex-shrink-0 flex items-center" href="/"><div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center mr-2 shadow-md" tabindex="0"><span class="text-dark-bg font-bold text-xl">C</span></div><span class="font-bold text-xl text-light-text-primary dark:text-dark-text-primary">Cashminder</span></a><div class="hidden md:ml-6 md:flex md:space-x-4"><a class="relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent" href="/"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg><span>Home</span></a><a class="relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent" href="/#features"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path></svg><span>Features</span></a><a class="relative px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-1 transition-all duration-200 text-light-text-secondary dark:text-dark-text-secondary hover:text-primary dark:hover:text-primary hover:bg-light-accent dark:hover:bg-dark-accent" href="/pricing"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg><span>Pricing</span></a></div></div><div class="flex items-center space-x-4"><div class="w-12 h-12"></div><a href="/auth"><button class="flex items-center space-x-2 px-4 py-2 rounded-full bg-primary hover:bg-primary-400 text-dark-bg transition-all duration-200 shadow-md" tabindex="0"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg><span class="text-sm font-medium">Login</span></button></a><div class="flex md:hidden"><button class="inline-flex items-center justify-center p-2 rounded-md text-light-text-secondary hover:text-light-text-primary hover:bg-light-border" tabindex="0"><span class="sr-only">Open main menu</span><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="block h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg></button></div></div></div></div></nav><div class="h-16"></div><main class="py-10"><div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8"><div class="flex items-center justify-center min-h-screen"><div class="text-xl text-light-text-secondary"><svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-primary inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading settings...</div></div></div></main></div></div><script src="/_next/static/chunks/webpack-0b26fac893bca99f.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7400,[\"844\",\"static/chunks/ee560e2c-e9c09e02cef8864c.js\",\"493\",\"static/chunks/493-1cf8f8ab3d5926af.js\",\"384\",\"static/chunks/384-c168db35f9f8295a.js\",\"874\",\"static/chunks/874-9f1ca1e6138a33f8.js\",\"177\",\"static/chunks/app/layout-3e61b786b068fce4.js\"],\"default\"]\n3:I[3979,[\"844\",\"static/chunks/ee560e2c-e9c09e02cef8864c.js\",\"493\",\"static/chunks/493-1cf8f8ab3d5926af.js\",\"384\",\"static/chunks/384-c168db35f9f8295a.js\",\"874\",\"static/chunks/874-9f1ca1e6138a33f8.js\",\"177\",\"static/chunks/app/layout-3e61b786b068fce4.js\"],\"default\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n6:I[894,[],\"ClientPageRoot\"]\n7:I[2718,[\"844\",\"static/chunks/ee560e2c-e9c09e02cef8864c.js\",\"384\",\"static/chunks/384-c168db35f9f8295a.js\",\"874\",\"static/chunks/874-9f1ca1e6138a33f8.js\",\"662\",\"static/chunks/app/settings/page-761cd65c9b01f5b4.js\"],\"default\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[9665,[],\"MetadataBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/b412c1caed99ddf9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"Ptdb-hYlZSSvVWk1MKald\",\"p\":\"\",\"c\":[\"\",\"settings\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"settings\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b412c1caed99ddf9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"title\",null,{\"children\":\"Cashminder - Smart Money Management\"}],[\"$\",\"meta\",null,{\"name\":\"description\",\"content\":\"Take control of your finances with Cashminder's intelligent money management tools\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n            // Block Sentry requests to prevent console errors\\n            const originalFetch = window.fetch;\\n            window.fetch = function(url, options) {\\n              if (url \u0026\u0026 typeof url === 'string' \u0026\u0026 url.includes('sentry')) {\\n                console.log('Blocked Sentry request:', url);\\n                return Promise.resolve(new Response('', { status: 200 }));\\n              }\\n              return originalFetch.apply(this, arguments);\\n            };\\n          \"}}]]}],[\"$\",\"body\",null,{\"className\":\"font-inter antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col min-h-screen\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex-grow\",\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"main\",null,{\"className\":\"py-10\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-4 mx-auto max-w-7xl sm:px-6 lg:px-8\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]}]}]}]]}]]}],{\"children\":[\"settings\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L6\",null,{\"Component\":\"$7\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],\"$undefined\",null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",null]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"5USn_nFK-8XgtdI22Y4xE\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],null]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"8:{}\n9:{}\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"c:null\n10:[[\"$\",\"title\",\"0\",{\"children\":\"Cashminder - Personal Finance Manager\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Track your expenses, manage your budget, and achieve your financial goals\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]]\n"])</script></body></html>