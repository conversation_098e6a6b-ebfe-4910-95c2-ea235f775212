(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_a1cbcd7e._.js", {

"[project]/src/lib/analytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateAnalytics": (()=>calculateAnalytics),
    "calculatePercentageChange": (()=>calculatePercentageChange),
    "filterTransactionsByDateRange": (()=>filterTransactionsByDateRange),
    "generateInsights": (()=>generateInsights),
    "getCategoryData": (()=>getCategoryData),
    "getMonthlyData": (()=>getMonthlyData),
    "getPreviousPeriodTransactions": (()=>getPreviousPeriodTransactions),
    "getTimeRanges": (()=>getTimeRanges)
});
'use client';
function getTimeRanges() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    // Last 7 days
    const last7Start = new Date(today);
    last7Start.setDate(today.getDate() - 6);
    // Last 30 days
    const last30Start = new Date(today);
    last30Start.setDate(today.getDate() - 29);
    // Last 3 months
    const last3MonthsStart = new Date(today);
    last3MonthsStart.setMonth(today.getMonth() - 3);
    last3MonthsStart.setDate(1);
    // Last 6 months
    const last6MonthsStart = new Date(today);
    last6MonthsStart.setMonth(today.getMonth() - 6);
    last6MonthsStart.setDate(1);
    // Year to date
    const yearToDateStart = new Date(today.getFullYear(), 0, 1);
    return {
        'last7days': {
            startDate: last7Start,
            endDate: today,
            label: 'Last 7 days'
        },
        'last30days': {
            startDate: last30Start,
            endDate: today,
            label: 'Last 30 days'
        },
        'last3months': {
            startDate: last3MonthsStart,
            endDate: today,
            label: 'Last 3 months'
        },
        'last6months': {
            startDate: last6MonthsStart,
            endDate: today,
            label: 'Last 6 months'
        },
        'yearToDate': {
            startDate: yearToDateStart,
            endDate: today,
            label: 'Year to date'
        }
    };
}
function filterTransactionsByDateRange(transactions, startDate, endDate) {
    return transactions.filter((transaction)=>{
        const transactionDate = new Date(transaction.date);
        return transactionDate >= startDate && transactionDate <= endDate;
    });
}
function getPreviousPeriodTransactions(transactions, currentStartDate, currentEndDate) {
    const periodLength = currentEndDate.getTime() - currentStartDate.getTime();
    const previousStartDate = new Date(currentStartDate.getTime() - periodLength);
    const previousEndDate = new Date(currentEndDate.getTime() - periodLength);
    return filterTransactionsByDateRange(transactions, previousStartDate, previousEndDate);
}
function calculatePercentageChange(current, previous) {
    if (previous === 0) return current > 0 ? 100 : 0;
    return (current - previous) / previous * 100;
}
function getMonthlyData(transactions, months = 6) {
    const result = [];
    const now = new Date();
    // Initialize with zero values for the last 'months' months
    for(let i = months - 1; i >= 0; i--){
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        result.push({
            month: monthDate.toLocaleString('default', {
                month: 'short'
            }),
            income: 0,
            expenses: 0
        });
    }
    // Fill in actual data
    transactions.forEach((transaction)=>{
        const transactionDate = new Date(transaction.date);
        const monthDiff = (now.getFullYear() - transactionDate.getFullYear()) * 12 + now.getMonth() - transactionDate.getMonth();
        if (monthDiff >= 0 && monthDiff < months) {
            const index = months - 1 - monthDiff;
            if (transaction.is_income) {
                result[index].income += transaction.amount;
            } else {
                result[index].expenses += transaction.amount;
            }
        }
    });
    return result;
}
function getCategoryData(transactions, categories) {
    // Filter to only expense transactions
    const expenseTransactions = transactions.filter((t)=>!t.is_income);
    // Group by category and sum amounts
    const categoryAmounts = {};
    expenseTransactions.forEach((transaction)=>{
        if (!categoryAmounts[transaction.category_id]) {
            categoryAmounts[transaction.category_id] = 0;
        }
        categoryAmounts[transaction.category_id] += transaction.amount;
    });
    // Convert to required format
    const result = Object.entries(categoryAmounts).map(([categoryId, amount])=>{
        const category = categories.find((c)=>c.id === categoryId);
        return {
            category: category ? category.name : 'Unknown',
            amount,
            color: category?.color || '#6366f1'
        };
    });
    // Sort by amount (descending)
    return result.sort((a, b)=>b.amount - a.amount);
}
function generateInsights(currentPeriodData, previousPeriodData) {
    const insights = [];
    // Savings rate insight
    const savingsRate = currentPeriodData.totalIncome > 0 ? currentPeriodData.netSavings / currentPeriodData.totalIncome * 100 : 0;
    if (savingsRate > 20) {
        insights.push({
            title: `You saved ${savingsRate.toFixed(0)}% of your income this period`,
            description: 'Great job! You\'re saving a significant portion of your income.',
            type: 'positive'
        });
    } else if (savingsRate > 0) {
        insights.push({
            title: `You saved ${savingsRate.toFixed(0)}% of your income this period`,
            description: 'Consider setting a goal to save at least 20% of your income.',
            type: 'neutral'
        });
    } else if (currentPeriodData.totalIncome > 0) {
        insights.push({
            title: 'Your expenses exceeded your income this period',
            description: 'Try to reduce expenses or increase income to avoid depleting your savings.',
            type: 'negative'
        });
    }
    // Category spending insights
    if (currentPeriodData.categoryData.length > 0 && previousPeriodData.categoryData.length > 0) {
        // Find the category with the biggest increase
        const currentCategoryMap = new Map(currentPeriodData.categoryData.map((item)=>[
                item.category,
                item.amount
            ]));
        const previousCategoryMap = new Map(previousPeriodData.categoryData.map((item)=>[
                item.category,
                item.amount
            ]));
        let biggestIncrease = {
            category: '',
            change: 0,
            percentage: 0
        };
        currentPeriodData.categoryData.forEach(({ category, amount })=>{
            const previousAmount = previousCategoryMap.get(category) || 0;
            if (previousAmount > 0) {
                const change = amount - previousAmount;
                const percentage = change / previousAmount * 100;
                if (percentage > 15 && change > biggestIncrease.change) {
                    biggestIncrease = {
                        category,
                        change,
                        percentage
                    };
                }
            }
        });
        if (biggestIncrease.category) {
            insights.push({
                title: `Spending in ${biggestIncrease.category} increased by ${biggestIncrease.percentage.toFixed(0)}%`,
                description: `Your spending on ${biggestIncrease.category} has increased compared to last period. Consider reviewing these expenses.`,
                type: 'negative'
            });
        }
        // Top spending category
        if (currentPeriodData.categoryData.length > 0) {
            const topCategory = currentPeriodData.categoryData[0];
            const topCategoryPercentage = topCategory.amount / currentPeriodData.totalExpenses * 100;
            insights.push({
                title: `${topCategory.category} is your top spending category (${topCategoryPercentage.toFixed(0)}%)`,
                description: `You spent $${topCategory.amount.toFixed(2)} on ${topCategory.category} this period.`,
                type: 'neutral'
            });
        }
    }
    // Income trend insight
    const incomeChange = calculatePercentageChange(currentPeriodData.totalIncome, previousPeriodData.totalIncome);
    if (Math.abs(incomeChange) > 10) {
        insights.push({
            title: `Your income ${incomeChange > 0 ? 'increased' : 'decreased'} by ${Math.abs(incomeChange).toFixed(0)}%`,
            description: incomeChange > 0 ? 'Great job increasing your income! Consider allocating some of this increase to savings.' : 'Your income has decreased. You might need to adjust your budget accordingly.',
            type: incomeChange > 0 ? 'positive' : 'negative'
        });
    }
    return insights;
}
function calculateAnalytics(transactions, categories, timeRange) {
    // Filter transactions by date range
    const currentPeriodTransactions = filterTransactionsByDateRange(transactions, timeRange.startDate, timeRange.endDate);
    // Get previous period transactions
    const previousPeriodTransactions = getPreviousPeriodTransactions(transactions, timeRange.startDate, timeRange.endDate);
    // Calculate current period totals
    const currentIncome = currentPeriodTransactions.filter((t)=>t.is_income).reduce((sum, t)=>sum + t.amount, 0);
    const currentExpenses = currentPeriodTransactions.filter((t)=>!t.is_income).reduce((sum, t)=>sum + t.amount, 0);
    const currentNetSavings = currentIncome - currentExpenses;
    // Calculate previous period totals
    const previousIncome = previousPeriodTransactions.filter((t)=>t.is_income).reduce((sum, t)=>sum + t.amount, 0);
    const previousExpenses = previousPeriodTransactions.filter((t)=>!t.is_income).reduce((sum, t)=>sum + t.amount, 0);
    const previousNetSavings = previousIncome - previousExpenses;
    // Calculate percentage changes
    const incomeChange = calculatePercentageChange(currentIncome, previousIncome);
    const expenseChange = calculatePercentageChange(currentExpenses, previousExpenses);
    const savingsChange = calculatePercentageChange(currentNetSavings, previousNetSavings);
    // Get monthly data
    const monthlyData = getMonthlyData(transactions);
    // Get category data
    const categoryData = getCategoryData(currentPeriodTransactions, categories);
    // Generate insights
    const insights = generateInsights({
        totalIncome: currentIncome,
        totalExpenses: currentExpenses,
        netSavings: currentNetSavings,
        categoryData
    }, {
        totalIncome: previousIncome,
        totalExpenses: previousExpenses,
        netSavings: previousNetSavings,
        categoryData: getCategoryData(previousPeriodTransactions, categories)
    });
    return {
        totalIncome: currentIncome,
        totalExpenses: currentExpenses,
        netSavings: currentNetSavings,
        incomeChange,
        expenseChange,
        savingsChange,
        monthlyData,
        categoryData,
        insights
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/eventBus.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "emitEvent": (()=>emitEvent),
    "listenEvent": (()=>listenEvent),
    "refreshTransactions": (()=>refreshTransactions)
});
'use client';
// Custom event name for our application
const EVENT_NAME = 'cashminder_event';
function emitEvent(type, data = {}) {
    // Create a custom event with our data
    const event = new CustomEvent(EVENT_NAME, {
        detail: {
            type,
            data,
            timestamp: new Date().toISOString()
        }
    });
    // Dispatch the event on the window object
    window.dispatchEvent(event);
    console.log(`Event emitted: ${type}`, data);
}
function listenEvent(type, callback) {
    const handler = (event)=>{
        const customEvent = event;
        if (customEvent.detail && customEvent.detail.type === type) {
            callback(customEvent.detail.data);
        }
    };
    // Add event listener
    window.addEventListener(EVENT_NAME, handler);
    // Return a function to remove the listener
    return ()=>{
        window.removeEventListener(EVENT_NAME, handler);
    };
}
function refreshTransactions(userId) {
    emitEvent('transactions_changed', {
        userId
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/analytics/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AnalyticsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$ThemeContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/context/ThemeContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analytics.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$eventBus$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/eventBus.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
// Default categories (same as in transactions page)
const defaultCategories = [
    {
        id: '1',
        name: 'Housing',
        color: '#4F46E5',
        is_income: false,
        is_default: true
    },
    {
        id: '2',
        name: 'Food',
        color: '#10B981',
        is_income: false,
        is_default: true
    },
    {
        id: '3',
        name: 'Transportation',
        color: '#F59E0B',
        is_income: false,
        is_default: true
    },
    {
        id: '4',
        name: 'Entertainment',
        color: '#EC4899',
        is_income: false,
        is_default: true
    },
    {
        id: '5',
        name: 'Utilities',
        color: '#6366F1',
        is_income: false,
        is_default: true
    },
    {
        id: '6',
        name: 'Salary',
        color: '#34D399',
        is_income: true,
        is_default: true
    }
];
function AnalyticsPage() {
    _s();
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$ThemeContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const isDark = theme === 'dark';
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [transactions, setTransactions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultCategories);
    const [timeRanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTimeRanges"])());
    const [selectedTimeRange, setSelectedTimeRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('last3months');
    const [analytics, setAnalytics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Function to load transactions and categories
    const loadTransactionsAndCategories = ()=>{
        // Check if user is logged in
        const userJson = localStorage.getItem('cashminder_user');
        if (!userJson) {
            console.log('No user found in localStorage, redirecting to auth');
            router.push('/auth');
            return;
        }
        try {
            // Parse user data
            const userData = JSON.parse(userJson);
            const userId = userData.id || 'default';
            console.log('Loading transactions for user:', userId);
            // Load transactions from localStorage
            const storedTransactions = localStorage.getItem(`cashminder_transactions_${userId}`);
            if (storedTransactions) {
                const parsedTransactions = JSON.parse(storedTransactions);
                console.log('Loaded transactions from localStorage:', parsedTransactions.length);
                setTransactions(parsedTransactions);
            } else {
                console.log('No transactions found in localStorage');
                setTransactions([]);
            }
            // Load categories from localStorage or use defaults
            const storedCategories = localStorage.getItem(`cashminder_categories_${userId}`);
            if (storedCategories) {
                const parsedCategories = JSON.parse(storedCategories);
                console.log('Loaded categories from localStorage:', parsedCategories.length);
                setCategories(parsedCategories);
            } else {
                console.log('Using default categories');
            }
            setIsLoading(false);
        } catch (error) {
            console.error('Error loading user data:', error);
            setIsLoading(false);
        }
    };
    // Initial load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsPage.useEffect": ()=>{
            loadTransactionsAndCategories();
        }
    }["AnalyticsPage.useEffect"], [
        router
    ]);
    // Listen for transaction changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsPage.useEffect": ()=>{
            // Get user ID
            const userJson = localStorage.getItem('cashminder_user');
            if (!userJson) return;
            const userData = JSON.parse(userJson);
            const userId = userData.id || 'default';
            // Set up event listeners for all transaction events
            const removeCreatedListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$eventBus$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["listenEvent"])('transaction_created', {
                "AnalyticsPage.useEffect.removeCreatedListener": (data)=>{
                    if (data.userId === userId) {
                        console.log('Analytics detected new transaction:', data);
                        loadTransactionsAndCategories();
                    }
                }
            }["AnalyticsPage.useEffect.removeCreatedListener"]);
            const removeUpdatedListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$eventBus$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["listenEvent"])('transaction_updated', {
                "AnalyticsPage.useEffect.removeUpdatedListener": (data)=>{
                    if (data.userId === userId) {
                        console.log('Analytics detected updated transaction:', data);
                        loadTransactionsAndCategories();
                    }
                }
            }["AnalyticsPage.useEffect.removeUpdatedListener"]);
            const removeDeletedListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$eventBus$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["listenEvent"])('transaction_deleted', {
                "AnalyticsPage.useEffect.removeDeletedListener": (data)=>{
                    if (data.userId === userId) {
                        console.log('Analytics detected deleted transaction:', data);
                        loadTransactionsAndCategories();
                    }
                }
            }["AnalyticsPage.useEffect.removeDeletedListener"]);
            const removeChangedListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$eventBus$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["listenEvent"])('transactions_changed', {
                "AnalyticsPage.useEffect.removeChangedListener": (data)=>{
                    if (data.userId === userId) {
                        console.log('Analytics detected transactions changed');
                        loadTransactionsAndCategories();
                    }
                }
            }["AnalyticsPage.useEffect.removeChangedListener"]);
            // Clean up event listeners on unmount
            return ({
                "AnalyticsPage.useEffect": ()=>{
                    removeCreatedListener();
                    removeUpdatedListener();
                    removeDeletedListener();
                    removeChangedListener();
                }
            })["AnalyticsPage.useEffect"];
        }
    }["AnalyticsPage.useEffect"], []);
    // Calculate analytics when transactions, categories, or time range changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsPage.useEffect": ()=>{
            if (transactions.length === 0) {
                // If no transactions, set empty analytics
                setAnalytics({
                    totalIncome: 0,
                    totalExpenses: 0,
                    netSavings: 0,
                    incomeChange: 0,
                    expenseChange: 0,
                    savingsChange: 0,
                    monthlyData: getLastSixMonths(),
                    categoryData: [],
                    insights: []
                });
                return;
            }
            const timeRange = timeRanges[selectedTimeRange];
            const analyticsData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateAnalytics"])(transactions, categories, timeRange);
            setAnalytics(analyticsData);
        }
    }["AnalyticsPage.useEffect"], [
        transactions,
        categories,
        selectedTimeRange,
        timeRanges
    ]);
    // Animation variants
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };
    const itemVariants = {
        hidden: {
            y: 20,
            opacity: 0
        },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: 'spring',
                stiffness: 100,
                damping: 12
            }
        }
    };
    // Helper function to get last six months with zero values
    function getLastSixMonths() {
        const result = [];
        const now = new Date();
        for(let i = 5; i >= 0; i--){
            const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
            result.push({
                month: monthDate.toLocaleString('default', {
                    month: 'short'
                }),
                income: 0,
                expenses: 0
            });
        }
        return result;
    }
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-xl ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "animate-spin -ml-1 mr-3 h-8 w-8 text-primary-500 inline-block",
                        xmlns: "http://www.w3.org/2000/svg",
                        fill: "none",
                        viewBox: "0 0 24 24",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                className: "opacity-25",
                                cx: "12",
                                cy: "12",
                                r: "10",
                                stroke: "currentColor",
                                strokeWidth: "4"
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                className: "opacity-75",
                                fill: "currentColor",
                                d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, this),
                    "Loading analytics..."
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 204,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/analytics/page.tsx",
            lineNumber: 203,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: "container mx-auto px-4 py-8 max-w-7xl",
        initial: "hidden",
        animate: "visible",
        variants: containerVariants,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "pb-5 border-b border-light-border dark:border-dark-border mb-8",
                variants: itemVariants,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: `text-3xl font-bold leading-tight ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                        children: "Financial Analytics"
                    }, void 0, false, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 226,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: `mt-1 text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                        children: "Gain insights into your spending patterns and financial health"
                    }, void 0, false, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 229,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 222,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "flex flex-wrap gap-2 mb-8",
                variants: itemVariants,
                children: Object.entries(timeRanges).map(([key, range], index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>setSelectedTimeRange(key),
                        className: `px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2 transition-all duration-200 ${key === selectedTimeRange ? `${isDark ? 'bg-primary/30 border border-primary/50 text-dark-text-primary' : 'bg-primary/10 border border-primary/20 text-primary'}` : `${isDark ? 'border border-dark-border text-dark-text-secondary hover:text-dark-text-primary hover:bg-dark-surface' : 'border border-light-border text-light-text-secondary hover:text-light-text-primary hover:bg-light-border/50'}`}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: range.label
                        }, void 0, false, {
                            fileName: "[project]/src/app/analytics/page.tsx",
                            lineNumber: 253,
                            columnNumber: 13
                        }, this)
                    }, key, false, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 240,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 235,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",
                variants: itemVariants,
                children: [
                    {
                        title: 'Total Income',
                        value: analytics ? `$${analytics.totalIncome.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })}` : '$0.00',
                        change: analytics ? `${analytics.incomeChange > 0 ? '+' : ''}${analytics.incomeChange.toFixed(1)}%` : '0%',
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiArrowUp"], {
                            className: "text-success-light dark:text-success-dark"
                        }, void 0, false, {
                            fileName: "[project]/src/app/analytics/page.tsx",
                            lineNumber: 268,
                            columnNumber: 19
                        }, this),
                        trend: analytics && analytics.incomeChange > 0 ? 'up' : 'down',
                        isExpense: false
                    },
                    {
                        title: 'Total Expenses',
                        value: analytics ? `$${analytics.totalExpenses.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })}` : '$0.00',
                        change: analytics ? `${analytics.expenseChange > 0 ? '+' : ''}${analytics.expenseChange.toFixed(1)}%` : '0%',
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiArrowDown"], {
                            className: "text-error-light dark:text-error-dark"
                        }, void 0, false, {
                            fileName: "[project]/src/app/analytics/page.tsx",
                            lineNumber: 276,
                            columnNumber: 19
                        }, this),
                        trend: analytics && analytics.expenseChange > 0 ? 'up' : 'down',
                        isExpense: true
                    },
                    {
                        title: 'Net Savings',
                        value: analytics ? `$${analytics.netSavings.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })}` : '$0.00',
                        change: analytics ? `${analytics.savingsChange > 0 ? '+' : ''}${analytics.savingsChange.toFixed(1)}%` : '0%',
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrendingUp"], {
                            className: "text-primary"
                        }, void 0, false, {
                            fileName: "[project]/src/app/analytics/page.tsx",
                            lineNumber: 284,
                            columnNumber: 19
                        }, this),
                        trend: analytics && analytics.savingsChange > 0 ? 'up' : 'down',
                        isExpense: false
                    }
                ].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: `p-6 rounded-xl border ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border shadow-sm'}`,
                        whileHover: {
                            y: -5,
                            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)"
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-medium ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                        children: item.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 299,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `p-2 rounded-full ${item.title === 'Total Income' ? 'bg-success-light/10 dark:bg-success-dark/10' : item.title === 'Total Expenses' ? 'bg-error-light/10 dark:bg-error-dark/10' : 'bg-primary/10 dark:bg-primary/20'}`,
                                        children: item.icon
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 302,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 298,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-2xl font-bold mb-2 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                children: item.value
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 312,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `flex items-center text-sm ${item.trend === 'up' ? item.isExpense ? 'text-error-light dark:text-error-dark' : 'text-success-light dark:text-success-dark' : item.trend === 'down' ? item.isExpense ? 'text-success-light dark:text-success-dark' : 'text-error-light dark:text-error-dark' : 'text-light-text-muted dark:text-dark-text-muted'}`,
                                children: [
                                    item.trend === 'up' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrendingUp"], {
                                        className: "mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 327,
                                        columnNumber: 19
                                    }, this) : item.trend === 'down' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiArrowDown"], {
                                        className: "mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 329,
                                        columnNumber: 21
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrendingUp"], {
                                        className: "mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 330,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            item.change,
                                            " from previous period"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 332,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 315,
                                columnNumber: 13
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 289,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 259,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: `p-6 rounded-xl border ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border shadow-sm'}`,
                        variants: itemVariants,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                        children: "Income vs Expenses"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 350,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiBarChart2"], {
                                        className: `w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 353,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 349,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-64 flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full h-full flex items-end justify-between",
                                    children: analytics && analytics.monthlyData.map((data, index)=>{
                                        // Calculate the maximum value for scaling
                                        const maxValue = analytics.monthlyData.reduce((max, item)=>{
                                            const itemMax = Math.max(item.income, item.expenses);
                                            return itemMax > max ? itemMax : max;
                                        }, 1); // Minimum 1 to avoid division by zero
                                        // Scale factor - max height is 180px
                                        const scaleFactor = maxValue > 0 ? 180 / maxValue : 0;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col items-center w-1/6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-full flex justify-center space-x-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                            className: "w-5 bg-primary rounded-t",
                                                            initial: {
                                                                height: 0
                                                            },
                                                            animate: {
                                                                height: `${Math.max(data.income * scaleFactor, 0)}px`
                                                            },
                                                            transition: {
                                                                duration: 0.8,
                                                                delay: index * 0.1
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/analytics/page.tsx",
                                                            lineNumber: 372,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                            className: "w-5 bg-secondary rounded-t",
                                                            initial: {
                                                                height: 0
                                                            },
                                                            animate: {
                                                                height: `${Math.max(data.expenses * scaleFactor, 0)}px`
                                                            },
                                                            transition: {
                                                                duration: 0.8,
                                                                delay: index * 0.1 + 0.2
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/analytics/page.tsx",
                                                            lineNumber: 378,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/analytics/page.tsx",
                                                    lineNumber: 371,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `text-xs mt-2 ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`,
                                                    children: data.month
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/analytics/page.tsx",
                                                    lineNumber: 385,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/src/app/analytics/page.tsx",
                                            lineNumber: 370,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/app/analytics/page.tsx",
                                    lineNumber: 358,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 357,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-center mt-4 space-x-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-3 h-3 bg-primary rounded-full mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 396,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                                children: "Income"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 397,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 395,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-3 h-3 bg-secondary rounded-full mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 400,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                                children: "Expenses"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 401,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 399,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 394,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 341,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: `p-6 rounded-xl border ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border shadow-sm'}`,
                        variants: itemVariants,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                        children: "Expense Breakdown"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 416,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiPieChart"], {
                                        className: `w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 419,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 415,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative h-64 flex items-center justify-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-32 h-32 rounded-full border-8 border-gray-200 dark:border-gray-700"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 425,
                                                columnNumber: 15
                                            }, this),
                                            analytics && analytics.categoryData.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    analytics.categoryData.map((category, index)=>{
                                                        // Calculate total expenses
                                                        const totalExpenses = analytics.categoryData.reduce((sum, cat)=>sum + cat.amount, 0);
                                                        // Calculate degrees for this category (out of 360)
                                                        const degrees = totalExpenses > 0 ? category.amount / totalExpenses * 360 : 0;
                                                        // Calculate rotation offset based on previous categories
                                                        const previousCategories = analytics.categoryData.slice(0, index);
                                                        const previousDegrees = previousCategories.reduce((sum, cat)=>{
                                                            return sum + (totalExpenses > 0 ? cat.amount / totalExpenses * 360 : 0);
                                                        }, 0);
                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                            className: "absolute w-32 h-32 rounded-full",
                                                            style: {
                                                                background: `conic-gradient(${category.color} 0deg, ${category.color} ${degrees}deg, transparent ${degrees}deg)`,
                                                                transform: `rotate(${previousDegrees}deg)`
                                                            },
                                                            initial: {
                                                                opacity: 0
                                                            },
                                                            animate: {
                                                                opacity: 1
                                                            },
                                                            transition: {
                                                                duration: 0.5,
                                                                delay: index * 0.1
                                                            }
                                                        }, index, false, {
                                                            fileName: "[project]/src/app/analytics/page.tsx",
                                                            lineNumber: 443,
                                                            columnNumber: 23
                                                        }, this);
                                                    }),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `absolute text-lg font-bold ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                                        children: [
                                                            "$",
                                                            analytics.totalExpenses.toFixed(0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/analytics/page.tsx",
                                                        lineNumber: 457,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `absolute text-base ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                                children: "No expense data"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 462,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 424,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: analytics && analytics.categoryData.length > 0 ? analytics.categoryData.map((category, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                className: "flex items-center justify-between",
                                                initial: {
                                                    opacity: 0,
                                                    x: 20
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    x: 0
                                                },
                                                transition: {
                                                    duration: 0.3,
                                                    delay: index * 0.1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-3 h-3 rounded-full mr-2",
                                                                style: {
                                                                    backgroundColor: category.color
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                                lineNumber: 479,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                                                children: category.category
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                                lineNumber: 480,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/analytics/page.tsx",
                                                        lineNumber: 478,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: `text-sm font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                                        children: [
                                                            "$",
                                                            category.amount.toFixed(2)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/analytics/page.tsx",
                                                        lineNumber: 484,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 471,
                                                columnNumber: 19
                                            }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `text-sm ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                                            children: "Add expense transactions to see your spending breakdown"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/analytics/page.tsx",
                                            lineNumber: 490,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/analytics/page.tsx",
                                        lineNumber: 468,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 423,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 407,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 339,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: `p-6 rounded-xl border ${isDark ? 'bg-dark-surface border-dark-border' : 'bg-light-surface border-light-border shadow-sm'} mb-8`,
                variants: itemVariants,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: `text-lg font-medium ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                children: "Financial Insights"
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 509,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrendingUp"], {
                                className: `w-5 h-5 ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`
                            }, void 0, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 512,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 508,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: analytics && analytics.insights.length > 0 ? analytics.insights.map((insight, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                className: `p-4 rounded-lg border ${isDark ? 'border-dark-border bg-dark-surface/50' : 'border-light-border bg-light-border/10'}`,
                                initial: {
                                    opacity: 0,
                                    y: 10
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                transition: {
                                    duration: 0.3,
                                    delay: index * 0.1
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-start",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `p-2 rounded-full ${isDark ? 'bg-dark-border' : 'bg-light-border/50'} mr-3 mt-1`,
                                            children: insight.type === 'positive' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrendingUp"], {
                                                className: "text-success-light dark:text-success-dark"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 534,
                                                columnNumber: 23
                                            }, this) : insight.type === 'negative' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiArrowUp"], {
                                                className: "text-error-light dark:text-error-dark"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 536,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiDollarSign"], {
                                                className: "text-primary"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/analytics/page.tsx",
                                                lineNumber: 538,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/analytics/page.tsx",
                                            lineNumber: 530,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                    className: `text-base font-medium mb-1 ${isDark ? 'text-dark-text-primary' : 'text-light-text-primary'}`,
                                                    children: insight.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/analytics/page.tsx",
                                                    lineNumber: 542,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: `text-sm ${isDark ? 'text-dark-text-tertiary' : 'text-light-text-tertiary'}`,
                                                    children: insight.description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/analytics/page.tsx",
                                                    lineNumber: 545,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/analytics/page.tsx",
                                            lineNumber: 541,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/analytics/page.tsx",
                                    lineNumber: 529,
                                    columnNumber: 17
                                }, this)
                            }, index, false, {
                                fileName: "[project]/src/app/analytics/page.tsx",
                                lineNumber: 518,
                                columnNumber: 15
                            }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `p-8 text-center ${isDark ? 'text-dark-text-secondary' : 'text-light-text-secondary'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiDollarSign"], {
                                    className: "w-12 h-12 mx-auto mb-4 opacity-30"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/analytics/page.tsx",
                                    lineNumber: 554,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-lg font-medium mb-2",
                                    children: "No insights available yet"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/analytics/page.tsx",
                                    lineNumber: 555,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm",
                                    children: "Add more transactions to get personalized financial insights"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/analytics/page.tsx",
                                    lineNumber: 556,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/analytics/page.tsx",
                            lineNumber: 553,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/analytics/page.tsx",
                        lineNumber: 515,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/analytics/page.tsx",
                lineNumber: 500,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/analytics/page.tsx",
        lineNumber: 216,
        columnNumber: 5
    }, this);
}
_s(AnalyticsPage, "xfZLwoKGwEFOxg0UAQwU4yQLhgI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$ThemeContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AnalyticsPage;
var _c;
__turbopack_context__.k.register(_c, "AnalyticsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_a1cbcd7e._.js.map