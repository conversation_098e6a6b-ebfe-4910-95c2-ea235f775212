{"name": "temp-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "bcryptjs": "^3.0.2", "chart.js": "^4.4.8", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.0", "next": "15.2.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}