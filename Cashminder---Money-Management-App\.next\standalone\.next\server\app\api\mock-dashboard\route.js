(()=>{var e={};e.id=19,e.ids=[19],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},92789:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var t={};s.r(t),s.d(t,{GET:()=>d});var a=s(96559),o=s(48088),n=s(37719),i=s(32190);async function d(e){try{let r=e.nextUrl.searchParams.get("userId");if(!r)return i.NextResponse.json({success:!1,error:"User ID is required"},{status:400});return console.log("Serving mock dashboard data for userId:",r),i.NextResponse.json({success:!0,data:{totalBalance:0,income:0,expenses:0,savingsGoal:{current:0,target:1e4,percentage:0},recentTransactions:[]}})}catch(e){return console.error("Error generating mock dashboard data:",e),i.NextResponse.json({success:!1,error:"Failed to generate mock dashboard data",details:e instanceof Error?e.message:String(e)},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/mock-dashboard/route",pathname:"/api/mock-dashboard",filename:"route",bundlePath:"app/api/mock-dashboard/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\mk\\Cashminder---Money-Management-App\\src\\app\\api\\mock-dashboard\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:l}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580],()=>s(92789));module.exports=t})();